import 'dart:async';

import 'package:alink/data/model/part_item_response.dart';
import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';

part 'selected_part_item_event.dart';
part 'selected_part_item_state.dart';

class SelectedPartItemBloc
    extends Bloc<SelectedPartItemEvent, SelectedPartItemState> {
  List<PartItemResponse?> selectedPartList = [];
  SelectedPartItemBloc() : super(SelectedPartItemInitial());

  @override
  Stream<SelectedPartItemState> mapEventToState(
    SelectedPartItemEvent event,
  ) async* {
    if (event is UpdatePartItem) {
      if (event.part == null) {
        selectedPartList.clear();
      } else {
        if (event.isAdd) {
          selectedPartList.add(event.part);
        } else {
          selectedPartList.remove(event.part);
        }
      }

      yield UpdatedPartItem(selectedPartList);
    }
    if (event is RemovePartItem) {
      selectedPartList.remove(event.part);
      yield RemovedPartItem(selectedPartList);
    }
    if (event is FetchSelectedPartItem) {
      yield FetchedSelectedPartItem(selectedPartList);
    }

    if (event is ProPopulateSuggestedPart) {
      selectedPartList = [];
      selectedPartList = [
        ...?event.suggestedParts,
        ...selectedPartList,
      ];
      ;
      yield UpdatedPartItem(selectedPartList);
    }
  }
}
