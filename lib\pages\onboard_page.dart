import 'package:alink/pages/dashboard_page.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:introduction_screen/introduction_screen.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class OnBoardingPage extends StatefulWidget {
  static const routeName = 'onboard';

  const OnBoardingPage({Key? key}) : super(key: key);
  @override
  _OnBoardingPageState createState() => _OnBoardingPageState();
}

class _OnBoardingPageState extends State<OnBoardingPage> {
  final introKey = GlobalKey<IntroductionScreenState>();

  void _onIntroEnd(context) {
    Navigator.pushReplacementNamed(context, DashboardPage.routeName);
  }

  void _skipToDashBoard(context) {
    Navigator.pushReplacementNamed(context, DashboardPage.routeName);
  }

  Widget _buildImage(String assetName, {double width = 350, double? height}) {
    return Image.asset(
      'assets/images/$assetName',
      width: width,
      height: height,
    );
  }

  @override
  Widget build(BuildContext context) {
    const bodyStyle = TextStyle(fontSize: 19.0);

    const pageDecoration = PageDecoration(
      titleTextStyle: TextStyle(fontSize: 28.0, fontWeight: FontWeight.w700),
      bodyTextStyle: bodyStyle,
      //descriptionPadding: EdgeInsets.fromLTRB(16.0, 0.0, 16.0, 16.0),
      pageColor: Colors.white,
      imagePadding: EdgeInsets.zero,
    );

    return Center(
      child: Container(
        decoration: kIsWeb
            ? BoxDecoration(
                border: Border.all(
                  color: Theme.of(context).primaryColorLight,
                ),
                borderRadius: const BorderRadius.all(
                  Radius.circular(10),
                ),
              )
            : const BoxDecoration(),
        constraints: const BoxConstraints(maxWidth: 500),
        child: IntroductionScreen(
          key: introKey,
          globalBackgroundColor: Colors.white,
          globalHeader: Align(
            alignment: Alignment.topRight,
            child: SafeArea(
              child: Padding(
                padding: const EdgeInsets.only(top: 16, right: 16),
                child: _buildImage('logo.png', width: 150),
              ),
            ),
          ),
          globalFooter: SizedBox(
            width: double.infinity,
            height: 60,
            child: Container(
              margin: const EdgeInsets.all(5),
              child: ElevatedButton(
                child: Text(
                  AppLocalizations.of(context)!.gettingStarted,
                  style: const TextStyle(
                      fontSize: 16.0, fontWeight: FontWeight.bold),
                ),
                onPressed: () => _skipToDashBoard(context),
              ),
            ),
          ),
          pages: [
            PageViewModel(
              title: "",
              /*  bodyWidget: Container(
                margin: EdgeInsets.only(top: 50),
                child: _buildImage('alink_square.jpg', height: 260),
              ),
              footer: _getTextTitle(
                  title: AppLocalizations.of(context)!
                      .anInnovativeSoftwareSolutionDescFirstLine),*/
              bodyWidget: Container(
                margin: EdgeInsets.only(top: 100),
                child: Column(
                  children: [
                    _getTextTitle(
                        title: AppLocalizations.of(context)!
                            .anInnovativeSoftwareSolutionDescFirstLine),
                    _buildImage('alink_square.jpg', height: 260),
                  ],
                ),
              ),
              decoration: pageDecoration,
            ),
            PageViewModel(
              title: "",
              bodyWidget: Container(
                margin: EdgeInsets.only(top: 100),
                child: _getTextTitle(
                    title: AppLocalizations.of(context)!
                        .anInnovativeSoftwareSolutionDescSecondLine),
              ),
              decoration: pageDecoration,
            ),
            PageViewModel(
              title: "",
              bodyWidget: Container(
                margin: EdgeInsets.only(top: 100),
                child: Column(
                  children: [
                    _getTextTitle(
                        title: AppLocalizations.of(context)!
                            .onePlatformToPlanYourWork),
                    _buildImage('alink_linear.jpg', height: 260),
                  ],
                ),
              ),
              decoration: pageDecoration,
            ),
            /* PageViewModel(
              title: "AFS Security",
              bodyWidget: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: const [
                  Text("Click on ", style: bodyStyle),
                  Icon(Icons.edit),
                  Text(" to edit a post", style: bodyStyle),
                ],
              ),
              decoration: pageDecoration.copyWith(
                bodyFlex: 2,
                imageFlex: 4,
                bodyAlignment: Alignment.bottomCenter,
                imageAlignment: Alignment.topCenter,
              ),
              image: _buildImage('img1.jpg'),
              reverse: true,
            ),*/
          ],
          onDone: () => _onIntroEnd(context),
          //onSkip: () => _onIntroEnd(context), // You can override onSkip callback
          showSkipButton: true,
          dotsFlex: 0,
          nextFlex: 0,
          //rtl: true, // Display as right-to-left
          skip: Text(AppLocalizations.of(context)!.skip),
          next: const FaIcon(FontAwesomeIcons.arrowRight),
          done: Text(AppLocalizations.of(context)!.done,
              style: const TextStyle(fontWeight: FontWeight.w600)),
          curve: Curves.fastLinearToSlowEaseIn,
          controlsMargin: const EdgeInsets.all(16),
          controlsPadding: kIsWeb
              ? const EdgeInsets.all(12.0)
              : const EdgeInsets.fromLTRB(8.0, 4.0, 8.0, 4.0),
          dotsDecorator: DotsDecorator(
            size: const Size(10.0, 10.0),
            color: const Color(0xFFBDBDBD),
            activeSize: const Size(22.0, 10.0),
            activeColor: Theme.of(context).primaryColor,
            activeShape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.all(Radius.circular(25.0)),
            ),
          ),
          dotsContainerDecorator: const ShapeDecoration(
            color: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.all(Radius.circular(8.0)),
            ),
          ),
        ),
      ),
    );
  }

  _getTextTitle({required String title}) {
    return Text(
      title,
      textAlign: TextAlign.center,
      style: const TextStyle(
        fontSize: 20.0,
      ),
    );
  }
}
