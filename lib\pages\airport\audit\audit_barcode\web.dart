import 'dart:developer';

import 'package:alink/bloc/audit/audit_equipment_cubit.dart';
import 'package:alink/bloc/service/service_request_bloc.dart';
import 'package:alink/util/barcode_html.dart';
import 'package:alink/widget/audit_bar_code_dialog.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:vibration/vibration.dart';
import 'package:webviewx_plus/webviewx_plus.dart';

import '../../../../data/model/barcode_response.dart';
import '../../../../data/repository/api_service.dart';
import '../../../../logger/logger.dart';
import '../../../../util/application_util.dart';
import '../../../../util/enums/app_enum.dart';
import '../../repair/assign_equipment/assign_equipment_page.dart';

///ScannerWidget
class AuditBarcodeScanner extends StatefulWidget {
  bool closeScreen = false;
  static String routeName = 'scanner';

  AuditBarcodeScanner({Key? key}) : super(key: key);
  @override
  State<StatefulWidget> createState() {
    return _AuditBarcodeScannerState();
  }
}

class _AuditBarcodeScannerState extends State<AuditBarcodeScanner> {
  static const String className = '_AuditBarcodeScannerState';
  late FToast fToast;

  bool isFlashOn = false;
  bool showDialog = false;
  WebViewXController? webviewController;
  int prevCount = 0, newCount = 0;
  bool isNotificationState = true;
  @override
  void initState() {
    super.initState();
    fToast = FToast();
    fToast.init(context);
    widget.closeScreen = false;
    _getCount();
  }

  AuditEquipmentCubit get auditEquipmentCubit =>
      BlocProvider.of<AuditEquipmentCubit>(context);
  ServiceRequestBloc get serviceRequestBloc =>
      BlocProvider.of<ServiceRequestBloc>(context);

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (kDebugMode) {
      Logger.i("Class Name: " + className);
    }
    return Container(
      color: Colors.black,
      child: Stack(
        alignment: Alignment.bottomCenter,
        children: [
          !showDialog
              ? WebViewX(
                  width: MediaQuery.of(context).size.width,
                  height: MediaQuery.of(context).size.height,
                  javascriptMode: JavascriptMode.unrestricted,
                  initialContent: barcodeHtmlString,
                  initialSourceType: SourceType.html,
                  onWebViewCreated: (controller) {
                    webviewController = controller;
                    // _loadHtmlFromAssets();
                  },
                  onPageFinished: (src) {},
                  dartCallBacks: {
                    DartCallback(
                      name: 'SubmitCallback',
                      callBack: (msg) async {
                        Vibration.vibrate();
                        await _saveBarcodeDataInDatabase(msg.toString());
                      },
                    ),
                    DartCallback(
                      name: 'CloseCallback',
                      callBack: (msg) {
                        Navigator.pop(context);
                      },
                    )
                  },
                )
              : Container(),
          WebViewAware(
            child: Container(
              padding: const EdgeInsets.only(bottom: 20),
              child: BlocConsumer<ServiceRequestBloc, ServiceRequestState>(
                listener: (ctx, state) {
                  log("log::before FetchedEquipmentFromAuditTable Scanned barcode count->$newCount");
                  if (state is FetchedEquipmentFromAuditTable) {
                    log("log::Scanned barcode count->$newCount");
                    if (newCount <
                        state.equipmentList!
                            .where((element) => element.isScanned == true)
                            .toList()
                            .length) {
                      if (kDebugMode) {
                        print(newCount);
                        print(
                            "+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++");
                        print(state.equipmentList!
                            .where((element) => element.isScanned == true)
                            .toList()
                            .length);
                      }
                      log("log::isNotificationState->$isNotificationState");
                      //can be removed if in case something breaks here
                      _showToast();
                      if (!isNotificationState) {
                        log("log::show success toast msg");
                        //can be added if in case something breaks here
                        //_showToast();
                      } else {
                        isNotificationState = false;
                      }
                      prevCount = newCount;
                      log("log::Scanned barcode count->$newCount");
                    } else if (!(newCount == 0 && prevCount > 0)) {
                      //can be removed if in case something breaks here
                      _showWaringToast();
                      if (!isNotificationState) {
                        log("log::show warning toast msg");
                        //can be added if in case something breaks here
                        //_showWaringToast();
                      } else {
                        isNotificationState = false;
                      }
                    }
                    int count = state.equipmentList!
                        .where((element) => element.isScanned == true)
                        .toList()
                        .length;
                    if (count == state.equipmentList!.length &&
                        widget.closeScreen == false) {
                      if (kDebugMode) {
                        print('pop from widget');
                        print(AuditBarcodeScanner.routeName);
                      }
                      widget.closeScreen = true;
                      closePage();
                    }
                  }
                },
                builder: (context, state) {
                  if (state is FetchedEquipmentFromAuditTable) {
                    int count = state.equipmentList!
                        .where((element) =>
                            element.isScanned == true && element.name != null)
                        .toList()
                        .length;
                    int newEquipment = state.equipmentList!
                        .where((element) => element.name == null)
                        .toList()
                        .length;
                    newCount = state.equipmentList!
                        .where((element) => element.isScanned == true)
                        .toList()
                        .length;
                    prevCount = state.equipmentList!
                        .where((element) =>
                            element.isScanned == true && element.name != null)
                        .toList()
                        .length;
                    return Column(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Text(
                          count > 0
                              ? '${AppLocalizations.of(context)!.equipmentPartOfAudit}: $count'
                                  '/${((state.equipmentList?.length)! - newEquipment).toString()}'
                              : '${AppLocalizations.of(context)!.equipmentPartOfAudit}: 0',
                          style: const TextStyle(
                              fontSize: 18, color: Colors.white),
                        ),
                        const SizedBox(
                          height: 5,
                        ),
                        Text(
                          newEquipment > 0
                              ? '${AppLocalizations.of(context)!.equipmentOutsideAudit}: $newEquipment'
                              : '${AppLocalizations.of(context)!.equipmentOutsideAudit}: 0',
                          style: const TextStyle(
                              fontSize: 18, color: Colors.white),
                        ),
                        const SizedBox(
                          height: 5,
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            _getFlashButton(),
                            const SizedBox(
                              width: 40,
                            ),
                            IconButton(
                              onPressed: () {
                                showDialog = true;
                                setState(() {});
                                showGeneralDialog(
                                  barrierDismissible: false,
                                  context: context,
                                  pageBuilder:
                                      (context, animation, secondaryAnimation) {
                                    return AuditBarCodeDialog(
                                      callback: () {
                                        showDialog = false;
                                        Navigator.pop(context);
                                        setState(() {});
                                      },
                                    );
                                  },
                                );
                              },
                              icon: const Icon(
                                Icons.keyboard_alt,
                                size: 40,
                                color: Colors.white,
                              ),
                            )
                          ],
                        ),
                      ],
                    );
                  }

                  return Column(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Text(
                        '${AppLocalizations.of(context)!.equipmentPartOfAudit}: 0',
                        style:
                            const TextStyle(fontSize: 18, color: Colors.white),
                      ),
                      const SizedBox(
                        height: 5,
                      ),
                      Text(
                        '${AppLocalizations.of(context)!.equipmentOutsideAudit}: 0',
                        style:
                            const TextStyle(fontSize: 18, color: Colors.white),
                      ),
                      const SizedBox(
                        height: 5,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          _getFlashButton(),
                          const SizedBox(
                            width: 40,
                          ),
                          IconButton(
                            onPressed: () {
                              showDialog = true;
                              setState(() {});
                              showGeneralDialog(
                                barrierDismissible: false,
                                context: context,
                                pageBuilder:
                                    (context, animation, secondaryAnimation) {
                                  return AuditBarCodeDialog(
                                    callback: () {
                                      showDialog = false;
                                      Navigator.pop(context);
                                      setState(() {});
                                    },
                                  );
                                },
                              );
                            },
                            icon: const Icon(
                              Icons.keyboard_alt,
                              size: 40,
                              color: Colors.white,
                            ),
                          )
                        ],
                      ),
                    ],
                  );
                },
              ),
            ),
          )
        ],
      ),
    );
  }

  _getFlashButton() {
    if (isFlashOn) {
      return IconButton(
        onPressed: () {
          setState(() {
            isFlashOn = false;
          });
        },
        icon: const Icon(
          Icons.flash_off,
          size: 35,
          color: Colors.white,
        ),
      );
    }
    return IconButton(
      onPressed: () {
        setState(() {
          isFlashOn = true;
        });
      },
      icon: const Icon(
        Icons.flash_on,
        size: 35,
        color: Colors.white,
      ),
    );
  }

  void closePage() {
    Navigator.pop(context);
  }

  Future<void> _saveBarcodeDataInDatabase(barcodeTag) async {
    log("Scanned barcode and saving in database tag->$barcodeTag");
    Navigator.of(context).pop();
    final response =
        await ApiService().getEquipmentDetailByBarcodeNumber(barcodeTag, false);
    if (response is BarcodeResponse) {
      serviceRequestBloc
          .add(DeleteAuditFromTable(auditId: AuditEquipmentCubit.auditId!));
      serviceRequestBloc.add(UpdateBarcodeDataInAudit(
          AuditEquipmentCubit.auditId!, barcodeTag, null, null, null, []));
      return;
    } else if (response == ApiResponse.BAR_CODE_NOT_FOUND) {
      ApplicationUtil.showWarningAlertDialog(
        context,
        title: AppLocalizations.of(context)!.barcodeNotfound,
        desc:
            'This barcode ($barcodeTag) is not mapped to any equipment. Do you want to assign this barcode to new equipment?',
        positiveLabel: AppLocalizations.of(context)!.assign,
        negativeLabel: AppLocalizations.of(context)!.cancel,
        onPositiveClickListener: () async {
          Navigator.of(context).pop();
          final assignedConfirmation = await Navigator.pushNamed(
              context, AssignEquipmentPage.routeName,
              arguments: ServiceType(
                  type: ServiceRequestType.ASSIGN_EQUIPMENT,
                  barcodeNumber: barcodeTag,
                  fromAudit: true,
                  defaultLocationId:
                      AuditEquipmentCubit.auditLocation!['LOCATION_ID']));
          if (assignedConfirmation == 204) {
            await _saveBarcodeDataInDatabase(barcodeTag);
          }
        },
        onNegativeClickListener: () {},
      );
    } else {
      ApplicationUtil.showWarningAlertDialog(context,
          title: AppLocalizations.of(context)!.error,
          desc: AppLocalizations.of(context)!.errorOccurWhileFetching,
          negativeLabel: AppLocalizations.of(context)!.okay);
    }
  }

  _showToast() {
    log("-----------------------------log::Showing success toast-----------------------------");
    Widget toast = Center(
        child: Container(
      width: MediaQuery.of(context).size.width - 50,
      height: 120,
      padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 12.0),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(25.0),
        color: Colors.greenAccent,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(
            FontAwesomeIcons.check,
            size: 50,
            color: Colors.white,
          ),
          const SizedBox(
            width: 12.0,
          ),
          Text(
            AppLocalizations.of(context)!.successfulScan,
            style: const TextStyle(fontSize: 22, color: Colors.white),
          ),
        ],
      ),
    ));

    fToast.showToast(
      child: toast,
      gravity: ToastGravity.TOP,
      toastDuration: const Duration(seconds: 2),
    );
  }

  _showWaringToast() {
    log("-----------------------------log::Showing warning toast-----------------------------");
    Widget toast = Center(
        child: Container(
      width: MediaQuery.of(context).size.width - 50,
      height: 120,
      padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 12.0),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(25.0),
        color: Colors.orangeAccent,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(
            FontAwesomeIcons.exclamationTriangle,
            size: 50,
            color: Colors.white,
          ),
          const SizedBox(
            width: 12.0,
          ),
          Text(
            AppLocalizations.of(context)!.alreadyScanned,
            style: const TextStyle(fontSize: 20, color: Colors.white),
          ),
        ],
      ),
    ));

    fToast.showToast(
      child: toast,
      gravity: ToastGravity.TOP,
      toastDuration: const Duration(seconds: 2),
    );
  }

  void _getCount() {
    for (var value in ServiceRequestBloc.equipmentList) {
      if (value.isScanned == true) {
        newCount = 0;
        prevCount += 1;
      }
    }
  }

  void _loadHtmlFromAssets() {
    webviewController!.loadContent(
      'assets/barcode/barcode.html',
      sourceType: SourceType.html,
      fromAssets: true,
    );
  }
}
