class Tail {
  String tailNo;
  int serviceRequestCount;
  int messageCount;

  Tail({
    required this.tailNo,
    required this.serviceRequestCount,
    required this.messageCount,
  });

  Map<String, dynamic> toMap() {
    return {
      'TailNo': tailNo,
      'ServiceRequestCount': serviceRequestCount,
      'MessageCount': messageCount,
    };
  }

  factory Tail.fromMap(Map<String, dynamic> map) {
    return Tail(
      tailNo: map['TailNo'].toString(),
      serviceRequestCount: map['ServiceRequestCount'] as int,
      messageCount: map['MessageCount'] as int,
    );
  }
}
