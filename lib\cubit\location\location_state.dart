part of 'location_cubit.dart';

@immutable
abstract class LocationState {}

class LocationInitial extends LocationState {}

class FetchingLocationData extends LocationState {}

class FetchedLocationData extends LocationState {
  final List<LocationDetail> locationList;
  FetchedLocationData(this.locationList);
}

class FetchLocationDataError extends LocationState {
  final String errorMessage;
  FetchLocationDataError({required this.errorMessage});
}
