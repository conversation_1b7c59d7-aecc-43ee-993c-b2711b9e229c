import 'package:alink/data/model/location_detail.dart';
import 'package:alink/util/app_color.dart';
import 'package:alink/widget/seperator.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class LocationDropdownWidget extends StatefulWidget {
  final List<LocationDetail> locationList;
  String? defaultLocationId;
  final Function(
          String locationId, String locationName, String locationCategoryName)
      onLocationSelected;
  final Function()? onReset;
  final String title;
  final String description;
  final bool showRequiredSymbol;
  final bool disableDropDown;
  LocationDropdownWidget({
    Key? key,
    required this.locationList,
    this.defaultLocationId,
    required this.onLocationSelected,
    this.onReset()?,
    this.showRequiredSymbol = false,
    this.title = '',
    this.description = '',
    this.disableDropDown = false,
  }) : super(key: key);

  @override
  State<LocationDropdownWidget> createState() => _LocationDropdownWidgetState();
}

class _LocationDropdownWidgetState extends State<LocationDropdownWidget> {
  int dropdownLength = 0;
  List<LocationDropdownModel> dropdownList = [];

  @override
  void initState() {
    super.initState();
    //Set default dropdown if defaultLocationId is not null
    //print(widget.defaultLocationId);
    if (widget.defaultLocationId != null &&
        widget.defaultLocationId!.isNotEmpty) {
      if (dropdownList.isEmpty) {
        LocationDetail? locationDetail = widget.locationList
            .where((element) => widget.defaultLocationId!.contains(element.id))
            .first;
        if (widget.defaultLocationId!.contains(locationDetail.id)) {
          dropdownList.add(LocationDropdownModel(
              dropdownList: widget.locationList,
              selectedValue: locationDetail,
              parentNodeId: 'root'));
        }
        _generateDefaultDropdownList(locationDetail);
      }
    }
  }

  void _generateDefaultDropdownList(LocationDetail locationDetail) {
    if (locationDetail.childrens != null &&
        locationDetail.childrens!.isNotEmpty) {
      late LocationDetail selected;
      if (locationDetail.childrens!
          .where((element) => widget.defaultLocationId!.contains(element.id))
          .isNotEmpty) {
        List<LocationDetail> list = locationDetail.childrens!
            .where((element) => widget.defaultLocationId!.contains(element.id))
            .toList();
        if (list.length == 1) {
          selected = locationDetail.childrens!
              .where(
                  (element) => widget.defaultLocationId!.contains(element.id))
              .first;
        } else {
          selected = locationDetail.childrens!
              .where(
                  (element) => widget.defaultLocationId!.contains(element.id))
              .last;
        }
      } else {
        selected = locationDetail.childrens![0];
      }
      if (widget.defaultLocationId!.contains(selected.id)) {
        var dropdown = LocationDropdownModel(
            dropdownList: locationDetail.childrens!,
            selectedValue: selected,
            parentNodeId: locationDetail.id);
        dropdownList.add(dropdown);
        //onDropdownChanged(dropdown);
        _generateDefaultDropdownList(selected);
      } else {
        dropdownList.add(
          LocationDropdownModel(
              dropdownList: locationDetail.childrens!,
              selectedValue: selected,
              parentNodeId: selected.id),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    //Root Value
    if (dropdownList.isEmpty) {
      if (widget.locationList.length == 2) {
        var dropdown = LocationDropdownModel(
          dropdownList: widget.locationList,
          selectedValue: widget.locationList[1],
          parentNodeId: 'root',
        );
        dropdownList.add(
          dropdown,
        );
        onDropdownChanged(dropdown);
      } else {
        dropdownList.add(
          LocationDropdownModel(
            dropdownList: widget.locationList,
            selectedValue: widget.locationList[0],
            parentNodeId: 'root',
          ),
        );
      }

      //Future.delayed(const Duration(seconds: 1), () {});
    }
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 5),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (widget.title.trim().isNotEmpty) ...[
            widget.showRequiredSymbol
                ? Row(
                    children: [
                      Expanded(
                        child: RichText(
                          textAlign: TextAlign.start,
                          text: TextSpan(
                              style: const TextStyle(fontSize: 18),
                              children: <TextSpan>[
                                const TextSpan(
                                  text: "* ",
                                  style: TextStyle(
                                      color: AppColor.redColor,
                                      fontWeight: FontWeight.bold),
                                ),
                                TextSpan(
                                  text: widget.title,
                                  style: TextStyle(
                                      color: Theme.of(context).primaryColor,
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold),
                                ),
                              ]),
                        ),
                      ),
                    ],
                  )
                : Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Expanded(
                        child: Container(
                          child: Text(
                            widget.title,
                            style: TextStyle(
                                color: Theme.of(context).primaryColor,
                                fontSize: 18,
                                fontWeight: FontWeight.bold),
                          ),
                        ),
                      ),
                      TextButton(
                        onPressed: () {
                          if (widget.onReset != null) {
                            widget.onReset!();
                            _setDefaultLocation(null);
                            setState(() {});
                          } else {
                            _setDefaultLocation(null);
                            setState(() {});
                          }
                        },
                        child: Text(
                          AppLocalizations.of(context)!.reset,
                          style: const TextStyle(
                              fontSize: 16, color: AppColor.redColor),
                        ),
                      )
                    ],
                  ),
            const DottedDivider(
              color: AppColor.redColor,
            ),
            const SizedBox(
              height: 10,
            ),
          ],
          if (widget.description.trim().isNotEmpty) ...[
            const SizedBox(
              height: 10,
            ),
            Text(
              widget.description,
              style: const TextStyle(
                color: AppColor.greyTextColor,
                fontSize: 16,
              ),
            ),
            const SizedBox(
              height: 10,
            ),
          ] else ...[
            const SizedBox(
              height: 10,
            ),
          ],
          ListView.separated(
            padding: EdgeInsets.zero,
            shrinkWrap: true,
            itemBuilder: (context, index) {
              return buildDropdown(dropdownList[index], onDropdownChanged);
            },
            itemCount: dropdownList.length,
            separatorBuilder: (BuildContext context, int index) {
              return const SizedBox(
                height: 10,
              );
            },
          ),
        ],
      ),
    );
  }

  onDropdownChanged(LocationDropdownModel dropdownModel) {
    if (dropdownModel.parentNodeId == 'root') {
      // Root node is changed so retain only the first dropdown
      dropdownList.retainWhere((element) => element.parentNodeId == 'root');
    }
    LocationDetail selectedData = dropdownModel.dropdownList.firstWhere(
        (element) =>
            element.id ==
            dropdownModel
                .selectedValue.id); // Get the data of the selected dropdown
    int indexOfSelectedItem = dropdownList.indexWhere((element) =>
        element.selectedValue ==
        dropdownModel.selectedValue); //Get the index of selected dropdown
    if ((dropdownList.length - 1) > indexOfSelectedItem + 1) {
      for (int i = dropdownList.length - 1; i >= indexOfSelectedItem + 1; i--) {
        dropdownList.removeAt(i);
      }
    } else {
      //Only the last dropdown item to be removed
      if ((dropdownList.length - 1) == (indexOfSelectedItem + 1)) {
        dropdownList.removeAt(indexOfSelectedItem + 1);
      }
    }
    int index = dropdownList.indexWhere((element) =>
        element.parentNodeId ==
        dropdownModel.selectedValue
            .id); //Check if the child dropdown for selectedValue is already exists
    if (selectedData.childrens != null && selectedData.childrens!.isNotEmpty) {
      if (index == -1) {
        dropdownList.add(LocationDropdownModel(
            dropdownList: selectedData.childrens!,
            selectedValue: selectedData.childrens![0],
            parentNodeId: dropdownModel.selectedValue.id));
      } else {}
    }
    //last dropdown is select value use previous drop down value
    LocationDetail locationDetail;
    if (dropdownList.last.selectedValue.id == "-1") {
      if (dropdownList.length >= 2) {
        locationDetail = dropdownList[dropdownList.length - 2].selectedValue;
        widget.onLocationSelected(
            locationDetail.id, locationDetail.name, locationDetail.categoryId);
      } else {
        locationDetail = dropdownList[dropdownList.length - 1].selectedValue;
        widget.onLocationSelected('', '', '');
      }
    } else {
      locationDetail = dropdownList.last.selectedValue;
      widget.onLocationSelected(
          locationDetail.id, locationDetail.name, locationDetail.categoryId);
    }

    setState(() {});
  }

  Widget buildDropdown(LocationDropdownModel item,
      Function(LocationDropdownModel) onChangedCallback) {
    return Container(
      decoration: BoxDecoration(
        color: !widget.disableDropDown
            ? Theme.of(context).primaryColor
            : AppColor.greyBorderColor,
        borderRadius: BorderRadius.circular(10),
      ),
      child: DropdownButton(
        underline: Container(),
        isExpanded: true,
        focusColor: Colors.white,
        value: item.selectedValue,
        icon: const Icon(
          // Add this
          Icons.arrow_drop_down,
          color: Colors.white,
          size: 35, // Add this
        ),
        items: item.dropdownList.map((LocationDetail item) {
          return DropdownMenuItem(
            value: item,
            child: Text(item.name),
          );
        }).toList(),
        selectedItemBuilder: (BuildContext ctx) {
          return item.dropdownList.map<Widget>((LocationDetail item) {
            return DropdownMenuItem(
                child: Container(
                  margin: const EdgeInsets.only(left: 20),
                  child: Text(item.name,
                      style: const TextStyle(
                          color: Colors.white, fontWeight: FontWeight.bold)),
                ),
                value: item);
          }).toList();
        },
        // After selecting the desired option,it will
        // change button value to selected value
        onChanged: !widget.disableDropDown
            ? (newValue) {
                item.selectedValue = newValue as LocationDetail;
                int index = dropdownList.indexWhere(
                    (element) => element.selectedValue == item.selectedValue);
                dropdownList[index] = item;
                onChangedCallback(item);
              }
            : null,
      ),
    );
  }

  _setDefaultLocation(String? defaultLocationId) {
    //print(defaultLocationId);
    dropdownList.clear();
    if (defaultLocationId != null && defaultLocationId.isNotEmpty) {
      if (dropdownList.isEmpty) {
        LocationDetail? locationDetail = widget.locationList
            .where((element) => defaultLocationId.contains(element.id))
            .first;
        if (defaultLocationId.contains(locationDetail.id)) {
          dropdownList.add(LocationDropdownModel(
              dropdownList: widget.locationList,
              selectedValue: locationDetail,
              parentNodeId: 'root'));
        }
        _generateDefaultDropdownList(locationDetail);
      }
    }
  }
}
