import 'package:alink/scanner/barcode/mobile.dart'
    if (dart.library.html) 'package:alink/scanner/barcode/web.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import '../../util/application_util.dart';

String? _label;
Function(String result, bool isScanned)? _resultCallback;

class AppBarcodeScannerWidget extends StatefulWidget {
  final String? title;
  AppBarcodeScannerWidget.defaultStyle({
    Key? key,
    required Function(String result, bool isScanned) resultCallback,
    String label = 'Single number',
    this.title,
  }) : super(key: key) {
    _resultCallback = resultCallback;
    _label = label;
  }

  @override
  _AppBarcodeState createState() => _AppBarcodeState();
}

class _AppBarcodeState extends State<AppBarcodeScannerWidget> {
  @override
  Widget build(BuildContext context) {
    return BarcodePermissionWidget(
      title: widget.title,
    );
  }
}

class BarcodePermissionWidget extends StatefulWidget {
  final String? title;

  const BarcodePermissionWidget({super.key, this.title});

  @override
  State<StatefulWidget> createState() {
    return _BarcodePermissionWidgetState();
  }
}

class _BarcodePermissionWidgetState extends State<BarcodePermissionWidget> {
  bool _isGranted = false;

  @override
  void initState() {
    super.initState();
  }

  void _requestMobilePermission() async {
    if (await Permission.camera.request().isGranted) {
      setState(() {
        _isGranted = true;
      });
    } else {
      ApplicationUtil.showWarningAlertDialog(context,
          title: "Permission not granted",
          desc: AppLocalizations.of(context)!.setCameraPermission,
          positiveLabel: AppLocalizations.of(context)!.okay,
          onPositiveClickListener: () {
        Navigator.of(context).pop();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    TargetPlatform platform = Theme.of(context).platform;
    if (!kIsWeb) {
      if (platform == TargetPlatform.android ||
          platform == TargetPlatform.iOS) {
        _requestMobilePermission();
      } else {
        setState(() {
          _isGranted = true;
        });
      }
    } else {
      setState(() {
        _isGranted = true;
      });
    }

    return Column(
      children: <Widget>[
        Expanded(
          child: _isGranted
              ? _getBarcodeScannerWidget()
              : Center(
                  child: OutlinedButton(
                    onPressed: () {
                      _requestMobilePermission();
                    },
                    child:
                        Text(AppLocalizations.of(context)!.requestPermission),
                  ),
                ),
        ),
      ],
    );
  }

  _getBarcodeScannerWidget() {
    return BarcodeScannerWidget(
      _resultCallback,
      title: widget.title,
    );
  }
}
