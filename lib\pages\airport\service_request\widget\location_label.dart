import 'dart:collection';

import 'package:flutter/material.dart';

import '../../../../logger/logger.dart';

class LocationLabel extends StatelessWidget {
  static const String className = 'LocationLabel';
  final List<Map<String, dynamic>> locationMap;
  const LocationLabel({Key? key, required this.locationMap}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Logger.i("Class Name: " + className);
    List<Map<String, dynamic>> reversedMap = [];
    locationMap.forEach((element) {
      reversedMap
          .add(LinkedHashMap.fromEntries(element.entries.toList().reversed));
    });
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: _getLocationlabelChildren(context, reversedMap),
    );
  }

  _getLocationlabelChildren(
      BuildContext context, List<Map<String, dynamic>> reversedMap) {
    List<Widget> widgetList = [];
    reversedMap.forEach(
      (element) {
        element.values.map(
          (e) {
            widgetList.add(Container(
              width: double.infinity,
              margin: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 15),
              decoration: BoxDecoration(
                border: Border.all(color: Theme.of(context).primaryColor),
                borderRadius: BorderRadius.circular(15),
              ),
              child: Container(
                margin: const EdgeInsets.only(left: 10),
                child: Text(
                  e['NAME'],
                  style: const TextStyle(
                      color: Colors.black, fontWeight: FontWeight.bold),
                ),
              ),
            ));
          },
        );
      },
    );
    return widgetList;
  }
}
