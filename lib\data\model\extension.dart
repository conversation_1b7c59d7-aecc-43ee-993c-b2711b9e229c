class Extension {
  String? title;
  List<ExtensionContent>? extensionContent;

  Extension({
    this.title,
    this.extensionContent,
  });
  Map<String, dynamic> toMap() {
    return {
      'title': title,
      'extensionContent': extensionContent,
    };
  }

  factory Extension.fromMap(Map<String, dynamic> map) {
    return Extension(
      title: map['title'] as String,
      extensionContent: map['extensionContent'] as List<ExtensionContent>,
    );
  }
}

class ExtensionContent {
  int? fieldOrder;
  int? extensionId;
  String? fieldTechName;
  String? fieldName;
  String? fieldControl;
  String? fieldChoiceType;
  String? fieldMandatory;
  ExtensionContent({
    this.fieldOrder,
    this.extensionId,
    this.fieldTechName,
    this.fieldName,
    this.fieldControl,
    this.fieldChoiceType,
    this.fieldMandatory,
  });

  Map<String, dynamic> toMap() {
    return {
      'FIELD_ORDER': fieldOrder,
      'EXTENSION_ID': extensionId,
      'FIELD_TECH_NAME': fieldTechName,
      'FIELD_NAME': fieldName,
      'FIELD_CONTROL': fieldControl,
      'FIELD_CHOICE_TYPE': fieldChoiceType,
      'FIELD_MANDATORY': fieldMandatory,
    };
  }

  factory ExtensionContent.fromMap(Map<String, dynamic> map) {
    return ExtensionContent(
      fieldOrder: map['FIELD_ORDER'] as int,
      extensionId: map['EXTENSION_ID'] as int,
      fieldTechName: map['FIELD_TECH_NAME'] as String,
      fieldName: map['FIELD_NAME'] as String,
      fieldControl: map['FIELD_CONTROL'] as String,
      fieldChoiceType: map['FIELD_CHOICE_TYPE'] as dynamic,
      fieldMandatory: map['FIELD_MANDATORY'] as String,
    );
  }
}
