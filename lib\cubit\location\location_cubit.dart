import 'package:alink/bloc/api/api_bloc.dart';
import 'package:alink/data/model/location_detail.dart';
import 'package:alink/data/repository/api_repository.dart';
import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';

part 'location_state.dart';

class LocationCubit extends Cubit<LocationState> {
  final ApiRepository apiRepository;
  LocationCubit({required this.apiRepository}) : super(LocationInitial());

  getLocationData() async {
    emit(FetchingLocationData());
    try {
      var response = await apiRepository.getLocationData();
      if (response is SingleServiceDetailError) {
        emit(FetchLocationDataError(errorMessage: response.errorMessage));
      } else if (response is List<LocationDetail>) {
        print("location in cubit");
        emit(FetchedLocationData(response));
      } else {
        emit(FetchLocationDataError(errorMessage: response));
      }
    } catch (error) {
      emit(FetchLocationDataError(errorMessage: error.toString()));
    }
  }
}
