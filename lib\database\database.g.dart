// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'database.dart';

// ignore_for_file: type=lint
class $ConfigTable extends Config with TableInfo<$ConfigTable, ConfigData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $ConfigTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'CONFIG_ID', aliasedName, false,
      hasAutoIncrement: true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('PRIMARY KEY AUTOINCREMENT'));
  static const VerificationMeta _keyMeta = const VerificationMeta('key');
  @override
  late final GeneratedColumn<String> key = GeneratedColumn<String>(
      'KEY', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _valueMeta = const VerificationMeta('value');
  @override
  late final GeneratedColumn<String> value = GeneratedColumn<String>(
      'VALUE', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  @override
  List<GeneratedColumn> get $columns => [id, key, value];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'CONFIG';
  @override
  VerificationContext validateIntegrity(Insertable<ConfigData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('CONFIG_ID')) {
      context.handle(
          _idMeta, id.isAcceptableOrUnknown(data['CONFIG_ID']!, _idMeta));
    }
    if (data.containsKey('KEY')) {
      context.handle(
          _keyMeta, key.isAcceptableOrUnknown(data['KEY']!, _keyMeta));
    }
    if (data.containsKey('VALUE')) {
      context.handle(
          _valueMeta, value.isAcceptableOrUnknown(data['VALUE']!, _valueMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  ConfigData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return ConfigData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}CONFIG_ID'])!,
      key: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}KEY']),
      value: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}VALUE']),
    );
  }

  @override
  $ConfigTable createAlias(String alias) {
    return $ConfigTable(attachedDatabase, alias);
  }
}

class ConfigData extends DataClass implements Insertable<ConfigData> {
  final int id;
  final String? key;
  final String? value;
  const ConfigData({required this.id, this.key, this.value});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['CONFIG_ID'] = Variable<int>(id);
    if (!nullToAbsent || key != null) {
      map['KEY'] = Variable<String>(key);
    }
    if (!nullToAbsent || value != null) {
      map['VALUE'] = Variable<String>(value);
    }
    return map;
  }

  ConfigCompanion toCompanion(bool nullToAbsent) {
    return ConfigCompanion(
      id: Value(id),
      key: key == null && nullToAbsent ? const Value.absent() : Value(key),
      value:
          value == null && nullToAbsent ? const Value.absent() : Value(value),
    );
  }

  factory ConfigData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return ConfigData(
      id: serializer.fromJson<int>(json['id']),
      key: serializer.fromJson<String?>(json['key']),
      value: serializer.fromJson<String?>(json['value']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'key': serializer.toJson<String?>(key),
      'value': serializer.toJson<String?>(value),
    };
  }

  ConfigData copyWith(
          {int? id,
          Value<String?> key = const Value.absent(),
          Value<String?> value = const Value.absent()}) =>
      ConfigData(
        id: id ?? this.id,
        key: key.present ? key.value : this.key,
        value: value.present ? value.value : this.value,
      );
  ConfigData copyWithCompanion(ConfigCompanion data) {
    return ConfigData(
      id: data.id.present ? data.id.value : this.id,
      key: data.key.present ? data.key.value : this.key,
      value: data.value.present ? data.value.value : this.value,
    );
  }

  @override
  String toString() {
    return (StringBuffer('ConfigData(')
          ..write('id: $id, ')
          ..write('key: $key, ')
          ..write('value: $value')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, key, value);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is ConfigData &&
          other.id == this.id &&
          other.key == this.key &&
          other.value == this.value);
}

class ConfigCompanion extends UpdateCompanion<ConfigData> {
  final Value<int> id;
  final Value<String?> key;
  final Value<String?> value;
  const ConfigCompanion({
    this.id = const Value.absent(),
    this.key = const Value.absent(),
    this.value = const Value.absent(),
  });
  ConfigCompanion.insert({
    this.id = const Value.absent(),
    this.key = const Value.absent(),
    this.value = const Value.absent(),
  });
  static Insertable<ConfigData> custom({
    Expression<int>? id,
    Expression<String>? key,
    Expression<String>? value,
  }) {
    return RawValuesInsertable({
      if (id != null) 'CONFIG_ID': id,
      if (key != null) 'KEY': key,
      if (value != null) 'VALUE': value,
    });
  }

  ConfigCompanion copyWith(
      {Value<int>? id, Value<String?>? key, Value<String?>? value}) {
    return ConfigCompanion(
      id: id ?? this.id,
      key: key ?? this.key,
      value: value ?? this.value,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['CONFIG_ID'] = Variable<int>(id.value);
    }
    if (key.present) {
      map['KEY'] = Variable<String>(key.value);
    }
    if (value.present) {
      map['VALUE'] = Variable<String>(value.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('ConfigCompanion(')
          ..write('id: $id, ')
          ..write('key: $key, ')
          ..write('value: $value')
          ..write(')'))
        .toString();
  }
}

class $UserTable extends User with TableInfo<$UserTable, UserData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $UserTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _USER_IDMeta =
      const VerificationMeta('USER_ID');
  @override
  late final GeneratedColumn<int> USER_ID = GeneratedColumn<int>(
      'USER_ID', aliasedName, false,
      hasAutoIncrement: true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('PRIMARY KEY AUTOINCREMENT'));
  static const VerificationMeta _FIRST_NAMEMeta =
      const VerificationMeta('FIRST_NAME');
  @override
  late final GeneratedColumn<String> FIRST_NAME = GeneratedColumn<String>(
      'FIRST_NAME', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _LAST_NAMEMeta =
      const VerificationMeta('LAST_NAME');
  @override
  late final GeneratedColumn<String> LAST_NAME = GeneratedColumn<String>(
      'LAST_NAME', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _EMAILMeta = const VerificationMeta('EMAIL');
  @override
  late final GeneratedColumn<String> EMAIL = GeneratedColumn<String>(
      'EMAIL', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _PHONEMeta = const VerificationMeta('PHONE');
  @override
  late final GeneratedColumn<String> PHONE = GeneratedColumn<String>(
      'PHONE', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _MODULEMeta = const VerificationMeta('MODULE');
  @override
  late final GeneratedColumnWithTypeConverter<List<dynamic>?, String> MODULE =
      GeneratedColumn<String>('MODULE', aliasedName, true,
              type: DriftSqlType.string, requiredDuringInsert: false)
          .withConverter<List<dynamic>?>($UserTable.$converterMODULEn);
  static const VerificationMeta _AFS_USERMeta =
      const VerificationMeta('AFS_USER');
  @override
  late final GeneratedColumn<bool> AFS_USER = GeneratedColumn<bool>(
      'AFS_USER', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('CHECK ("AFS_USER" IN (0, 1))'));
  static const VerificationMeta _CUSTOMER_TYPEMeta =
      const VerificationMeta('CUSTOMER_TYPE');
  @override
  late final GeneratedColumn<String> CUSTOMER_TYPE = GeneratedColumn<String>(
      'CUSTOMER_TYPE', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  @override
  List<GeneratedColumn> get $columns => [
        USER_ID,
        FIRST_NAME,
        LAST_NAME,
        EMAIL,
        PHONE,
        MODULE,
        AFS_USER,
        CUSTOMER_TYPE
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'USER';
  @override
  VerificationContext validateIntegrity(Insertable<UserData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('USER_ID')) {
      context.handle(_USER_IDMeta,
          USER_ID.isAcceptableOrUnknown(data['USER_ID']!, _USER_IDMeta));
    }
    if (data.containsKey('FIRST_NAME')) {
      context.handle(
          _FIRST_NAMEMeta,
          FIRST_NAME.isAcceptableOrUnknown(
              data['FIRST_NAME']!, _FIRST_NAMEMeta));
    }
    if (data.containsKey('LAST_NAME')) {
      context.handle(_LAST_NAMEMeta,
          LAST_NAME.isAcceptableOrUnknown(data['LAST_NAME']!, _LAST_NAMEMeta));
    }
    if (data.containsKey('EMAIL')) {
      context.handle(
          _EMAILMeta, EMAIL.isAcceptableOrUnknown(data['EMAIL']!, _EMAILMeta));
    }
    if (data.containsKey('PHONE')) {
      context.handle(
          _PHONEMeta, PHONE.isAcceptableOrUnknown(data['PHONE']!, _PHONEMeta));
    }
    context.handle(_MODULEMeta, const VerificationResult.success());
    if (data.containsKey('AFS_USER')) {
      context.handle(_AFS_USERMeta,
          AFS_USER.isAcceptableOrUnknown(data['AFS_USER']!, _AFS_USERMeta));
    }
    if (data.containsKey('CUSTOMER_TYPE')) {
      context.handle(
          _CUSTOMER_TYPEMeta,
          CUSTOMER_TYPE.isAcceptableOrUnknown(
              data['CUSTOMER_TYPE']!, _CUSTOMER_TYPEMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {USER_ID};
  @override
  UserData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return UserData(
      USER_ID: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}USER_ID'])!,
      FIRST_NAME: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}FIRST_NAME']),
      LAST_NAME: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}LAST_NAME']),
      EMAIL: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}EMAIL']),
      PHONE: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}PHONE']),
      MODULE: $UserTable.$converterMODULEn.fromSql(attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}MODULE'])),
      AFS_USER: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}AFS_USER']),
      CUSTOMER_TYPE: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}CUSTOMER_TYPE']),
    );
  }

  @override
  $UserTable createAlias(String alias) {
    return $UserTable(attachedDatabase, alias);
  }

  static TypeConverter<List<dynamic>, String> $converterMODULE =
      const JsonConverter();
  static TypeConverter<List<dynamic>?, String?> $converterMODULEn =
      NullAwareTypeConverter.wrap($converterMODULE);
}

class UserData extends DataClass implements Insertable<UserData> {
  final int USER_ID;
  final String? FIRST_NAME;
  final String? LAST_NAME;
  final String? EMAIL;
  final String? PHONE;
  final List<dynamic>? MODULE;
  final bool? AFS_USER;
  final String? CUSTOMER_TYPE;
  const UserData(
      {required this.USER_ID,
      this.FIRST_NAME,
      this.LAST_NAME,
      this.EMAIL,
      this.PHONE,
      this.MODULE,
      this.AFS_USER,
      this.CUSTOMER_TYPE});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['USER_ID'] = Variable<int>(USER_ID);
    if (!nullToAbsent || FIRST_NAME != null) {
      map['FIRST_NAME'] = Variable<String>(FIRST_NAME);
    }
    if (!nullToAbsent || LAST_NAME != null) {
      map['LAST_NAME'] = Variable<String>(LAST_NAME);
    }
    if (!nullToAbsent || EMAIL != null) {
      map['EMAIL'] = Variable<String>(EMAIL);
    }
    if (!nullToAbsent || PHONE != null) {
      map['PHONE'] = Variable<String>(PHONE);
    }
    if (!nullToAbsent || MODULE != null) {
      map['MODULE'] =
          Variable<String>($UserTable.$converterMODULEn.toSql(MODULE));
    }
    if (!nullToAbsent || AFS_USER != null) {
      map['AFS_USER'] = Variable<bool>(AFS_USER);
    }
    if (!nullToAbsent || CUSTOMER_TYPE != null) {
      map['CUSTOMER_TYPE'] = Variable<String>(CUSTOMER_TYPE);
    }
    return map;
  }

  UserCompanion toCompanion(bool nullToAbsent) {
    return UserCompanion(
      USER_ID: Value(USER_ID),
      FIRST_NAME: FIRST_NAME == null && nullToAbsent
          ? const Value.absent()
          : Value(FIRST_NAME),
      LAST_NAME: LAST_NAME == null && nullToAbsent
          ? const Value.absent()
          : Value(LAST_NAME),
      EMAIL:
          EMAIL == null && nullToAbsent ? const Value.absent() : Value(EMAIL),
      PHONE:
          PHONE == null && nullToAbsent ? const Value.absent() : Value(PHONE),
      MODULE:
          MODULE == null && nullToAbsent ? const Value.absent() : Value(MODULE),
      AFS_USER: AFS_USER == null && nullToAbsent
          ? const Value.absent()
          : Value(AFS_USER),
      CUSTOMER_TYPE: CUSTOMER_TYPE == null && nullToAbsent
          ? const Value.absent()
          : Value(CUSTOMER_TYPE),
    );
  }

  factory UserData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return UserData(
      USER_ID: serializer.fromJson<int>(json['USER_ID']),
      FIRST_NAME: serializer.fromJson<String?>(json['FIRST_NAME']),
      LAST_NAME: serializer.fromJson<String?>(json['LAST_NAME']),
      EMAIL: serializer.fromJson<String?>(json['EMAIL']),
      PHONE: serializer.fromJson<String?>(json['PHONE']),
      MODULE: serializer.fromJson<List<dynamic>?>(json['MODULE']),
      AFS_USER: serializer.fromJson<bool?>(json['AFS_USER']),
      CUSTOMER_TYPE: serializer.fromJson<String?>(json['CUSTOMER_TYPE']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'USER_ID': serializer.toJson<int>(USER_ID),
      'FIRST_NAME': serializer.toJson<String?>(FIRST_NAME),
      'LAST_NAME': serializer.toJson<String?>(LAST_NAME),
      'EMAIL': serializer.toJson<String?>(EMAIL),
      'PHONE': serializer.toJson<String?>(PHONE),
      'MODULE': serializer.toJson<List<dynamic>?>(MODULE),
      'AFS_USER': serializer.toJson<bool?>(AFS_USER),
      'CUSTOMER_TYPE': serializer.toJson<String?>(CUSTOMER_TYPE),
    };
  }

  UserData copyWith(
          {int? USER_ID,
          Value<String?> FIRST_NAME = const Value.absent(),
          Value<String?> LAST_NAME = const Value.absent(),
          Value<String?> EMAIL = const Value.absent(),
          Value<String?> PHONE = const Value.absent(),
          Value<List<dynamic>?> MODULE = const Value.absent(),
          Value<bool?> AFS_USER = const Value.absent(),
          Value<String?> CUSTOMER_TYPE = const Value.absent()}) =>
      UserData(
        USER_ID: USER_ID ?? this.USER_ID,
        FIRST_NAME: FIRST_NAME.present ? FIRST_NAME.value : this.FIRST_NAME,
        LAST_NAME: LAST_NAME.present ? LAST_NAME.value : this.LAST_NAME,
        EMAIL: EMAIL.present ? EMAIL.value : this.EMAIL,
        PHONE: PHONE.present ? PHONE.value : this.PHONE,
        MODULE: MODULE.present ? MODULE.value : this.MODULE,
        AFS_USER: AFS_USER.present ? AFS_USER.value : this.AFS_USER,
        CUSTOMER_TYPE:
            CUSTOMER_TYPE.present ? CUSTOMER_TYPE.value : this.CUSTOMER_TYPE,
      );
  UserData copyWithCompanion(UserCompanion data) {
    return UserData(
      USER_ID: data.USER_ID.present ? data.USER_ID.value : this.USER_ID,
      FIRST_NAME:
          data.FIRST_NAME.present ? data.FIRST_NAME.value : this.FIRST_NAME,
      LAST_NAME: data.LAST_NAME.present ? data.LAST_NAME.value : this.LAST_NAME,
      EMAIL: data.EMAIL.present ? data.EMAIL.value : this.EMAIL,
      PHONE: data.PHONE.present ? data.PHONE.value : this.PHONE,
      MODULE: data.MODULE.present ? data.MODULE.value : this.MODULE,
      AFS_USER: data.AFS_USER.present ? data.AFS_USER.value : this.AFS_USER,
      CUSTOMER_TYPE: data.CUSTOMER_TYPE.present
          ? data.CUSTOMER_TYPE.value
          : this.CUSTOMER_TYPE,
    );
  }

  @override
  String toString() {
    return (StringBuffer('UserData(')
          ..write('USER_ID: $USER_ID, ')
          ..write('FIRST_NAME: $FIRST_NAME, ')
          ..write('LAST_NAME: $LAST_NAME, ')
          ..write('EMAIL: $EMAIL, ')
          ..write('PHONE: $PHONE, ')
          ..write('MODULE: $MODULE, ')
          ..write('AFS_USER: $AFS_USER, ')
          ..write('CUSTOMER_TYPE: $CUSTOMER_TYPE')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(USER_ID, FIRST_NAME, LAST_NAME, EMAIL, PHONE,
      MODULE, AFS_USER, CUSTOMER_TYPE);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is UserData &&
          other.USER_ID == this.USER_ID &&
          other.FIRST_NAME == this.FIRST_NAME &&
          other.LAST_NAME == this.LAST_NAME &&
          other.EMAIL == this.EMAIL &&
          other.PHONE == this.PHONE &&
          other.MODULE == this.MODULE &&
          other.AFS_USER == this.AFS_USER &&
          other.CUSTOMER_TYPE == this.CUSTOMER_TYPE);
}

class UserCompanion extends UpdateCompanion<UserData> {
  final Value<int> USER_ID;
  final Value<String?> FIRST_NAME;
  final Value<String?> LAST_NAME;
  final Value<String?> EMAIL;
  final Value<String?> PHONE;
  final Value<List<dynamic>?> MODULE;
  final Value<bool?> AFS_USER;
  final Value<String?> CUSTOMER_TYPE;
  const UserCompanion({
    this.USER_ID = const Value.absent(),
    this.FIRST_NAME = const Value.absent(),
    this.LAST_NAME = const Value.absent(),
    this.EMAIL = const Value.absent(),
    this.PHONE = const Value.absent(),
    this.MODULE = const Value.absent(),
    this.AFS_USER = const Value.absent(),
    this.CUSTOMER_TYPE = const Value.absent(),
  });
  UserCompanion.insert({
    this.USER_ID = const Value.absent(),
    this.FIRST_NAME = const Value.absent(),
    this.LAST_NAME = const Value.absent(),
    this.EMAIL = const Value.absent(),
    this.PHONE = const Value.absent(),
    this.MODULE = const Value.absent(),
    this.AFS_USER = const Value.absent(),
    this.CUSTOMER_TYPE = const Value.absent(),
  });
  static Insertable<UserData> custom({
    Expression<int>? USER_ID,
    Expression<String>? FIRST_NAME,
    Expression<String>? LAST_NAME,
    Expression<String>? EMAIL,
    Expression<String>? PHONE,
    Expression<String>? MODULE,
    Expression<bool>? AFS_USER,
    Expression<String>? CUSTOMER_TYPE,
  }) {
    return RawValuesInsertable({
      if (USER_ID != null) 'USER_ID': USER_ID,
      if (FIRST_NAME != null) 'FIRST_NAME': FIRST_NAME,
      if (LAST_NAME != null) 'LAST_NAME': LAST_NAME,
      if (EMAIL != null) 'EMAIL': EMAIL,
      if (PHONE != null) 'PHONE': PHONE,
      if (MODULE != null) 'MODULE': MODULE,
      if (AFS_USER != null) 'AFS_USER': AFS_USER,
      if (CUSTOMER_TYPE != null) 'CUSTOMER_TYPE': CUSTOMER_TYPE,
    });
  }

  UserCompanion copyWith(
      {Value<int>? USER_ID,
      Value<String?>? FIRST_NAME,
      Value<String?>? LAST_NAME,
      Value<String?>? EMAIL,
      Value<String?>? PHONE,
      Value<List<dynamic>?>? MODULE,
      Value<bool?>? AFS_USER,
      Value<String?>? CUSTOMER_TYPE}) {
    return UserCompanion(
      USER_ID: USER_ID ?? this.USER_ID,
      FIRST_NAME: FIRST_NAME ?? this.FIRST_NAME,
      LAST_NAME: LAST_NAME ?? this.LAST_NAME,
      EMAIL: EMAIL ?? this.EMAIL,
      PHONE: PHONE ?? this.PHONE,
      MODULE: MODULE ?? this.MODULE,
      AFS_USER: AFS_USER ?? this.AFS_USER,
      CUSTOMER_TYPE: CUSTOMER_TYPE ?? this.CUSTOMER_TYPE,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (USER_ID.present) {
      map['USER_ID'] = Variable<int>(USER_ID.value);
    }
    if (FIRST_NAME.present) {
      map['FIRST_NAME'] = Variable<String>(FIRST_NAME.value);
    }
    if (LAST_NAME.present) {
      map['LAST_NAME'] = Variable<String>(LAST_NAME.value);
    }
    if (EMAIL.present) {
      map['EMAIL'] = Variable<String>(EMAIL.value);
    }
    if (PHONE.present) {
      map['PHONE'] = Variable<String>(PHONE.value);
    }
    if (MODULE.present) {
      map['MODULE'] =
          Variable<String>($UserTable.$converterMODULEn.toSql(MODULE.value));
    }
    if (AFS_USER.present) {
      map['AFS_USER'] = Variable<bool>(AFS_USER.value);
    }
    if (CUSTOMER_TYPE.present) {
      map['CUSTOMER_TYPE'] = Variable<String>(CUSTOMER_TYPE.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('UserCompanion(')
          ..write('USER_ID: $USER_ID, ')
          ..write('FIRST_NAME: $FIRST_NAME, ')
          ..write('LAST_NAME: $LAST_NAME, ')
          ..write('EMAIL: $EMAIL, ')
          ..write('PHONE: $PHONE, ')
          ..write('MODULE: $MODULE, ')
          ..write('AFS_USER: $AFS_USER, ')
          ..write('CUSTOMER_TYPE: $CUSTOMER_TYPE')
          ..write(')'))
        .toString();
  }
}

class $ServiceRequestTable extends ServiceRequest
    with TableInfo<$ServiceRequestTable, ServiceRequestData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $ServiceRequestTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _requestIdMeta =
      const VerificationMeta('requestId');
  @override
  late final GeneratedColumn<int> requestId = GeneratedColumn<int>(
      'REQUEST_ID', aliasedName, false,
      hasAutoIncrement: true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('PRIMARY KEY AUTOINCREMENT'));
  static const VerificationMeta _customerIdMeta =
      const VerificationMeta('customerId');
  @override
  late final GeneratedColumn<int> customerId = GeneratedColumn<int>(
      'CUSTOMER_ID', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _equipmentIdMeta =
      const VerificationMeta('equipmentId');
  @override
  late final GeneratedColumn<String> equipmentId = GeneratedColumn<String>(
      'EQUIPMENT_ID', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _requestTypeMeta =
      const VerificationMeta('requestType');
  @override
  late final GeneratedColumn<String> requestType = GeneratedColumn<String>(
      'REQUEST_TYPE', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _locationIdMeta =
      const VerificationMeta('locationId');
  @override
  late final GeneratedColumn<String> locationId = GeneratedColumn<String>(
      'CURRENT_LOCATION_ID', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _newLocationIdMeta =
      const VerificationMeta('newLocationId');
  @override
  late final GeneratedColumn<String> newLocationId = GeneratedColumn<String>(
      'NEW_LOCATION_ID', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _requestChoiceTypeMeta =
      const VerificationMeta('requestChoiceType');
  @override
  late final GeneratedColumn<String> requestChoiceType =
      GeneratedColumn<String>('REQUEST_CHOICE_TYPE', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _descriptionMeta =
      const VerificationMeta('description');
  @override
  late final GeneratedColumn<String> description = GeneratedColumn<String>(
      'DESCRIPTION', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _createdByMeta =
      const VerificationMeta('createdBy');
  @override
  late final GeneratedColumn<int> createdBy = GeneratedColumn<int>(
      'CREATED_BY', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _createdAtMeta =
      const VerificationMeta('createdAt');
  @override
  late final GeneratedColumn<DateTime> createdAt = GeneratedColumn<DateTime>(
      'CREATED_AT', aliasedName, true,
      type: DriftSqlType.dateTime, requiredDuringInsert: false);
  static const VerificationMeta _statusMeta = const VerificationMeta('status');
  @override
  late final GeneratedColumn<int> status = GeneratedColumn<int>(
      'STATUS', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _documentMeta =
      const VerificationMeta('document');
  @override
  late final GeneratedColumn<String> document = GeneratedColumn<String>(
      'DOCUMENT', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _extnMeta = const VerificationMeta('extn');
  @override
  late final GeneratedColumn<String> extn = GeneratedColumn<String>(
      'EXTN', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _partsMeta = const VerificationMeta('parts');
  @override
  late final GeneratedColumn<String> parts = GeneratedColumn<String>(
      'PARTS', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _newBarcodeMeta =
      const VerificationMeta('newBarcode');
  @override
  late final GeneratedColumn<String> newBarcode = GeneratedColumn<String>(
      'NEW_BARCODE', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _equipmentNameMeta =
      const VerificationMeta('equipmentName');
  @override
  late final GeneratedColumn<String> equipmentName = GeneratedColumn<String>(
      'EQUIPMENT_NAME', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _equipmentCategoryMeta =
      const VerificationMeta('equipmentCategory');
  @override
  late final GeneratedColumn<String> equipmentCategory =
      GeneratedColumn<String>('EQUIPMENT_CATEGORY', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _equipmentBarcodeNumberMeta =
      const VerificationMeta('equipmentBarcodeNumber');
  @override
  late final GeneratedColumn<String> equipmentBarcodeNumber =
      GeneratedColumn<String>('EQUIPMENT_BARCODE_NUMBER', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _clusterIdMeta =
      const VerificationMeta('clusterId');
  @override
  late final GeneratedColumn<int> clusterId = GeneratedColumn<int>(
      'CLUSTER_ID', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _choiceIdMeta =
      const VerificationMeta('choiceId');
  @override
  late final GeneratedColumn<int> choiceId = GeneratedColumn<int>(
      'CHOICE_ID', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _repairDocumentMeta =
      const VerificationMeta('repairDocument');
  @override
  late final GeneratedColumn<String> repairDocument = GeneratedColumn<String>(
      'REPAIR_DOCUMENT', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _auditIdMeta =
      const VerificationMeta('auditId');
  @override
  late final GeneratedColumn<int> auditId = GeneratedColumn<int>(
      'AUDIT_ID', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _latitudeMeta =
      const VerificationMeta('latitude');
  @override
  late final GeneratedColumn<double> latitude = GeneratedColumn<double>(
      'LATITUDE', aliasedName, true,
      type: DriftSqlType.double, requiredDuringInsert: false);
  static const VerificationMeta _longitudeMeta =
      const VerificationMeta('longitude');
  @override
  late final GeneratedColumn<double> longitude = GeneratedColumn<double>(
      'LONGITUDE', aliasedName, true,
      type: DriftSqlType.double, requiredDuringInsert: false);
  static const VerificationMeta _refIdMeta = const VerificationMeta('refId');
  @override
  late final GeneratedColumn<String> refId = GeneratedColumn<String>(
      'REF_ID', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _safetyMeta = const VerificationMeta('safety');
  @override
  late final GeneratedColumn<bool> safety = GeneratedColumn<bool>(
      'SAFETY', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('CHECK ("SAFETY" IN (0, 1))'));
  @override
  List<GeneratedColumn> get $columns => [
        requestId,
        customerId,
        equipmentId,
        requestType,
        locationId,
        newLocationId,
        requestChoiceType,
        description,
        createdBy,
        createdAt,
        status,
        document,
        extn,
        parts,
        newBarcode,
        equipmentName,
        equipmentCategory,
        equipmentBarcodeNumber,
        clusterId,
        choiceId,
        repairDocument,
        auditId,
        latitude,
        longitude,
        refId,
        safety
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'SERVICE_REQUEST';
  @override
  VerificationContext validateIntegrity(Insertable<ServiceRequestData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('REQUEST_ID')) {
      context.handle(_requestIdMeta,
          requestId.isAcceptableOrUnknown(data['REQUEST_ID']!, _requestIdMeta));
    }
    if (data.containsKey('CUSTOMER_ID')) {
      context.handle(
          _customerIdMeta,
          customerId.isAcceptableOrUnknown(
              data['CUSTOMER_ID']!, _customerIdMeta));
    }
    if (data.containsKey('EQUIPMENT_ID')) {
      context.handle(
          _equipmentIdMeta,
          equipmentId.isAcceptableOrUnknown(
              data['EQUIPMENT_ID']!, _equipmentIdMeta));
    }
    if (data.containsKey('REQUEST_TYPE')) {
      context.handle(
          _requestTypeMeta,
          requestType.isAcceptableOrUnknown(
              data['REQUEST_TYPE']!, _requestTypeMeta));
    }
    if (data.containsKey('CURRENT_LOCATION_ID')) {
      context.handle(
          _locationIdMeta,
          locationId.isAcceptableOrUnknown(
              data['CURRENT_LOCATION_ID']!, _locationIdMeta));
    }
    if (data.containsKey('NEW_LOCATION_ID')) {
      context.handle(
          _newLocationIdMeta,
          newLocationId.isAcceptableOrUnknown(
              data['NEW_LOCATION_ID']!, _newLocationIdMeta));
    }
    if (data.containsKey('REQUEST_CHOICE_TYPE')) {
      context.handle(
          _requestChoiceTypeMeta,
          requestChoiceType.isAcceptableOrUnknown(
              data['REQUEST_CHOICE_TYPE']!, _requestChoiceTypeMeta));
    }
    if (data.containsKey('DESCRIPTION')) {
      context.handle(
          _descriptionMeta,
          description.isAcceptableOrUnknown(
              data['DESCRIPTION']!, _descriptionMeta));
    }
    if (data.containsKey('CREATED_BY')) {
      context.handle(_createdByMeta,
          createdBy.isAcceptableOrUnknown(data['CREATED_BY']!, _createdByMeta));
    }
    if (data.containsKey('CREATED_AT')) {
      context.handle(_createdAtMeta,
          createdAt.isAcceptableOrUnknown(data['CREATED_AT']!, _createdAtMeta));
    }
    if (data.containsKey('STATUS')) {
      context.handle(_statusMeta,
          status.isAcceptableOrUnknown(data['STATUS']!, _statusMeta));
    }
    if (data.containsKey('DOCUMENT')) {
      context.handle(_documentMeta,
          document.isAcceptableOrUnknown(data['DOCUMENT']!, _documentMeta));
    }
    if (data.containsKey('EXTN')) {
      context.handle(
          _extnMeta, extn.isAcceptableOrUnknown(data['EXTN']!, _extnMeta));
    }
    if (data.containsKey('PARTS')) {
      context.handle(
          _partsMeta, parts.isAcceptableOrUnknown(data['PARTS']!, _partsMeta));
    }
    if (data.containsKey('NEW_BARCODE')) {
      context.handle(
          _newBarcodeMeta,
          newBarcode.isAcceptableOrUnknown(
              data['NEW_BARCODE']!, _newBarcodeMeta));
    }
    if (data.containsKey('EQUIPMENT_NAME')) {
      context.handle(
          _equipmentNameMeta,
          equipmentName.isAcceptableOrUnknown(
              data['EQUIPMENT_NAME']!, _equipmentNameMeta));
    }
    if (data.containsKey('EQUIPMENT_CATEGORY')) {
      context.handle(
          _equipmentCategoryMeta,
          equipmentCategory.isAcceptableOrUnknown(
              data['EQUIPMENT_CATEGORY']!, _equipmentCategoryMeta));
    }
    if (data.containsKey('EQUIPMENT_BARCODE_NUMBER')) {
      context.handle(
          _equipmentBarcodeNumberMeta,
          equipmentBarcodeNumber.isAcceptableOrUnknown(
              data['EQUIPMENT_BARCODE_NUMBER']!, _equipmentBarcodeNumberMeta));
    }
    if (data.containsKey('CLUSTER_ID')) {
      context.handle(_clusterIdMeta,
          clusterId.isAcceptableOrUnknown(data['CLUSTER_ID']!, _clusterIdMeta));
    }
    if (data.containsKey('CHOICE_ID')) {
      context.handle(_choiceIdMeta,
          choiceId.isAcceptableOrUnknown(data['CHOICE_ID']!, _choiceIdMeta));
    }
    if (data.containsKey('REPAIR_DOCUMENT')) {
      context.handle(
          _repairDocumentMeta,
          repairDocument.isAcceptableOrUnknown(
              data['REPAIR_DOCUMENT']!, _repairDocumentMeta));
    }
    if (data.containsKey('AUDIT_ID')) {
      context.handle(_auditIdMeta,
          auditId.isAcceptableOrUnknown(data['AUDIT_ID']!, _auditIdMeta));
    }
    if (data.containsKey('LATITUDE')) {
      context.handle(_latitudeMeta,
          latitude.isAcceptableOrUnknown(data['LATITUDE']!, _latitudeMeta));
    }
    if (data.containsKey('LONGITUDE')) {
      context.handle(_longitudeMeta,
          longitude.isAcceptableOrUnknown(data['LONGITUDE']!, _longitudeMeta));
    }
    if (data.containsKey('REF_ID')) {
      context.handle(
          _refIdMeta, refId.isAcceptableOrUnknown(data['REF_ID']!, _refIdMeta));
    }
    if (data.containsKey('SAFETY')) {
      context.handle(_safetyMeta,
          safety.isAcceptableOrUnknown(data['SAFETY']!, _safetyMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {requestId};
  @override
  ServiceRequestData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return ServiceRequestData(
      requestId: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}REQUEST_ID'])!,
      customerId: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}CUSTOMER_ID']),
      equipmentId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}EQUIPMENT_ID']),
      requestType: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}REQUEST_TYPE']),
      locationId: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}CURRENT_LOCATION_ID']),
      newLocationId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}NEW_LOCATION_ID']),
      requestChoiceType: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}REQUEST_CHOICE_TYPE']),
      description: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}DESCRIPTION']),
      createdBy: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}CREATED_BY']),
      createdAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}CREATED_AT']),
      status: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}STATUS']),
      document: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}DOCUMENT']),
      extn: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}EXTN']),
      parts: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}PARTS']),
      newBarcode: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}NEW_BARCODE']),
      equipmentName: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}EQUIPMENT_NAME']),
      equipmentCategory: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}EQUIPMENT_CATEGORY']),
      equipmentBarcodeNumber: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}EQUIPMENT_BARCODE_NUMBER']),
      clusterId: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}CLUSTER_ID']),
      choiceId: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}CHOICE_ID']),
      repairDocument: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}REPAIR_DOCUMENT']),
      auditId: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}AUDIT_ID']),
      latitude: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}LATITUDE']),
      longitude: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}LONGITUDE']),
      refId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}REF_ID']),
      safety: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}SAFETY']),
    );
  }

  @override
  $ServiceRequestTable createAlias(String alias) {
    return $ServiceRequestTable(attachedDatabase, alias);
  }
}

class ServiceRequestData extends DataClass
    implements Insertable<ServiceRequestData> {
  final int requestId;
  final int? customerId;
  final String? equipmentId;
  final String? requestType;
  final String? locationId;
  final String? newLocationId;
  final String? requestChoiceType;
  final String? description;
  final int? createdBy;
  final DateTime? createdAt;
  final int? status;
  final String? document;
  final String? extn;
  final String? parts;
  final String? newBarcode;
  final String? equipmentName;
  final String? equipmentCategory;
  final String? equipmentBarcodeNumber;
  final int? clusterId;
  final int? choiceId;
  final String? repairDocument;
  final int? auditId;
  final double? latitude;
  final double? longitude;
  final String? refId;
  final bool? safety;
  const ServiceRequestData(
      {required this.requestId,
      this.customerId,
      this.equipmentId,
      this.requestType,
      this.locationId,
      this.newLocationId,
      this.requestChoiceType,
      this.description,
      this.createdBy,
      this.createdAt,
      this.status,
      this.document,
      this.extn,
      this.parts,
      this.newBarcode,
      this.equipmentName,
      this.equipmentCategory,
      this.equipmentBarcodeNumber,
      this.clusterId,
      this.choiceId,
      this.repairDocument,
      this.auditId,
      this.latitude,
      this.longitude,
      this.refId,
      this.safety});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['REQUEST_ID'] = Variable<int>(requestId);
    if (!nullToAbsent || customerId != null) {
      map['CUSTOMER_ID'] = Variable<int>(customerId);
    }
    if (!nullToAbsent || equipmentId != null) {
      map['EQUIPMENT_ID'] = Variable<String>(equipmentId);
    }
    if (!nullToAbsent || requestType != null) {
      map['REQUEST_TYPE'] = Variable<String>(requestType);
    }
    if (!nullToAbsent || locationId != null) {
      map['CURRENT_LOCATION_ID'] = Variable<String>(locationId);
    }
    if (!nullToAbsent || newLocationId != null) {
      map['NEW_LOCATION_ID'] = Variable<String>(newLocationId);
    }
    if (!nullToAbsent || requestChoiceType != null) {
      map['REQUEST_CHOICE_TYPE'] = Variable<String>(requestChoiceType);
    }
    if (!nullToAbsent || description != null) {
      map['DESCRIPTION'] = Variable<String>(description);
    }
    if (!nullToAbsent || createdBy != null) {
      map['CREATED_BY'] = Variable<int>(createdBy);
    }
    if (!nullToAbsent || createdAt != null) {
      map['CREATED_AT'] = Variable<DateTime>(createdAt);
    }
    if (!nullToAbsent || status != null) {
      map['STATUS'] = Variable<int>(status);
    }
    if (!nullToAbsent || document != null) {
      map['DOCUMENT'] = Variable<String>(document);
    }
    if (!nullToAbsent || extn != null) {
      map['EXTN'] = Variable<String>(extn);
    }
    if (!nullToAbsent || parts != null) {
      map['PARTS'] = Variable<String>(parts);
    }
    if (!nullToAbsent || newBarcode != null) {
      map['NEW_BARCODE'] = Variable<String>(newBarcode);
    }
    if (!nullToAbsent || equipmentName != null) {
      map['EQUIPMENT_NAME'] = Variable<String>(equipmentName);
    }
    if (!nullToAbsent || equipmentCategory != null) {
      map['EQUIPMENT_CATEGORY'] = Variable<String>(equipmentCategory);
    }
    if (!nullToAbsent || equipmentBarcodeNumber != null) {
      map['EQUIPMENT_BARCODE_NUMBER'] =
          Variable<String>(equipmentBarcodeNumber);
    }
    if (!nullToAbsent || clusterId != null) {
      map['CLUSTER_ID'] = Variable<int>(clusterId);
    }
    if (!nullToAbsent || choiceId != null) {
      map['CHOICE_ID'] = Variable<int>(choiceId);
    }
    if (!nullToAbsent || repairDocument != null) {
      map['REPAIR_DOCUMENT'] = Variable<String>(repairDocument);
    }
    if (!nullToAbsent || auditId != null) {
      map['AUDIT_ID'] = Variable<int>(auditId);
    }
    if (!nullToAbsent || latitude != null) {
      map['LATITUDE'] = Variable<double>(latitude);
    }
    if (!nullToAbsent || longitude != null) {
      map['LONGITUDE'] = Variable<double>(longitude);
    }
    if (!nullToAbsent || refId != null) {
      map['REF_ID'] = Variable<String>(refId);
    }
    if (!nullToAbsent || safety != null) {
      map['SAFETY'] = Variable<bool>(safety);
    }
    return map;
  }

  ServiceRequestCompanion toCompanion(bool nullToAbsent) {
    return ServiceRequestCompanion(
      requestId: Value(requestId),
      customerId: customerId == null && nullToAbsent
          ? const Value.absent()
          : Value(customerId),
      equipmentId: equipmentId == null && nullToAbsent
          ? const Value.absent()
          : Value(equipmentId),
      requestType: requestType == null && nullToAbsent
          ? const Value.absent()
          : Value(requestType),
      locationId: locationId == null && nullToAbsent
          ? const Value.absent()
          : Value(locationId),
      newLocationId: newLocationId == null && nullToAbsent
          ? const Value.absent()
          : Value(newLocationId),
      requestChoiceType: requestChoiceType == null && nullToAbsent
          ? const Value.absent()
          : Value(requestChoiceType),
      description: description == null && nullToAbsent
          ? const Value.absent()
          : Value(description),
      createdBy: createdBy == null && nullToAbsent
          ? const Value.absent()
          : Value(createdBy),
      createdAt: createdAt == null && nullToAbsent
          ? const Value.absent()
          : Value(createdAt),
      status:
          status == null && nullToAbsent ? const Value.absent() : Value(status),
      document: document == null && nullToAbsent
          ? const Value.absent()
          : Value(document),
      extn: extn == null && nullToAbsent ? const Value.absent() : Value(extn),
      parts:
          parts == null && nullToAbsent ? const Value.absent() : Value(parts),
      newBarcode: newBarcode == null && nullToAbsent
          ? const Value.absent()
          : Value(newBarcode),
      equipmentName: equipmentName == null && nullToAbsent
          ? const Value.absent()
          : Value(equipmentName),
      equipmentCategory: equipmentCategory == null && nullToAbsent
          ? const Value.absent()
          : Value(equipmentCategory),
      equipmentBarcodeNumber: equipmentBarcodeNumber == null && nullToAbsent
          ? const Value.absent()
          : Value(equipmentBarcodeNumber),
      clusterId: clusterId == null && nullToAbsent
          ? const Value.absent()
          : Value(clusterId),
      choiceId: choiceId == null && nullToAbsent
          ? const Value.absent()
          : Value(choiceId),
      repairDocument: repairDocument == null && nullToAbsent
          ? const Value.absent()
          : Value(repairDocument),
      auditId: auditId == null && nullToAbsent
          ? const Value.absent()
          : Value(auditId),
      latitude: latitude == null && nullToAbsent
          ? const Value.absent()
          : Value(latitude),
      longitude: longitude == null && nullToAbsent
          ? const Value.absent()
          : Value(longitude),
      refId:
          refId == null && nullToAbsent ? const Value.absent() : Value(refId),
      safety:
          safety == null && nullToAbsent ? const Value.absent() : Value(safety),
    );
  }

  factory ServiceRequestData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return ServiceRequestData(
      requestId: serializer.fromJson<int>(json['requestId']),
      customerId: serializer.fromJson<int?>(json['customerId']),
      equipmentId: serializer.fromJson<String?>(json['equipmentId']),
      requestType: serializer.fromJson<String?>(json['requestType']),
      locationId: serializer.fromJson<String?>(json['locationId']),
      newLocationId: serializer.fromJson<String?>(json['newLocationId']),
      requestChoiceType:
          serializer.fromJson<String?>(json['requestChoiceType']),
      description: serializer.fromJson<String?>(json['description']),
      createdBy: serializer.fromJson<int?>(json['createdBy']),
      createdAt: serializer.fromJson<DateTime?>(json['createdAt']),
      status: serializer.fromJson<int?>(json['status']),
      document: serializer.fromJson<String?>(json['document']),
      extn: serializer.fromJson<String?>(json['extn']),
      parts: serializer.fromJson<String?>(json['parts']),
      newBarcode: serializer.fromJson<String?>(json['newBarcode']),
      equipmentName: serializer.fromJson<String?>(json['equipmentName']),
      equipmentCategory:
          serializer.fromJson<String?>(json['equipmentCategory']),
      equipmentBarcodeNumber:
          serializer.fromJson<String?>(json['equipmentBarcodeNumber']),
      clusterId: serializer.fromJson<int?>(json['clusterId']),
      choiceId: serializer.fromJson<int?>(json['choiceId']),
      repairDocument: serializer.fromJson<String?>(json['repairDocument']),
      auditId: serializer.fromJson<int?>(json['auditId']),
      latitude: serializer.fromJson<double?>(json['latitude']),
      longitude: serializer.fromJson<double?>(json['longitude']),
      refId: serializer.fromJson<String?>(json['refId']),
      safety: serializer.fromJson<bool?>(json['safety']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'requestId': serializer.toJson<int>(requestId),
      'customerId': serializer.toJson<int?>(customerId),
      'equipmentId': serializer.toJson<String?>(equipmentId),
      'requestType': serializer.toJson<String?>(requestType),
      'locationId': serializer.toJson<String?>(locationId),
      'newLocationId': serializer.toJson<String?>(newLocationId),
      'requestChoiceType': serializer.toJson<String?>(requestChoiceType),
      'description': serializer.toJson<String?>(description),
      'createdBy': serializer.toJson<int?>(createdBy),
      'createdAt': serializer.toJson<DateTime?>(createdAt),
      'status': serializer.toJson<int?>(status),
      'document': serializer.toJson<String?>(document),
      'extn': serializer.toJson<String?>(extn),
      'parts': serializer.toJson<String?>(parts),
      'newBarcode': serializer.toJson<String?>(newBarcode),
      'equipmentName': serializer.toJson<String?>(equipmentName),
      'equipmentCategory': serializer.toJson<String?>(equipmentCategory),
      'equipmentBarcodeNumber':
          serializer.toJson<String?>(equipmentBarcodeNumber),
      'clusterId': serializer.toJson<int?>(clusterId),
      'choiceId': serializer.toJson<int?>(choiceId),
      'repairDocument': serializer.toJson<String?>(repairDocument),
      'auditId': serializer.toJson<int?>(auditId),
      'latitude': serializer.toJson<double?>(latitude),
      'longitude': serializer.toJson<double?>(longitude),
      'refId': serializer.toJson<String?>(refId),
      'safety': serializer.toJson<bool?>(safety),
    };
  }

  ServiceRequestData copyWith(
          {int? requestId,
          Value<int?> customerId = const Value.absent(),
          Value<String?> equipmentId = const Value.absent(),
          Value<String?> requestType = const Value.absent(),
          Value<String?> locationId = const Value.absent(),
          Value<String?> newLocationId = const Value.absent(),
          Value<String?> requestChoiceType = const Value.absent(),
          Value<String?> description = const Value.absent(),
          Value<int?> createdBy = const Value.absent(),
          Value<DateTime?> createdAt = const Value.absent(),
          Value<int?> status = const Value.absent(),
          Value<String?> document = const Value.absent(),
          Value<String?> extn = const Value.absent(),
          Value<String?> parts = const Value.absent(),
          Value<String?> newBarcode = const Value.absent(),
          Value<String?> equipmentName = const Value.absent(),
          Value<String?> equipmentCategory = const Value.absent(),
          Value<String?> equipmentBarcodeNumber = const Value.absent(),
          Value<int?> clusterId = const Value.absent(),
          Value<int?> choiceId = const Value.absent(),
          Value<String?> repairDocument = const Value.absent(),
          Value<int?> auditId = const Value.absent(),
          Value<double?> latitude = const Value.absent(),
          Value<double?> longitude = const Value.absent(),
          Value<String?> refId = const Value.absent(),
          Value<bool?> safety = const Value.absent()}) =>
      ServiceRequestData(
        requestId: requestId ?? this.requestId,
        customerId: customerId.present ? customerId.value : this.customerId,
        equipmentId: equipmentId.present ? equipmentId.value : this.equipmentId,
        requestType: requestType.present ? requestType.value : this.requestType,
        locationId: locationId.present ? locationId.value : this.locationId,
        newLocationId:
            newLocationId.present ? newLocationId.value : this.newLocationId,
        requestChoiceType: requestChoiceType.present
            ? requestChoiceType.value
            : this.requestChoiceType,
        description: description.present ? description.value : this.description,
        createdBy: createdBy.present ? createdBy.value : this.createdBy,
        createdAt: createdAt.present ? createdAt.value : this.createdAt,
        status: status.present ? status.value : this.status,
        document: document.present ? document.value : this.document,
        extn: extn.present ? extn.value : this.extn,
        parts: parts.present ? parts.value : this.parts,
        newBarcode: newBarcode.present ? newBarcode.value : this.newBarcode,
        equipmentName:
            equipmentName.present ? equipmentName.value : this.equipmentName,
        equipmentCategory: equipmentCategory.present
            ? equipmentCategory.value
            : this.equipmentCategory,
        equipmentBarcodeNumber: equipmentBarcodeNumber.present
            ? equipmentBarcodeNumber.value
            : this.equipmentBarcodeNumber,
        clusterId: clusterId.present ? clusterId.value : this.clusterId,
        choiceId: choiceId.present ? choiceId.value : this.choiceId,
        repairDocument:
            repairDocument.present ? repairDocument.value : this.repairDocument,
        auditId: auditId.present ? auditId.value : this.auditId,
        latitude: latitude.present ? latitude.value : this.latitude,
        longitude: longitude.present ? longitude.value : this.longitude,
        refId: refId.present ? refId.value : this.refId,
        safety: safety.present ? safety.value : this.safety,
      );
  ServiceRequestData copyWithCompanion(ServiceRequestCompanion data) {
    return ServiceRequestData(
      requestId: data.requestId.present ? data.requestId.value : this.requestId,
      customerId:
          data.customerId.present ? data.customerId.value : this.customerId,
      equipmentId:
          data.equipmentId.present ? data.equipmentId.value : this.equipmentId,
      requestType:
          data.requestType.present ? data.requestType.value : this.requestType,
      locationId:
          data.locationId.present ? data.locationId.value : this.locationId,
      newLocationId: data.newLocationId.present
          ? data.newLocationId.value
          : this.newLocationId,
      requestChoiceType: data.requestChoiceType.present
          ? data.requestChoiceType.value
          : this.requestChoiceType,
      description:
          data.description.present ? data.description.value : this.description,
      createdBy: data.createdBy.present ? data.createdBy.value : this.createdBy,
      createdAt: data.createdAt.present ? data.createdAt.value : this.createdAt,
      status: data.status.present ? data.status.value : this.status,
      document: data.document.present ? data.document.value : this.document,
      extn: data.extn.present ? data.extn.value : this.extn,
      parts: data.parts.present ? data.parts.value : this.parts,
      newBarcode:
          data.newBarcode.present ? data.newBarcode.value : this.newBarcode,
      equipmentName: data.equipmentName.present
          ? data.equipmentName.value
          : this.equipmentName,
      equipmentCategory: data.equipmentCategory.present
          ? data.equipmentCategory.value
          : this.equipmentCategory,
      equipmentBarcodeNumber: data.equipmentBarcodeNumber.present
          ? data.equipmentBarcodeNumber.value
          : this.equipmentBarcodeNumber,
      clusterId: data.clusterId.present ? data.clusterId.value : this.clusterId,
      choiceId: data.choiceId.present ? data.choiceId.value : this.choiceId,
      repairDocument: data.repairDocument.present
          ? data.repairDocument.value
          : this.repairDocument,
      auditId: data.auditId.present ? data.auditId.value : this.auditId,
      latitude: data.latitude.present ? data.latitude.value : this.latitude,
      longitude: data.longitude.present ? data.longitude.value : this.longitude,
      refId: data.refId.present ? data.refId.value : this.refId,
      safety: data.safety.present ? data.safety.value : this.safety,
    );
  }

  @override
  String toString() {
    return (StringBuffer('ServiceRequestData(')
          ..write('requestId: $requestId, ')
          ..write('customerId: $customerId, ')
          ..write('equipmentId: $equipmentId, ')
          ..write('requestType: $requestType, ')
          ..write('locationId: $locationId, ')
          ..write('newLocationId: $newLocationId, ')
          ..write('requestChoiceType: $requestChoiceType, ')
          ..write('description: $description, ')
          ..write('createdBy: $createdBy, ')
          ..write('createdAt: $createdAt, ')
          ..write('status: $status, ')
          ..write('document: $document, ')
          ..write('extn: $extn, ')
          ..write('parts: $parts, ')
          ..write('newBarcode: $newBarcode, ')
          ..write('equipmentName: $equipmentName, ')
          ..write('equipmentCategory: $equipmentCategory, ')
          ..write('equipmentBarcodeNumber: $equipmentBarcodeNumber, ')
          ..write('clusterId: $clusterId, ')
          ..write('choiceId: $choiceId, ')
          ..write('repairDocument: $repairDocument, ')
          ..write('auditId: $auditId, ')
          ..write('latitude: $latitude, ')
          ..write('longitude: $longitude, ')
          ..write('refId: $refId, ')
          ..write('safety: $safety')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hashAll([
        requestId,
        customerId,
        equipmentId,
        requestType,
        locationId,
        newLocationId,
        requestChoiceType,
        description,
        createdBy,
        createdAt,
        status,
        document,
        extn,
        parts,
        newBarcode,
        equipmentName,
        equipmentCategory,
        equipmentBarcodeNumber,
        clusterId,
        choiceId,
        repairDocument,
        auditId,
        latitude,
        longitude,
        refId,
        safety
      ]);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is ServiceRequestData &&
          other.requestId == this.requestId &&
          other.customerId == this.customerId &&
          other.equipmentId == this.equipmentId &&
          other.requestType == this.requestType &&
          other.locationId == this.locationId &&
          other.newLocationId == this.newLocationId &&
          other.requestChoiceType == this.requestChoiceType &&
          other.description == this.description &&
          other.createdBy == this.createdBy &&
          other.createdAt == this.createdAt &&
          other.status == this.status &&
          other.document == this.document &&
          other.extn == this.extn &&
          other.parts == this.parts &&
          other.newBarcode == this.newBarcode &&
          other.equipmentName == this.equipmentName &&
          other.equipmentCategory == this.equipmentCategory &&
          other.equipmentBarcodeNumber == this.equipmentBarcodeNumber &&
          other.clusterId == this.clusterId &&
          other.choiceId == this.choiceId &&
          other.repairDocument == this.repairDocument &&
          other.auditId == this.auditId &&
          other.latitude == this.latitude &&
          other.longitude == this.longitude &&
          other.refId == this.refId &&
          other.safety == this.safety);
}

class ServiceRequestCompanion extends UpdateCompanion<ServiceRequestData> {
  final Value<int> requestId;
  final Value<int?> customerId;
  final Value<String?> equipmentId;
  final Value<String?> requestType;
  final Value<String?> locationId;
  final Value<String?> newLocationId;
  final Value<String?> requestChoiceType;
  final Value<String?> description;
  final Value<int?> createdBy;
  final Value<DateTime?> createdAt;
  final Value<int?> status;
  final Value<String?> document;
  final Value<String?> extn;
  final Value<String?> parts;
  final Value<String?> newBarcode;
  final Value<String?> equipmentName;
  final Value<String?> equipmentCategory;
  final Value<String?> equipmentBarcodeNumber;
  final Value<int?> clusterId;
  final Value<int?> choiceId;
  final Value<String?> repairDocument;
  final Value<int?> auditId;
  final Value<double?> latitude;
  final Value<double?> longitude;
  final Value<String?> refId;
  final Value<bool?> safety;
  const ServiceRequestCompanion({
    this.requestId = const Value.absent(),
    this.customerId = const Value.absent(),
    this.equipmentId = const Value.absent(),
    this.requestType = const Value.absent(),
    this.locationId = const Value.absent(),
    this.newLocationId = const Value.absent(),
    this.requestChoiceType = const Value.absent(),
    this.description = const Value.absent(),
    this.createdBy = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.status = const Value.absent(),
    this.document = const Value.absent(),
    this.extn = const Value.absent(),
    this.parts = const Value.absent(),
    this.newBarcode = const Value.absent(),
    this.equipmentName = const Value.absent(),
    this.equipmentCategory = const Value.absent(),
    this.equipmentBarcodeNumber = const Value.absent(),
    this.clusterId = const Value.absent(),
    this.choiceId = const Value.absent(),
    this.repairDocument = const Value.absent(),
    this.auditId = const Value.absent(),
    this.latitude = const Value.absent(),
    this.longitude = const Value.absent(),
    this.refId = const Value.absent(),
    this.safety = const Value.absent(),
  });
  ServiceRequestCompanion.insert({
    this.requestId = const Value.absent(),
    this.customerId = const Value.absent(),
    this.equipmentId = const Value.absent(),
    this.requestType = const Value.absent(),
    this.locationId = const Value.absent(),
    this.newLocationId = const Value.absent(),
    this.requestChoiceType = const Value.absent(),
    this.description = const Value.absent(),
    this.createdBy = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.status = const Value.absent(),
    this.document = const Value.absent(),
    this.extn = const Value.absent(),
    this.parts = const Value.absent(),
    this.newBarcode = const Value.absent(),
    this.equipmentName = const Value.absent(),
    this.equipmentCategory = const Value.absent(),
    this.equipmentBarcodeNumber = const Value.absent(),
    this.clusterId = const Value.absent(),
    this.choiceId = const Value.absent(),
    this.repairDocument = const Value.absent(),
    this.auditId = const Value.absent(),
    this.latitude = const Value.absent(),
    this.longitude = const Value.absent(),
    this.refId = const Value.absent(),
    this.safety = const Value.absent(),
  });
  static Insertable<ServiceRequestData> custom({
    Expression<int>? requestId,
    Expression<int>? customerId,
    Expression<String>? equipmentId,
    Expression<String>? requestType,
    Expression<String>? locationId,
    Expression<String>? newLocationId,
    Expression<String>? requestChoiceType,
    Expression<String>? description,
    Expression<int>? createdBy,
    Expression<DateTime>? createdAt,
    Expression<int>? status,
    Expression<String>? document,
    Expression<String>? extn,
    Expression<String>? parts,
    Expression<String>? newBarcode,
    Expression<String>? equipmentName,
    Expression<String>? equipmentCategory,
    Expression<String>? equipmentBarcodeNumber,
    Expression<int>? clusterId,
    Expression<int>? choiceId,
    Expression<String>? repairDocument,
    Expression<int>? auditId,
    Expression<double>? latitude,
    Expression<double>? longitude,
    Expression<String>? refId,
    Expression<bool>? safety,
  }) {
    return RawValuesInsertable({
      if (requestId != null) 'REQUEST_ID': requestId,
      if (customerId != null) 'CUSTOMER_ID': customerId,
      if (equipmentId != null) 'EQUIPMENT_ID': equipmentId,
      if (requestType != null) 'REQUEST_TYPE': requestType,
      if (locationId != null) 'CURRENT_LOCATION_ID': locationId,
      if (newLocationId != null) 'NEW_LOCATION_ID': newLocationId,
      if (requestChoiceType != null) 'REQUEST_CHOICE_TYPE': requestChoiceType,
      if (description != null) 'DESCRIPTION': description,
      if (createdBy != null) 'CREATED_BY': createdBy,
      if (createdAt != null) 'CREATED_AT': createdAt,
      if (status != null) 'STATUS': status,
      if (document != null) 'DOCUMENT': document,
      if (extn != null) 'EXTN': extn,
      if (parts != null) 'PARTS': parts,
      if (newBarcode != null) 'NEW_BARCODE': newBarcode,
      if (equipmentName != null) 'EQUIPMENT_NAME': equipmentName,
      if (equipmentCategory != null) 'EQUIPMENT_CATEGORY': equipmentCategory,
      if (equipmentBarcodeNumber != null)
        'EQUIPMENT_BARCODE_NUMBER': equipmentBarcodeNumber,
      if (clusterId != null) 'CLUSTER_ID': clusterId,
      if (choiceId != null) 'CHOICE_ID': choiceId,
      if (repairDocument != null) 'REPAIR_DOCUMENT': repairDocument,
      if (auditId != null) 'AUDIT_ID': auditId,
      if (latitude != null) 'LATITUDE': latitude,
      if (longitude != null) 'LONGITUDE': longitude,
      if (refId != null) 'REF_ID': refId,
      if (safety != null) 'SAFETY': safety,
    });
  }

  ServiceRequestCompanion copyWith(
      {Value<int>? requestId,
      Value<int?>? customerId,
      Value<String?>? equipmentId,
      Value<String?>? requestType,
      Value<String?>? locationId,
      Value<String?>? newLocationId,
      Value<String?>? requestChoiceType,
      Value<String?>? description,
      Value<int?>? createdBy,
      Value<DateTime?>? createdAt,
      Value<int?>? status,
      Value<String?>? document,
      Value<String?>? extn,
      Value<String?>? parts,
      Value<String?>? newBarcode,
      Value<String?>? equipmentName,
      Value<String?>? equipmentCategory,
      Value<String?>? equipmentBarcodeNumber,
      Value<int?>? clusterId,
      Value<int?>? choiceId,
      Value<String?>? repairDocument,
      Value<int?>? auditId,
      Value<double?>? latitude,
      Value<double?>? longitude,
      Value<String?>? refId,
      Value<bool?>? safety}) {
    return ServiceRequestCompanion(
      requestId: requestId ?? this.requestId,
      customerId: customerId ?? this.customerId,
      equipmentId: equipmentId ?? this.equipmentId,
      requestType: requestType ?? this.requestType,
      locationId: locationId ?? this.locationId,
      newLocationId: newLocationId ?? this.newLocationId,
      requestChoiceType: requestChoiceType ?? this.requestChoiceType,
      description: description ?? this.description,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      status: status ?? this.status,
      document: document ?? this.document,
      extn: extn ?? this.extn,
      parts: parts ?? this.parts,
      newBarcode: newBarcode ?? this.newBarcode,
      equipmentName: equipmentName ?? this.equipmentName,
      equipmentCategory: equipmentCategory ?? this.equipmentCategory,
      equipmentBarcodeNumber:
          equipmentBarcodeNumber ?? this.equipmentBarcodeNumber,
      clusterId: clusterId ?? this.clusterId,
      choiceId: choiceId ?? this.choiceId,
      repairDocument: repairDocument ?? this.repairDocument,
      auditId: auditId ?? this.auditId,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      refId: refId ?? this.refId,
      safety: safety ?? this.safety,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (requestId.present) {
      map['REQUEST_ID'] = Variable<int>(requestId.value);
    }
    if (customerId.present) {
      map['CUSTOMER_ID'] = Variable<int>(customerId.value);
    }
    if (equipmentId.present) {
      map['EQUIPMENT_ID'] = Variable<String>(equipmentId.value);
    }
    if (requestType.present) {
      map['REQUEST_TYPE'] = Variable<String>(requestType.value);
    }
    if (locationId.present) {
      map['CURRENT_LOCATION_ID'] = Variable<String>(locationId.value);
    }
    if (newLocationId.present) {
      map['NEW_LOCATION_ID'] = Variable<String>(newLocationId.value);
    }
    if (requestChoiceType.present) {
      map['REQUEST_CHOICE_TYPE'] = Variable<String>(requestChoiceType.value);
    }
    if (description.present) {
      map['DESCRIPTION'] = Variable<String>(description.value);
    }
    if (createdBy.present) {
      map['CREATED_BY'] = Variable<int>(createdBy.value);
    }
    if (createdAt.present) {
      map['CREATED_AT'] = Variable<DateTime>(createdAt.value);
    }
    if (status.present) {
      map['STATUS'] = Variable<int>(status.value);
    }
    if (document.present) {
      map['DOCUMENT'] = Variable<String>(document.value);
    }
    if (extn.present) {
      map['EXTN'] = Variable<String>(extn.value);
    }
    if (parts.present) {
      map['PARTS'] = Variable<String>(parts.value);
    }
    if (newBarcode.present) {
      map['NEW_BARCODE'] = Variable<String>(newBarcode.value);
    }
    if (equipmentName.present) {
      map['EQUIPMENT_NAME'] = Variable<String>(equipmentName.value);
    }
    if (equipmentCategory.present) {
      map['EQUIPMENT_CATEGORY'] = Variable<String>(equipmentCategory.value);
    }
    if (equipmentBarcodeNumber.present) {
      map['EQUIPMENT_BARCODE_NUMBER'] =
          Variable<String>(equipmentBarcodeNumber.value);
    }
    if (clusterId.present) {
      map['CLUSTER_ID'] = Variable<int>(clusterId.value);
    }
    if (choiceId.present) {
      map['CHOICE_ID'] = Variable<int>(choiceId.value);
    }
    if (repairDocument.present) {
      map['REPAIR_DOCUMENT'] = Variable<String>(repairDocument.value);
    }
    if (auditId.present) {
      map['AUDIT_ID'] = Variable<int>(auditId.value);
    }
    if (latitude.present) {
      map['LATITUDE'] = Variable<double>(latitude.value);
    }
    if (longitude.present) {
      map['LONGITUDE'] = Variable<double>(longitude.value);
    }
    if (refId.present) {
      map['REF_ID'] = Variable<String>(refId.value);
    }
    if (safety.present) {
      map['SAFETY'] = Variable<bool>(safety.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('ServiceRequestCompanion(')
          ..write('requestId: $requestId, ')
          ..write('customerId: $customerId, ')
          ..write('equipmentId: $equipmentId, ')
          ..write('requestType: $requestType, ')
          ..write('locationId: $locationId, ')
          ..write('newLocationId: $newLocationId, ')
          ..write('requestChoiceType: $requestChoiceType, ')
          ..write('description: $description, ')
          ..write('createdBy: $createdBy, ')
          ..write('createdAt: $createdAt, ')
          ..write('status: $status, ')
          ..write('document: $document, ')
          ..write('extn: $extn, ')
          ..write('parts: $parts, ')
          ..write('newBarcode: $newBarcode, ')
          ..write('equipmentName: $equipmentName, ')
          ..write('equipmentCategory: $equipmentCategory, ')
          ..write('equipmentBarcodeNumber: $equipmentBarcodeNumber, ')
          ..write('clusterId: $clusterId, ')
          ..write('choiceId: $choiceId, ')
          ..write('repairDocument: $repairDocument, ')
          ..write('auditId: $auditId, ')
          ..write('latitude: $latitude, ')
          ..write('longitude: $longitude, ')
          ..write('refId: $refId, ')
          ..write('safety: $safety')
          ..write(')'))
        .toString();
  }
}

class $RepairTable extends Repair with TableInfo<$RepairTable, RepairData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $RepairTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _repairIdMeta =
      const VerificationMeta('repairId');
  @override
  late final GeneratedColumn<int> repairId = GeneratedColumn<int>(
      'REPAIR_ID', aliasedName, false,
      hasAutoIncrement: true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('PRIMARY KEY AUTOINCREMENT'));
  static const VerificationMeta _customerIdMeta =
      const VerificationMeta('customerId');
  @override
  late final GeneratedColumn<int> customerId = GeneratedColumn<int>(
      'CUSTOMER_ID', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _equipmentIdMeta =
      const VerificationMeta('equipmentId');
  @override
  late final GeneratedColumn<String> equipmentId = GeneratedColumn<String>(
      'EQUIPMENT_ID', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _currentLocationIdMeta =
      const VerificationMeta('currentLocationId');
  @override
  late final GeneratedColumn<String> currentLocationId =
      GeneratedColumn<String>('CURRENT_LOCATION', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _newLocationIdMeta =
      const VerificationMeta('newLocationId');
  @override
  late final GeneratedColumn<String> newLocationId = GeneratedColumn<String>(
      'NEW_LOCATION', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _createdByMeta =
      const VerificationMeta('createdBy');
  @override
  late final GeneratedColumn<int> createdBy = GeneratedColumn<int>(
      'CREATED_BY', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _createdAtMeta =
      const VerificationMeta('createdAt');
  @override
  late final GeneratedColumn<DateTime> createdAt = GeneratedColumn<DateTime>(
      'CREATED_AT', aliasedName, true,
      type: DriftSqlType.dateTime, requiredDuringInsert: false);
  static const VerificationMeta _statusMeta = const VerificationMeta('status');
  @override
  late final GeneratedColumn<int> status = GeneratedColumn<int>(
      'STATUS', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _extnMeta = const VerificationMeta('extn');
  @override
  late final GeneratedColumn<String> extn = GeneratedColumn<String>(
      'EXTN', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _partsMeta = const VerificationMeta('parts');
  @override
  late final GeneratedColumn<String> parts = GeneratedColumn<String>(
      'PARTS', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _repairDocumentMeta =
      const VerificationMeta('repairDocument');
  @override
  late final GeneratedColumn<String> repairDocument = GeneratedColumn<String>(
      'REPAIR_DOCUMENT', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _serviceRequestDocumentMeta =
      const VerificationMeta('serviceRequestDocument');
  @override
  late final GeneratedColumn<String> serviceRequestDocument =
      GeneratedColumn<String>('SERVICE_REQUEST_DOCUMENT', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _requestIdMeta =
      const VerificationMeta('requestId');
  @override
  late final GeneratedColumn<int> requestId = GeneratedColumn<int>(
      'REQUEST_ID', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _latitudeMeta =
      const VerificationMeta('latitude');
  @override
  late final GeneratedColumn<double> latitude = GeneratedColumn<double>(
      'LATITUDE', aliasedName, true,
      type: DriftSqlType.double, requiredDuringInsert: false);
  static const VerificationMeta _longitudeMeta =
      const VerificationMeta('longitude');
  @override
  late final GeneratedColumn<double> longitude = GeneratedColumn<double>(
      'LONGITUDE', aliasedName, true,
      type: DriftSqlType.double, requiredDuringInsert: false);
  static const VerificationMeta _refIdMeta = const VerificationMeta('refId');
  @override
  late final GeneratedColumn<String> refId = GeneratedColumn<String>(
      'REF_ID', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _safetyMeta = const VerificationMeta('safety');
  @override
  late final GeneratedColumn<bool> safety = GeneratedColumn<bool>(
      'SAFETY', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('CHECK ("SAFETY" IN (0, 1))'));
  @override
  List<GeneratedColumn> get $columns => [
        repairId,
        customerId,
        equipmentId,
        currentLocationId,
        newLocationId,
        createdBy,
        createdAt,
        status,
        extn,
        parts,
        repairDocument,
        serviceRequestDocument,
        requestId,
        latitude,
        longitude,
        refId,
        safety
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'REPAIR';
  @override
  VerificationContext validateIntegrity(Insertable<RepairData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('REPAIR_ID')) {
      context.handle(_repairIdMeta,
          repairId.isAcceptableOrUnknown(data['REPAIR_ID']!, _repairIdMeta));
    }
    if (data.containsKey('CUSTOMER_ID')) {
      context.handle(
          _customerIdMeta,
          customerId.isAcceptableOrUnknown(
              data['CUSTOMER_ID']!, _customerIdMeta));
    }
    if (data.containsKey('EQUIPMENT_ID')) {
      context.handle(
          _equipmentIdMeta,
          equipmentId.isAcceptableOrUnknown(
              data['EQUIPMENT_ID']!, _equipmentIdMeta));
    }
    if (data.containsKey('CURRENT_LOCATION')) {
      context.handle(
          _currentLocationIdMeta,
          currentLocationId.isAcceptableOrUnknown(
              data['CURRENT_LOCATION']!, _currentLocationIdMeta));
    }
    if (data.containsKey('NEW_LOCATION')) {
      context.handle(
          _newLocationIdMeta,
          newLocationId.isAcceptableOrUnknown(
              data['NEW_LOCATION']!, _newLocationIdMeta));
    }
    if (data.containsKey('CREATED_BY')) {
      context.handle(_createdByMeta,
          createdBy.isAcceptableOrUnknown(data['CREATED_BY']!, _createdByMeta));
    }
    if (data.containsKey('CREATED_AT')) {
      context.handle(_createdAtMeta,
          createdAt.isAcceptableOrUnknown(data['CREATED_AT']!, _createdAtMeta));
    }
    if (data.containsKey('STATUS')) {
      context.handle(_statusMeta,
          status.isAcceptableOrUnknown(data['STATUS']!, _statusMeta));
    }
    if (data.containsKey('EXTN')) {
      context.handle(
          _extnMeta, extn.isAcceptableOrUnknown(data['EXTN']!, _extnMeta));
    }
    if (data.containsKey('PARTS')) {
      context.handle(
          _partsMeta, parts.isAcceptableOrUnknown(data['PARTS']!, _partsMeta));
    }
    if (data.containsKey('REPAIR_DOCUMENT')) {
      context.handle(
          _repairDocumentMeta,
          repairDocument.isAcceptableOrUnknown(
              data['REPAIR_DOCUMENT']!, _repairDocumentMeta));
    }
    if (data.containsKey('SERVICE_REQUEST_DOCUMENT')) {
      context.handle(
          _serviceRequestDocumentMeta,
          serviceRequestDocument.isAcceptableOrUnknown(
              data['SERVICE_REQUEST_DOCUMENT']!, _serviceRequestDocumentMeta));
    }
    if (data.containsKey('REQUEST_ID')) {
      context.handle(_requestIdMeta,
          requestId.isAcceptableOrUnknown(data['REQUEST_ID']!, _requestIdMeta));
    }
    if (data.containsKey('LATITUDE')) {
      context.handle(_latitudeMeta,
          latitude.isAcceptableOrUnknown(data['LATITUDE']!, _latitudeMeta));
    }
    if (data.containsKey('LONGITUDE')) {
      context.handle(_longitudeMeta,
          longitude.isAcceptableOrUnknown(data['LONGITUDE']!, _longitudeMeta));
    }
    if (data.containsKey('REF_ID')) {
      context.handle(
          _refIdMeta, refId.isAcceptableOrUnknown(data['REF_ID']!, _refIdMeta));
    }
    if (data.containsKey('SAFETY')) {
      context.handle(_safetyMeta,
          safety.isAcceptableOrUnknown(data['SAFETY']!, _safetyMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {repairId};
  @override
  RepairData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return RepairData(
      repairId: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}REPAIR_ID'])!,
      customerId: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}CUSTOMER_ID']),
      equipmentId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}EQUIPMENT_ID']),
      currentLocationId: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}CURRENT_LOCATION']),
      newLocationId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}NEW_LOCATION']),
      createdBy: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}CREATED_BY']),
      createdAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}CREATED_AT']),
      status: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}STATUS']),
      extn: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}EXTN']),
      parts: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}PARTS']),
      repairDocument: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}REPAIR_DOCUMENT']),
      serviceRequestDocument: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}SERVICE_REQUEST_DOCUMENT']),
      requestId: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}REQUEST_ID']),
      latitude: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}LATITUDE']),
      longitude: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}LONGITUDE']),
      refId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}REF_ID']),
      safety: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}SAFETY']),
    );
  }

  @override
  $RepairTable createAlias(String alias) {
    return $RepairTable(attachedDatabase, alias);
  }
}

class RepairData extends DataClass implements Insertable<RepairData> {
  final int repairId;
  final int? customerId;
  final String? equipmentId;
  final String? currentLocationId;
  final String? newLocationId;
  final int? createdBy;
  final DateTime? createdAt;
  final int? status;
  final String? extn;
  final String? parts;
  final String? repairDocument;
  final String? serviceRequestDocument;
  final int? requestId;
  final double? latitude;
  final double? longitude;
  final String? refId;
  final bool? safety;
  const RepairData(
      {required this.repairId,
      this.customerId,
      this.equipmentId,
      this.currentLocationId,
      this.newLocationId,
      this.createdBy,
      this.createdAt,
      this.status,
      this.extn,
      this.parts,
      this.repairDocument,
      this.serviceRequestDocument,
      this.requestId,
      this.latitude,
      this.longitude,
      this.refId,
      this.safety});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['REPAIR_ID'] = Variable<int>(repairId);
    if (!nullToAbsent || customerId != null) {
      map['CUSTOMER_ID'] = Variable<int>(customerId);
    }
    if (!nullToAbsent || equipmentId != null) {
      map['EQUIPMENT_ID'] = Variable<String>(equipmentId);
    }
    if (!nullToAbsent || currentLocationId != null) {
      map['CURRENT_LOCATION'] = Variable<String>(currentLocationId);
    }
    if (!nullToAbsent || newLocationId != null) {
      map['NEW_LOCATION'] = Variable<String>(newLocationId);
    }
    if (!nullToAbsent || createdBy != null) {
      map['CREATED_BY'] = Variable<int>(createdBy);
    }
    if (!nullToAbsent || createdAt != null) {
      map['CREATED_AT'] = Variable<DateTime>(createdAt);
    }
    if (!nullToAbsent || status != null) {
      map['STATUS'] = Variable<int>(status);
    }
    if (!nullToAbsent || extn != null) {
      map['EXTN'] = Variable<String>(extn);
    }
    if (!nullToAbsent || parts != null) {
      map['PARTS'] = Variable<String>(parts);
    }
    if (!nullToAbsent || repairDocument != null) {
      map['REPAIR_DOCUMENT'] = Variable<String>(repairDocument);
    }
    if (!nullToAbsent || serviceRequestDocument != null) {
      map['SERVICE_REQUEST_DOCUMENT'] =
          Variable<String>(serviceRequestDocument);
    }
    if (!nullToAbsent || requestId != null) {
      map['REQUEST_ID'] = Variable<int>(requestId);
    }
    if (!nullToAbsent || latitude != null) {
      map['LATITUDE'] = Variable<double>(latitude);
    }
    if (!nullToAbsent || longitude != null) {
      map['LONGITUDE'] = Variable<double>(longitude);
    }
    if (!nullToAbsent || refId != null) {
      map['REF_ID'] = Variable<String>(refId);
    }
    if (!nullToAbsent || safety != null) {
      map['SAFETY'] = Variable<bool>(safety);
    }
    return map;
  }

  RepairCompanion toCompanion(bool nullToAbsent) {
    return RepairCompanion(
      repairId: Value(repairId),
      customerId: customerId == null && nullToAbsent
          ? const Value.absent()
          : Value(customerId),
      equipmentId: equipmentId == null && nullToAbsent
          ? const Value.absent()
          : Value(equipmentId),
      currentLocationId: currentLocationId == null && nullToAbsent
          ? const Value.absent()
          : Value(currentLocationId),
      newLocationId: newLocationId == null && nullToAbsent
          ? const Value.absent()
          : Value(newLocationId),
      createdBy: createdBy == null && nullToAbsent
          ? const Value.absent()
          : Value(createdBy),
      createdAt: createdAt == null && nullToAbsent
          ? const Value.absent()
          : Value(createdAt),
      status:
          status == null && nullToAbsent ? const Value.absent() : Value(status),
      extn: extn == null && nullToAbsent ? const Value.absent() : Value(extn),
      parts:
          parts == null && nullToAbsent ? const Value.absent() : Value(parts),
      repairDocument: repairDocument == null && nullToAbsent
          ? const Value.absent()
          : Value(repairDocument),
      serviceRequestDocument: serviceRequestDocument == null && nullToAbsent
          ? const Value.absent()
          : Value(serviceRequestDocument),
      requestId: requestId == null && nullToAbsent
          ? const Value.absent()
          : Value(requestId),
      latitude: latitude == null && nullToAbsent
          ? const Value.absent()
          : Value(latitude),
      longitude: longitude == null && nullToAbsent
          ? const Value.absent()
          : Value(longitude),
      refId:
          refId == null && nullToAbsent ? const Value.absent() : Value(refId),
      safety:
          safety == null && nullToAbsent ? const Value.absent() : Value(safety),
    );
  }

  factory RepairData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return RepairData(
      repairId: serializer.fromJson<int>(json['repairId']),
      customerId: serializer.fromJson<int?>(json['customerId']),
      equipmentId: serializer.fromJson<String?>(json['equipmentId']),
      currentLocationId:
          serializer.fromJson<String?>(json['currentLocationId']),
      newLocationId: serializer.fromJson<String?>(json['newLocationId']),
      createdBy: serializer.fromJson<int?>(json['createdBy']),
      createdAt: serializer.fromJson<DateTime?>(json['createdAt']),
      status: serializer.fromJson<int?>(json['status']),
      extn: serializer.fromJson<String?>(json['extn']),
      parts: serializer.fromJson<String?>(json['parts']),
      repairDocument: serializer.fromJson<String?>(json['repairDocument']),
      serviceRequestDocument:
          serializer.fromJson<String?>(json['serviceRequestDocument']),
      requestId: serializer.fromJson<int?>(json['requestId']),
      latitude: serializer.fromJson<double?>(json['latitude']),
      longitude: serializer.fromJson<double?>(json['longitude']),
      refId: serializer.fromJson<String?>(json['refId']),
      safety: serializer.fromJson<bool?>(json['safety']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'repairId': serializer.toJson<int>(repairId),
      'customerId': serializer.toJson<int?>(customerId),
      'equipmentId': serializer.toJson<String?>(equipmentId),
      'currentLocationId': serializer.toJson<String?>(currentLocationId),
      'newLocationId': serializer.toJson<String?>(newLocationId),
      'createdBy': serializer.toJson<int?>(createdBy),
      'createdAt': serializer.toJson<DateTime?>(createdAt),
      'status': serializer.toJson<int?>(status),
      'extn': serializer.toJson<String?>(extn),
      'parts': serializer.toJson<String?>(parts),
      'repairDocument': serializer.toJson<String?>(repairDocument),
      'serviceRequestDocument':
          serializer.toJson<String?>(serviceRequestDocument),
      'requestId': serializer.toJson<int?>(requestId),
      'latitude': serializer.toJson<double?>(latitude),
      'longitude': serializer.toJson<double?>(longitude),
      'refId': serializer.toJson<String?>(refId),
      'safety': serializer.toJson<bool?>(safety),
    };
  }

  RepairData copyWith(
          {int? repairId,
          Value<int?> customerId = const Value.absent(),
          Value<String?> equipmentId = const Value.absent(),
          Value<String?> currentLocationId = const Value.absent(),
          Value<String?> newLocationId = const Value.absent(),
          Value<int?> createdBy = const Value.absent(),
          Value<DateTime?> createdAt = const Value.absent(),
          Value<int?> status = const Value.absent(),
          Value<String?> extn = const Value.absent(),
          Value<String?> parts = const Value.absent(),
          Value<String?> repairDocument = const Value.absent(),
          Value<String?> serviceRequestDocument = const Value.absent(),
          Value<int?> requestId = const Value.absent(),
          Value<double?> latitude = const Value.absent(),
          Value<double?> longitude = const Value.absent(),
          Value<String?> refId = const Value.absent(),
          Value<bool?> safety = const Value.absent()}) =>
      RepairData(
        repairId: repairId ?? this.repairId,
        customerId: customerId.present ? customerId.value : this.customerId,
        equipmentId: equipmentId.present ? equipmentId.value : this.equipmentId,
        currentLocationId: currentLocationId.present
            ? currentLocationId.value
            : this.currentLocationId,
        newLocationId:
            newLocationId.present ? newLocationId.value : this.newLocationId,
        createdBy: createdBy.present ? createdBy.value : this.createdBy,
        createdAt: createdAt.present ? createdAt.value : this.createdAt,
        status: status.present ? status.value : this.status,
        extn: extn.present ? extn.value : this.extn,
        parts: parts.present ? parts.value : this.parts,
        repairDocument:
            repairDocument.present ? repairDocument.value : this.repairDocument,
        serviceRequestDocument: serviceRequestDocument.present
            ? serviceRequestDocument.value
            : this.serviceRequestDocument,
        requestId: requestId.present ? requestId.value : this.requestId,
        latitude: latitude.present ? latitude.value : this.latitude,
        longitude: longitude.present ? longitude.value : this.longitude,
        refId: refId.present ? refId.value : this.refId,
        safety: safety.present ? safety.value : this.safety,
      );
  RepairData copyWithCompanion(RepairCompanion data) {
    return RepairData(
      repairId: data.repairId.present ? data.repairId.value : this.repairId,
      customerId:
          data.customerId.present ? data.customerId.value : this.customerId,
      equipmentId:
          data.equipmentId.present ? data.equipmentId.value : this.equipmentId,
      currentLocationId: data.currentLocationId.present
          ? data.currentLocationId.value
          : this.currentLocationId,
      newLocationId: data.newLocationId.present
          ? data.newLocationId.value
          : this.newLocationId,
      createdBy: data.createdBy.present ? data.createdBy.value : this.createdBy,
      createdAt: data.createdAt.present ? data.createdAt.value : this.createdAt,
      status: data.status.present ? data.status.value : this.status,
      extn: data.extn.present ? data.extn.value : this.extn,
      parts: data.parts.present ? data.parts.value : this.parts,
      repairDocument: data.repairDocument.present
          ? data.repairDocument.value
          : this.repairDocument,
      serviceRequestDocument: data.serviceRequestDocument.present
          ? data.serviceRequestDocument.value
          : this.serviceRequestDocument,
      requestId: data.requestId.present ? data.requestId.value : this.requestId,
      latitude: data.latitude.present ? data.latitude.value : this.latitude,
      longitude: data.longitude.present ? data.longitude.value : this.longitude,
      refId: data.refId.present ? data.refId.value : this.refId,
      safety: data.safety.present ? data.safety.value : this.safety,
    );
  }

  @override
  String toString() {
    return (StringBuffer('RepairData(')
          ..write('repairId: $repairId, ')
          ..write('customerId: $customerId, ')
          ..write('equipmentId: $equipmentId, ')
          ..write('currentLocationId: $currentLocationId, ')
          ..write('newLocationId: $newLocationId, ')
          ..write('createdBy: $createdBy, ')
          ..write('createdAt: $createdAt, ')
          ..write('status: $status, ')
          ..write('extn: $extn, ')
          ..write('parts: $parts, ')
          ..write('repairDocument: $repairDocument, ')
          ..write('serviceRequestDocument: $serviceRequestDocument, ')
          ..write('requestId: $requestId, ')
          ..write('latitude: $latitude, ')
          ..write('longitude: $longitude, ')
          ..write('refId: $refId, ')
          ..write('safety: $safety')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(
      repairId,
      customerId,
      equipmentId,
      currentLocationId,
      newLocationId,
      createdBy,
      createdAt,
      status,
      extn,
      parts,
      repairDocument,
      serviceRequestDocument,
      requestId,
      latitude,
      longitude,
      refId,
      safety);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is RepairData &&
          other.repairId == this.repairId &&
          other.customerId == this.customerId &&
          other.equipmentId == this.equipmentId &&
          other.currentLocationId == this.currentLocationId &&
          other.newLocationId == this.newLocationId &&
          other.createdBy == this.createdBy &&
          other.createdAt == this.createdAt &&
          other.status == this.status &&
          other.extn == this.extn &&
          other.parts == this.parts &&
          other.repairDocument == this.repairDocument &&
          other.serviceRequestDocument == this.serviceRequestDocument &&
          other.requestId == this.requestId &&
          other.latitude == this.latitude &&
          other.longitude == this.longitude &&
          other.refId == this.refId &&
          other.safety == this.safety);
}

class RepairCompanion extends UpdateCompanion<RepairData> {
  final Value<int> repairId;
  final Value<int?> customerId;
  final Value<String?> equipmentId;
  final Value<String?> currentLocationId;
  final Value<String?> newLocationId;
  final Value<int?> createdBy;
  final Value<DateTime?> createdAt;
  final Value<int?> status;
  final Value<String?> extn;
  final Value<String?> parts;
  final Value<String?> repairDocument;
  final Value<String?> serviceRequestDocument;
  final Value<int?> requestId;
  final Value<double?> latitude;
  final Value<double?> longitude;
  final Value<String?> refId;
  final Value<bool?> safety;
  const RepairCompanion({
    this.repairId = const Value.absent(),
    this.customerId = const Value.absent(),
    this.equipmentId = const Value.absent(),
    this.currentLocationId = const Value.absent(),
    this.newLocationId = const Value.absent(),
    this.createdBy = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.status = const Value.absent(),
    this.extn = const Value.absent(),
    this.parts = const Value.absent(),
    this.repairDocument = const Value.absent(),
    this.serviceRequestDocument = const Value.absent(),
    this.requestId = const Value.absent(),
    this.latitude = const Value.absent(),
    this.longitude = const Value.absent(),
    this.refId = const Value.absent(),
    this.safety = const Value.absent(),
  });
  RepairCompanion.insert({
    this.repairId = const Value.absent(),
    this.customerId = const Value.absent(),
    this.equipmentId = const Value.absent(),
    this.currentLocationId = const Value.absent(),
    this.newLocationId = const Value.absent(),
    this.createdBy = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.status = const Value.absent(),
    this.extn = const Value.absent(),
    this.parts = const Value.absent(),
    this.repairDocument = const Value.absent(),
    this.serviceRequestDocument = const Value.absent(),
    this.requestId = const Value.absent(),
    this.latitude = const Value.absent(),
    this.longitude = const Value.absent(),
    this.refId = const Value.absent(),
    this.safety = const Value.absent(),
  });
  static Insertable<RepairData> custom({
    Expression<int>? repairId,
    Expression<int>? customerId,
    Expression<String>? equipmentId,
    Expression<String>? currentLocationId,
    Expression<String>? newLocationId,
    Expression<int>? createdBy,
    Expression<DateTime>? createdAt,
    Expression<int>? status,
    Expression<String>? extn,
    Expression<String>? parts,
    Expression<String>? repairDocument,
    Expression<String>? serviceRequestDocument,
    Expression<int>? requestId,
    Expression<double>? latitude,
    Expression<double>? longitude,
    Expression<String>? refId,
    Expression<bool>? safety,
  }) {
    return RawValuesInsertable({
      if (repairId != null) 'REPAIR_ID': repairId,
      if (customerId != null) 'CUSTOMER_ID': customerId,
      if (equipmentId != null) 'EQUIPMENT_ID': equipmentId,
      if (currentLocationId != null) 'CURRENT_LOCATION': currentLocationId,
      if (newLocationId != null) 'NEW_LOCATION': newLocationId,
      if (createdBy != null) 'CREATED_BY': createdBy,
      if (createdAt != null) 'CREATED_AT': createdAt,
      if (status != null) 'STATUS': status,
      if (extn != null) 'EXTN': extn,
      if (parts != null) 'PARTS': parts,
      if (repairDocument != null) 'REPAIR_DOCUMENT': repairDocument,
      if (serviceRequestDocument != null)
        'SERVICE_REQUEST_DOCUMENT': serviceRequestDocument,
      if (requestId != null) 'REQUEST_ID': requestId,
      if (latitude != null) 'LATITUDE': latitude,
      if (longitude != null) 'LONGITUDE': longitude,
      if (refId != null) 'REF_ID': refId,
      if (safety != null) 'SAFETY': safety,
    });
  }

  RepairCompanion copyWith(
      {Value<int>? repairId,
      Value<int?>? customerId,
      Value<String?>? equipmentId,
      Value<String?>? currentLocationId,
      Value<String?>? newLocationId,
      Value<int?>? createdBy,
      Value<DateTime?>? createdAt,
      Value<int?>? status,
      Value<String?>? extn,
      Value<String?>? parts,
      Value<String?>? repairDocument,
      Value<String?>? serviceRequestDocument,
      Value<int?>? requestId,
      Value<double?>? latitude,
      Value<double?>? longitude,
      Value<String?>? refId,
      Value<bool?>? safety}) {
    return RepairCompanion(
      repairId: repairId ?? this.repairId,
      customerId: customerId ?? this.customerId,
      equipmentId: equipmentId ?? this.equipmentId,
      currentLocationId: currentLocationId ?? this.currentLocationId,
      newLocationId: newLocationId ?? this.newLocationId,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      status: status ?? this.status,
      extn: extn ?? this.extn,
      parts: parts ?? this.parts,
      repairDocument: repairDocument ?? this.repairDocument,
      serviceRequestDocument:
          serviceRequestDocument ?? this.serviceRequestDocument,
      requestId: requestId ?? this.requestId,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      refId: refId ?? this.refId,
      safety: safety ?? this.safety,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (repairId.present) {
      map['REPAIR_ID'] = Variable<int>(repairId.value);
    }
    if (customerId.present) {
      map['CUSTOMER_ID'] = Variable<int>(customerId.value);
    }
    if (equipmentId.present) {
      map['EQUIPMENT_ID'] = Variable<String>(equipmentId.value);
    }
    if (currentLocationId.present) {
      map['CURRENT_LOCATION'] = Variable<String>(currentLocationId.value);
    }
    if (newLocationId.present) {
      map['NEW_LOCATION'] = Variable<String>(newLocationId.value);
    }
    if (createdBy.present) {
      map['CREATED_BY'] = Variable<int>(createdBy.value);
    }
    if (createdAt.present) {
      map['CREATED_AT'] = Variable<DateTime>(createdAt.value);
    }
    if (status.present) {
      map['STATUS'] = Variable<int>(status.value);
    }
    if (extn.present) {
      map['EXTN'] = Variable<String>(extn.value);
    }
    if (parts.present) {
      map['PARTS'] = Variable<String>(parts.value);
    }
    if (repairDocument.present) {
      map['REPAIR_DOCUMENT'] = Variable<String>(repairDocument.value);
    }
    if (serviceRequestDocument.present) {
      map['SERVICE_REQUEST_DOCUMENT'] =
          Variable<String>(serviceRequestDocument.value);
    }
    if (requestId.present) {
      map['REQUEST_ID'] = Variable<int>(requestId.value);
    }
    if (latitude.present) {
      map['LATITUDE'] = Variable<double>(latitude.value);
    }
    if (longitude.present) {
      map['LONGITUDE'] = Variable<double>(longitude.value);
    }
    if (refId.present) {
      map['REF_ID'] = Variable<String>(refId.value);
    }
    if (safety.present) {
      map['SAFETY'] = Variable<bool>(safety.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('RepairCompanion(')
          ..write('repairId: $repairId, ')
          ..write('customerId: $customerId, ')
          ..write('equipmentId: $equipmentId, ')
          ..write('currentLocationId: $currentLocationId, ')
          ..write('newLocationId: $newLocationId, ')
          ..write('createdBy: $createdBy, ')
          ..write('createdAt: $createdAt, ')
          ..write('status: $status, ')
          ..write('extn: $extn, ')
          ..write('parts: $parts, ')
          ..write('repairDocument: $repairDocument, ')
          ..write('serviceRequestDocument: $serviceRequestDocument, ')
          ..write('requestId: $requestId, ')
          ..write('latitude: $latitude, ')
          ..write('longitude: $longitude, ')
          ..write('refId: $refId, ')
          ..write('safety: $safety')
          ..write(')'))
        .toString();
  }
}

class $AuditStatusTable extends AuditStatus
    with TableInfo<$AuditStatusTable, AuditStatusData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $AuditStatusTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _auditIdMeta =
      const VerificationMeta('auditId');
  @override
  late final GeneratedColumn<int> auditId = GeneratedColumn<int>(
      'AUDIT_ID', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _equipmentDataMeta =
      const VerificationMeta('equipmentData');
  @override
  late final GeneratedColumnWithTypeConverter<List<Map<String, dynamic>>?,
      String> equipmentData = GeneratedColumn<String>(
          'EQUIPMENT_DATA', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false)
      .withConverter<List<Map<String, dynamic>>?>(
          $AuditStatusTable.$converterequipmentDatan);
  @override
  List<GeneratedColumn> get $columns => [auditId, equipmentData];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'AUDIT_STATUS';
  @override
  VerificationContext validateIntegrity(Insertable<AuditStatusData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('AUDIT_ID')) {
      context.handle(_auditIdMeta,
          auditId.isAcceptableOrUnknown(data['AUDIT_ID']!, _auditIdMeta));
    }
    context.handle(_equipmentDataMeta, const VerificationResult.success());
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {auditId};
  @override
  AuditStatusData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return AuditStatusData(
      auditId: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}AUDIT_ID'])!,
      equipmentData: $AuditStatusTable.$converterequipmentDatan.fromSql(
          attachedDatabase.typeMapping.read(
              DriftSqlType.string, data['${effectivePrefix}EQUIPMENT_DATA'])),
    );
  }

  @override
  $AuditStatusTable createAlias(String alias) {
    return $AuditStatusTable(attachedDatabase, alias);
  }

  static TypeConverter<List<Map<String, dynamic>>, String>
      $converterequipmentData = const AuditJsonConverter();
  static TypeConverter<List<Map<String, dynamic>>?, String?>
      $converterequipmentDatan =
      NullAwareTypeConverter.wrap($converterequipmentData);
}

class AuditStatusData extends DataClass implements Insertable<AuditStatusData> {
  final int auditId;
  final List<Map<String, dynamic>>? equipmentData;
  const AuditStatusData({required this.auditId, this.equipmentData});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['AUDIT_ID'] = Variable<int>(auditId);
    if (!nullToAbsent || equipmentData != null) {
      map['EQUIPMENT_DATA'] = Variable<String>(
          $AuditStatusTable.$converterequipmentDatan.toSql(equipmentData));
    }
    return map;
  }

  AuditStatusCompanion toCompanion(bool nullToAbsent) {
    return AuditStatusCompanion(
      auditId: Value(auditId),
      equipmentData: equipmentData == null && nullToAbsent
          ? const Value.absent()
          : Value(equipmentData),
    );
  }

  factory AuditStatusData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return AuditStatusData(
      auditId: serializer.fromJson<int>(json['auditId']),
      equipmentData: serializer
          .fromJson<List<Map<String, dynamic>>?>(json['equipmentData']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'auditId': serializer.toJson<int>(auditId),
      'equipmentData':
          serializer.toJson<List<Map<String, dynamic>>?>(equipmentData),
    };
  }

  AuditStatusData copyWith(
          {int? auditId,
          Value<List<Map<String, dynamic>>?> equipmentData =
              const Value.absent()}) =>
      AuditStatusData(
        auditId: auditId ?? this.auditId,
        equipmentData:
            equipmentData.present ? equipmentData.value : this.equipmentData,
      );
  AuditStatusData copyWithCompanion(AuditStatusCompanion data) {
    return AuditStatusData(
      auditId: data.auditId.present ? data.auditId.value : this.auditId,
      equipmentData: data.equipmentData.present
          ? data.equipmentData.value
          : this.equipmentData,
    );
  }

  @override
  String toString() {
    return (StringBuffer('AuditStatusData(')
          ..write('auditId: $auditId, ')
          ..write('equipmentData: $equipmentData')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(auditId, equipmentData);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is AuditStatusData &&
          other.auditId == this.auditId &&
          other.equipmentData == this.equipmentData);
}

class AuditStatusCompanion extends UpdateCompanion<AuditStatusData> {
  final Value<int> auditId;
  final Value<List<Map<String, dynamic>>?> equipmentData;
  const AuditStatusCompanion({
    this.auditId = const Value.absent(),
    this.equipmentData = const Value.absent(),
  });
  AuditStatusCompanion.insert({
    this.auditId = const Value.absent(),
    this.equipmentData = const Value.absent(),
  });
  static Insertable<AuditStatusData> custom({
    Expression<int>? auditId,
    Expression<String>? equipmentData,
  }) {
    return RawValuesInsertable({
      if (auditId != null) 'AUDIT_ID': auditId,
      if (equipmentData != null) 'EQUIPMENT_DATA': equipmentData,
    });
  }

  AuditStatusCompanion copyWith(
      {Value<int>? auditId,
      Value<List<Map<String, dynamic>>?>? equipmentData}) {
    return AuditStatusCompanion(
      auditId: auditId ?? this.auditId,
      equipmentData: equipmentData ?? this.equipmentData,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (auditId.present) {
      map['AUDIT_ID'] = Variable<int>(auditId.value);
    }
    if (equipmentData.present) {
      map['EQUIPMENT_DATA'] = Variable<String>($AuditStatusTable
          .$converterequipmentDatan
          .toSql(equipmentData.value));
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('AuditStatusCompanion(')
          ..write('auditId: $auditId, ')
          ..write('equipmentData: $equipmentData')
          ..write(')'))
        .toString();
  }
}

class $TasksTable extends Tasks with TableInfo<$TasksTable, Task> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $TasksTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _TASK_IDMeta =
      const VerificationMeta('TASK_ID');
  @override
  late final GeneratedColumn<int> TASK_ID = GeneratedColumn<int>(
      'TASK_ID', aliasedName, false,
      hasAutoIncrement: true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('PRIMARY KEY AUTOINCREMENT'));
  static const VerificationMeta _EQUIPMENT_IDMeta =
      const VerificationMeta('EQUIPMENT_ID');
  @override
  late final GeneratedColumn<int> EQUIPMENT_ID = GeneratedColumn<int>(
      'EQUIPMENT_ID', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _TASK_TYPEMeta =
      const VerificationMeta('TASK_TYPE');
  @override
  late final GeneratedColumn<String> TASK_TYPE = GeneratedColumn<String>(
      'TASK_TYPE', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  @override
  List<GeneratedColumn> get $columns => [TASK_ID, EQUIPMENT_ID, TASK_TYPE];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'tasks';
  @override
  VerificationContext validateIntegrity(Insertable<Task> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('TASK_ID')) {
      context.handle(_TASK_IDMeta,
          TASK_ID.isAcceptableOrUnknown(data['TASK_ID']!, _TASK_IDMeta));
    }
    if (data.containsKey('EQUIPMENT_ID')) {
      context.handle(
          _EQUIPMENT_IDMeta,
          EQUIPMENT_ID.isAcceptableOrUnknown(
              data['EQUIPMENT_ID']!, _EQUIPMENT_IDMeta));
    } else if (isInserting) {
      context.missing(_EQUIPMENT_IDMeta);
    }
    if (data.containsKey('TASK_TYPE')) {
      context.handle(_TASK_TYPEMeta,
          TASK_TYPE.isAcceptableOrUnknown(data['TASK_TYPE']!, _TASK_TYPEMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {TASK_ID};
  @override
  Task map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return Task(
      TASK_ID: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}TASK_ID'])!,
      EQUIPMENT_ID: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}EQUIPMENT_ID'])!,
      TASK_TYPE: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}TASK_TYPE']),
    );
  }

  @override
  $TasksTable createAlias(String alias) {
    return $TasksTable(attachedDatabase, alias);
  }
}

class Task extends DataClass implements Insertable<Task> {
  final int TASK_ID;
  final int EQUIPMENT_ID;
  final String? TASK_TYPE;
  const Task(
      {required this.TASK_ID, required this.EQUIPMENT_ID, this.TASK_TYPE});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['TASK_ID'] = Variable<int>(TASK_ID);
    map['EQUIPMENT_ID'] = Variable<int>(EQUIPMENT_ID);
    if (!nullToAbsent || TASK_TYPE != null) {
      map['TASK_TYPE'] = Variable<String>(TASK_TYPE);
    }
    return map;
  }

  TasksCompanion toCompanion(bool nullToAbsent) {
    return TasksCompanion(
      TASK_ID: Value(TASK_ID),
      EQUIPMENT_ID: Value(EQUIPMENT_ID),
      TASK_TYPE: TASK_TYPE == null && nullToAbsent
          ? const Value.absent()
          : Value(TASK_TYPE),
    );
  }

  factory Task.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return Task(
      TASK_ID: serializer.fromJson<int>(json['TASK_ID']),
      EQUIPMENT_ID: serializer.fromJson<int>(json['EQUIPMENT_ID']),
      TASK_TYPE: serializer.fromJson<String?>(json['TASK_TYPE']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'TASK_ID': serializer.toJson<int>(TASK_ID),
      'EQUIPMENT_ID': serializer.toJson<int>(EQUIPMENT_ID),
      'TASK_TYPE': serializer.toJson<String?>(TASK_TYPE),
    };
  }

  Task copyWith(
          {int? TASK_ID,
          int? EQUIPMENT_ID,
          Value<String?> TASK_TYPE = const Value.absent()}) =>
      Task(
        TASK_ID: TASK_ID ?? this.TASK_ID,
        EQUIPMENT_ID: EQUIPMENT_ID ?? this.EQUIPMENT_ID,
        TASK_TYPE: TASK_TYPE.present ? TASK_TYPE.value : this.TASK_TYPE,
      );
  Task copyWithCompanion(TasksCompanion data) {
    return Task(
      TASK_ID: data.TASK_ID.present ? data.TASK_ID.value : this.TASK_ID,
      EQUIPMENT_ID: data.EQUIPMENT_ID.present
          ? data.EQUIPMENT_ID.value
          : this.EQUIPMENT_ID,
      TASK_TYPE: data.TASK_TYPE.present ? data.TASK_TYPE.value : this.TASK_TYPE,
    );
  }

  @override
  String toString() {
    return (StringBuffer('Task(')
          ..write('TASK_ID: $TASK_ID, ')
          ..write('EQUIPMENT_ID: $EQUIPMENT_ID, ')
          ..write('TASK_TYPE: $TASK_TYPE')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(TASK_ID, EQUIPMENT_ID, TASK_TYPE);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is Task &&
          other.TASK_ID == this.TASK_ID &&
          other.EQUIPMENT_ID == this.EQUIPMENT_ID &&
          other.TASK_TYPE == this.TASK_TYPE);
}

class TasksCompanion extends UpdateCompanion<Task> {
  final Value<int> TASK_ID;
  final Value<int> EQUIPMENT_ID;
  final Value<String?> TASK_TYPE;
  const TasksCompanion({
    this.TASK_ID = const Value.absent(),
    this.EQUIPMENT_ID = const Value.absent(),
    this.TASK_TYPE = const Value.absent(),
  });
  TasksCompanion.insert({
    this.TASK_ID = const Value.absent(),
    required int EQUIPMENT_ID,
    this.TASK_TYPE = const Value.absent(),
  }) : EQUIPMENT_ID = Value(EQUIPMENT_ID);
  static Insertable<Task> custom({
    Expression<int>? TASK_ID,
    Expression<int>? EQUIPMENT_ID,
    Expression<String>? TASK_TYPE,
  }) {
    return RawValuesInsertable({
      if (TASK_ID != null) 'TASK_ID': TASK_ID,
      if (EQUIPMENT_ID != null) 'EQUIPMENT_ID': EQUIPMENT_ID,
      if (TASK_TYPE != null) 'TASK_TYPE': TASK_TYPE,
    });
  }

  TasksCompanion copyWith(
      {Value<int>? TASK_ID,
      Value<int>? EQUIPMENT_ID,
      Value<String?>? TASK_TYPE}) {
    return TasksCompanion(
      TASK_ID: TASK_ID ?? this.TASK_ID,
      EQUIPMENT_ID: EQUIPMENT_ID ?? this.EQUIPMENT_ID,
      TASK_TYPE: TASK_TYPE ?? this.TASK_TYPE,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (TASK_ID.present) {
      map['TASK_ID'] = Variable<int>(TASK_ID.value);
    }
    if (EQUIPMENT_ID.present) {
      map['EQUIPMENT_ID'] = Variable<int>(EQUIPMENT_ID.value);
    }
    if (TASK_TYPE.present) {
      map['TASK_TYPE'] = Variable<String>(TASK_TYPE.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('TasksCompanion(')
          ..write('TASK_ID: $TASK_ID, ')
          ..write('EQUIPMENT_ID: $EQUIPMENT_ID, ')
          ..write('TASK_TYPE: $TASK_TYPE')
          ..write(')'))
        .toString();
  }
}

abstract class _$Database extends GeneratedDatabase {
  _$Database(QueryExecutor e) : super(e);
  _$Database.connect(DatabaseConnection c) : super.connect(c);
  $DatabaseManager get managers => $DatabaseManager(this);
  late final $ConfigTable config = $ConfigTable(this);
  late final $UserTable user = $UserTable(this);
  late final $ServiceRequestTable serviceRequest = $ServiceRequestTable(this);
  late final $RepairTable repair = $RepairTable(this);
  late final $AuditStatusTable auditStatus = $AuditStatusTable(this);
  late final $TasksTable tasks = $TasksTable(this);
  late final ServiceRequestDao serviceRequestDao =
      ServiceRequestDao(this as Database);
  late final UserDao userDao = UserDao(this as Database);
  late final RepairDao repairDao = RepairDao(this as Database);
  late final AuditStatusDao auditStatusDao = AuditStatusDao(this as Database);
  late final TaskDao taskDao = TaskDao(this as Database);
  Selectable<int> pendingServiceRequestCount() {
    return customSelect(
        'SELECT COUNT(*) AS _c0 FROM SERVICE_REQUEST WHERE IS_QUEUED = TRUE',
        variables: [],
        readsFrom: {
          serviceRequest,
        }).map((QueryRow row) => row.read<int>('_c0'));
  }

  @override
  Iterable<TableInfo<Table, Object?>> get allTables =>
      allSchemaEntities.whereType<TableInfo<Table, Object?>>();
  @override
  List<DatabaseSchemaEntity> get allSchemaEntities =>
      [config, user, serviceRequest, repair, auditStatus, tasks];
}

typedef $$ConfigTableCreateCompanionBuilder = ConfigCompanion Function({
  Value<int> id,
  Value<String?> key,
  Value<String?> value,
});
typedef $$ConfigTableUpdateCompanionBuilder = ConfigCompanion Function({
  Value<int> id,
  Value<String?> key,
  Value<String?> value,
});

class $$ConfigTableTableManager extends RootTableManager<
    _$Database,
    $ConfigTable,
    ConfigData,
    $$ConfigTableFilterComposer,
    $$ConfigTableOrderingComposer,
    $$ConfigTableCreateCompanionBuilder,
    $$ConfigTableUpdateCompanionBuilder> {
  $$ConfigTableTableManager(_$Database db, $ConfigTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          filteringComposer:
              $$ConfigTableFilterComposer(ComposerState(db, table)),
          orderingComposer:
              $$ConfigTableOrderingComposer(ComposerState(db, table)),
          updateCompanionCallback: ({
            Value<int> id = const Value.absent(),
            Value<String?> key = const Value.absent(),
            Value<String?> value = const Value.absent(),
          }) =>
              ConfigCompanion(
            id: id,
            key: key,
            value: value,
          ),
          createCompanionCallback: ({
            Value<int> id = const Value.absent(),
            Value<String?> key = const Value.absent(),
            Value<String?> value = const Value.absent(),
          }) =>
              ConfigCompanion.insert(
            id: id,
            key: key,
            value: value,
          ),
        ));
}

class $$ConfigTableFilterComposer
    extends FilterComposer<_$Database, $ConfigTable> {
  $$ConfigTableFilterComposer(super.$state);
  ColumnFilters<int> get id => $state.composableBuilder(
      column: $state.table.id,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get key => $state.composableBuilder(
      column: $state.table.key,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get value => $state.composableBuilder(
      column: $state.table.value,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));
}

class $$ConfigTableOrderingComposer
    extends OrderingComposer<_$Database, $ConfigTable> {
  $$ConfigTableOrderingComposer(super.$state);
  ColumnOrderings<int> get id => $state.composableBuilder(
      column: $state.table.id,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get key => $state.composableBuilder(
      column: $state.table.key,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get value => $state.composableBuilder(
      column: $state.table.value,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));
}

typedef $$UserTableCreateCompanionBuilder = UserCompanion Function({
  Value<int> USER_ID,
  Value<String?> FIRST_NAME,
  Value<String?> LAST_NAME,
  Value<String?> EMAIL,
  Value<String?> PHONE,
  Value<List<dynamic>?> MODULE,
  Value<bool?> AFS_USER,
  Value<String?> CUSTOMER_TYPE,
});
typedef $$UserTableUpdateCompanionBuilder = UserCompanion Function({
  Value<int> USER_ID,
  Value<String?> FIRST_NAME,
  Value<String?> LAST_NAME,
  Value<String?> EMAIL,
  Value<String?> PHONE,
  Value<List<dynamic>?> MODULE,
  Value<bool?> AFS_USER,
  Value<String?> CUSTOMER_TYPE,
});

class $$UserTableTableManager extends RootTableManager<
    _$Database,
    $UserTable,
    UserData,
    $$UserTableFilterComposer,
    $$UserTableOrderingComposer,
    $$UserTableCreateCompanionBuilder,
    $$UserTableUpdateCompanionBuilder> {
  $$UserTableTableManager(_$Database db, $UserTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          filteringComposer:
              $$UserTableFilterComposer(ComposerState(db, table)),
          orderingComposer:
              $$UserTableOrderingComposer(ComposerState(db, table)),
          updateCompanionCallback: ({
            Value<int> USER_ID = const Value.absent(),
            Value<String?> FIRST_NAME = const Value.absent(),
            Value<String?> LAST_NAME = const Value.absent(),
            Value<String?> EMAIL = const Value.absent(),
            Value<String?> PHONE = const Value.absent(),
            Value<List<dynamic>?> MODULE = const Value.absent(),
            Value<bool?> AFS_USER = const Value.absent(),
            Value<String?> CUSTOMER_TYPE = const Value.absent(),
          }) =>
              UserCompanion(
            USER_ID: USER_ID,
            FIRST_NAME: FIRST_NAME,
            LAST_NAME: LAST_NAME,
            EMAIL: EMAIL,
            PHONE: PHONE,
            MODULE: MODULE,
            AFS_USER: AFS_USER,
            CUSTOMER_TYPE: CUSTOMER_TYPE,
          ),
          createCompanionCallback: ({
            Value<int> USER_ID = const Value.absent(),
            Value<String?> FIRST_NAME = const Value.absent(),
            Value<String?> LAST_NAME = const Value.absent(),
            Value<String?> EMAIL = const Value.absent(),
            Value<String?> PHONE = const Value.absent(),
            Value<List<dynamic>?> MODULE = const Value.absent(),
            Value<bool?> AFS_USER = const Value.absent(),
            Value<String?> CUSTOMER_TYPE = const Value.absent(),
          }) =>
              UserCompanion.insert(
            USER_ID: USER_ID,
            FIRST_NAME: FIRST_NAME,
            LAST_NAME: LAST_NAME,
            EMAIL: EMAIL,
            PHONE: PHONE,
            MODULE: MODULE,
            AFS_USER: AFS_USER,
            CUSTOMER_TYPE: CUSTOMER_TYPE,
          ),
        ));
}

class $$UserTableFilterComposer extends FilterComposer<_$Database, $UserTable> {
  $$UserTableFilterComposer(super.$state);
  ColumnFilters<int> get USER_ID => $state.composableBuilder(
      column: $state.table.USER_ID,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get FIRST_NAME => $state.composableBuilder(
      column: $state.table.FIRST_NAME,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get LAST_NAME => $state.composableBuilder(
      column: $state.table.LAST_NAME,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get EMAIL => $state.composableBuilder(
      column: $state.table.EMAIL,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get PHONE => $state.composableBuilder(
      column: $state.table.PHONE,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnWithTypeConverterFilters<List<dynamic>?, List<dynamic>, String>
      get MODULE => $state.composableBuilder(
          column: $state.table.MODULE,
          builder: (column, joinBuilders) => ColumnWithTypeConverterFilters(
              column,
              joinBuilders: joinBuilders));

  ColumnFilters<bool> get AFS_USER => $state.composableBuilder(
      column: $state.table.AFS_USER,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get CUSTOMER_TYPE => $state.composableBuilder(
      column: $state.table.CUSTOMER_TYPE,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));
}

class $$UserTableOrderingComposer
    extends OrderingComposer<_$Database, $UserTable> {
  $$UserTableOrderingComposer(super.$state);
  ColumnOrderings<int> get USER_ID => $state.composableBuilder(
      column: $state.table.USER_ID,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get FIRST_NAME => $state.composableBuilder(
      column: $state.table.FIRST_NAME,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get LAST_NAME => $state.composableBuilder(
      column: $state.table.LAST_NAME,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get EMAIL => $state.composableBuilder(
      column: $state.table.EMAIL,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get PHONE => $state.composableBuilder(
      column: $state.table.PHONE,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get MODULE => $state.composableBuilder(
      column: $state.table.MODULE,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<bool> get AFS_USER => $state.composableBuilder(
      column: $state.table.AFS_USER,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get CUSTOMER_TYPE => $state.composableBuilder(
      column: $state.table.CUSTOMER_TYPE,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));
}

typedef $$ServiceRequestTableCreateCompanionBuilder = ServiceRequestCompanion
    Function({
  Value<int> requestId,
  Value<int?> customerId,
  Value<String?> equipmentId,
  Value<String?> requestType,
  Value<String?> locationId,
  Value<String?> newLocationId,
  Value<String?> requestChoiceType,
  Value<String?> description,
  Value<int?> createdBy,
  Value<DateTime?> createdAt,
  Value<int?> status,
  Value<String?> document,
  Value<String?> extn,
  Value<String?> parts,
  Value<String?> newBarcode,
  Value<String?> equipmentName,
  Value<String?> equipmentCategory,
  Value<String?> equipmentBarcodeNumber,
  Value<int?> clusterId,
  Value<int?> choiceId,
  Value<String?> repairDocument,
  Value<int?> auditId,
  Value<double?> latitude,
  Value<double?> longitude,
  Value<String?> refId,
  Value<bool?> safety,
});
typedef $$ServiceRequestTableUpdateCompanionBuilder = ServiceRequestCompanion
    Function({
  Value<int> requestId,
  Value<int?> customerId,
  Value<String?> equipmentId,
  Value<String?> requestType,
  Value<String?> locationId,
  Value<String?> newLocationId,
  Value<String?> requestChoiceType,
  Value<String?> description,
  Value<int?> createdBy,
  Value<DateTime?> createdAt,
  Value<int?> status,
  Value<String?> document,
  Value<String?> extn,
  Value<String?> parts,
  Value<String?> newBarcode,
  Value<String?> equipmentName,
  Value<String?> equipmentCategory,
  Value<String?> equipmentBarcodeNumber,
  Value<int?> clusterId,
  Value<int?> choiceId,
  Value<String?> repairDocument,
  Value<int?> auditId,
  Value<double?> latitude,
  Value<double?> longitude,
  Value<String?> refId,
  Value<bool?> safety,
});

class $$ServiceRequestTableTableManager extends RootTableManager<
    _$Database,
    $ServiceRequestTable,
    ServiceRequestData,
    $$ServiceRequestTableFilterComposer,
    $$ServiceRequestTableOrderingComposer,
    $$ServiceRequestTableCreateCompanionBuilder,
    $$ServiceRequestTableUpdateCompanionBuilder> {
  $$ServiceRequestTableTableManager(_$Database db, $ServiceRequestTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          filteringComposer:
              $$ServiceRequestTableFilterComposer(ComposerState(db, table)),
          orderingComposer:
              $$ServiceRequestTableOrderingComposer(ComposerState(db, table)),
          updateCompanionCallback: ({
            Value<int> requestId = const Value.absent(),
            Value<int?> customerId = const Value.absent(),
            Value<String?> equipmentId = const Value.absent(),
            Value<String?> requestType = const Value.absent(),
            Value<String?> locationId = const Value.absent(),
            Value<String?> newLocationId = const Value.absent(),
            Value<String?> requestChoiceType = const Value.absent(),
            Value<String?> description = const Value.absent(),
            Value<int?> createdBy = const Value.absent(),
            Value<DateTime?> createdAt = const Value.absent(),
            Value<int?> status = const Value.absent(),
            Value<String?> document = const Value.absent(),
            Value<String?> extn = const Value.absent(),
            Value<String?> parts = const Value.absent(),
            Value<String?> newBarcode = const Value.absent(),
            Value<String?> equipmentName = const Value.absent(),
            Value<String?> equipmentCategory = const Value.absent(),
            Value<String?> equipmentBarcodeNumber = const Value.absent(),
            Value<int?> clusterId = const Value.absent(),
            Value<int?> choiceId = const Value.absent(),
            Value<String?> repairDocument = const Value.absent(),
            Value<int?> auditId = const Value.absent(),
            Value<double?> latitude = const Value.absent(),
            Value<double?> longitude = const Value.absent(),
            Value<String?> refId = const Value.absent(),
            Value<bool?> safety = const Value.absent(),
          }) =>
              ServiceRequestCompanion(
            requestId: requestId,
            customerId: customerId,
            equipmentId: equipmentId,
            requestType: requestType,
            locationId: locationId,
            newLocationId: newLocationId,
            requestChoiceType: requestChoiceType,
            description: description,
            createdBy: createdBy,
            createdAt: createdAt,
            status: status,
            document: document,
            extn: extn,
            parts: parts,
            newBarcode: newBarcode,
            equipmentName: equipmentName,
            equipmentCategory: equipmentCategory,
            equipmentBarcodeNumber: equipmentBarcodeNumber,
            clusterId: clusterId,
            choiceId: choiceId,
            repairDocument: repairDocument,
            auditId: auditId,
            latitude: latitude,
            longitude: longitude,
            refId: refId,
            safety: safety,
          ),
          createCompanionCallback: ({
            Value<int> requestId = const Value.absent(),
            Value<int?> customerId = const Value.absent(),
            Value<String?> equipmentId = const Value.absent(),
            Value<String?> requestType = const Value.absent(),
            Value<String?> locationId = const Value.absent(),
            Value<String?> newLocationId = const Value.absent(),
            Value<String?> requestChoiceType = const Value.absent(),
            Value<String?> description = const Value.absent(),
            Value<int?> createdBy = const Value.absent(),
            Value<DateTime?> createdAt = const Value.absent(),
            Value<int?> status = const Value.absent(),
            Value<String?> document = const Value.absent(),
            Value<String?> extn = const Value.absent(),
            Value<String?> parts = const Value.absent(),
            Value<String?> newBarcode = const Value.absent(),
            Value<String?> equipmentName = const Value.absent(),
            Value<String?> equipmentCategory = const Value.absent(),
            Value<String?> equipmentBarcodeNumber = const Value.absent(),
            Value<int?> clusterId = const Value.absent(),
            Value<int?> choiceId = const Value.absent(),
            Value<String?> repairDocument = const Value.absent(),
            Value<int?> auditId = const Value.absent(),
            Value<double?> latitude = const Value.absent(),
            Value<double?> longitude = const Value.absent(),
            Value<String?> refId = const Value.absent(),
            Value<bool?> safety = const Value.absent(),
          }) =>
              ServiceRequestCompanion.insert(
            requestId: requestId,
            customerId: customerId,
            equipmentId: equipmentId,
            requestType: requestType,
            locationId: locationId,
            newLocationId: newLocationId,
            requestChoiceType: requestChoiceType,
            description: description,
            createdBy: createdBy,
            createdAt: createdAt,
            status: status,
            document: document,
            extn: extn,
            parts: parts,
            newBarcode: newBarcode,
            equipmentName: equipmentName,
            equipmentCategory: equipmentCategory,
            equipmentBarcodeNumber: equipmentBarcodeNumber,
            clusterId: clusterId,
            choiceId: choiceId,
            repairDocument: repairDocument,
            auditId: auditId,
            latitude: latitude,
            longitude: longitude,
            refId: refId,
            safety: safety,
          ),
        ));
}

class $$ServiceRequestTableFilterComposer
    extends FilterComposer<_$Database, $ServiceRequestTable> {
  $$ServiceRequestTableFilterComposer(super.$state);
  ColumnFilters<int> get requestId => $state.composableBuilder(
      column: $state.table.requestId,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<int> get customerId => $state.composableBuilder(
      column: $state.table.customerId,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get equipmentId => $state.composableBuilder(
      column: $state.table.equipmentId,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get requestType => $state.composableBuilder(
      column: $state.table.requestType,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get locationId => $state.composableBuilder(
      column: $state.table.locationId,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get newLocationId => $state.composableBuilder(
      column: $state.table.newLocationId,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get requestChoiceType => $state.composableBuilder(
      column: $state.table.requestChoiceType,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get description => $state.composableBuilder(
      column: $state.table.description,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<int> get createdBy => $state.composableBuilder(
      column: $state.table.createdBy,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<DateTime> get createdAt => $state.composableBuilder(
      column: $state.table.createdAt,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<int> get status => $state.composableBuilder(
      column: $state.table.status,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get document => $state.composableBuilder(
      column: $state.table.document,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get extn => $state.composableBuilder(
      column: $state.table.extn,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get parts => $state.composableBuilder(
      column: $state.table.parts,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get newBarcode => $state.composableBuilder(
      column: $state.table.newBarcode,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get equipmentName => $state.composableBuilder(
      column: $state.table.equipmentName,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get equipmentCategory => $state.composableBuilder(
      column: $state.table.equipmentCategory,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get equipmentBarcodeNumber => $state.composableBuilder(
      column: $state.table.equipmentBarcodeNumber,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<int> get clusterId => $state.composableBuilder(
      column: $state.table.clusterId,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<int> get choiceId => $state.composableBuilder(
      column: $state.table.choiceId,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get repairDocument => $state.composableBuilder(
      column: $state.table.repairDocument,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<int> get auditId => $state.composableBuilder(
      column: $state.table.auditId,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<double> get latitude => $state.composableBuilder(
      column: $state.table.latitude,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<double> get longitude => $state.composableBuilder(
      column: $state.table.longitude,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get refId => $state.composableBuilder(
      column: $state.table.refId,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<bool> get safety => $state.composableBuilder(
      column: $state.table.safety,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));
}

class $$ServiceRequestTableOrderingComposer
    extends OrderingComposer<_$Database, $ServiceRequestTable> {
  $$ServiceRequestTableOrderingComposer(super.$state);
  ColumnOrderings<int> get requestId => $state.composableBuilder(
      column: $state.table.requestId,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<int> get customerId => $state.composableBuilder(
      column: $state.table.customerId,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get equipmentId => $state.composableBuilder(
      column: $state.table.equipmentId,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get requestType => $state.composableBuilder(
      column: $state.table.requestType,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get locationId => $state.composableBuilder(
      column: $state.table.locationId,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get newLocationId => $state.composableBuilder(
      column: $state.table.newLocationId,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get requestChoiceType => $state.composableBuilder(
      column: $state.table.requestChoiceType,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get description => $state.composableBuilder(
      column: $state.table.description,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<int> get createdBy => $state.composableBuilder(
      column: $state.table.createdBy,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<DateTime> get createdAt => $state.composableBuilder(
      column: $state.table.createdAt,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<int> get status => $state.composableBuilder(
      column: $state.table.status,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get document => $state.composableBuilder(
      column: $state.table.document,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get extn => $state.composableBuilder(
      column: $state.table.extn,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get parts => $state.composableBuilder(
      column: $state.table.parts,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get newBarcode => $state.composableBuilder(
      column: $state.table.newBarcode,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get equipmentName => $state.composableBuilder(
      column: $state.table.equipmentName,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get equipmentCategory => $state.composableBuilder(
      column: $state.table.equipmentCategory,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get equipmentBarcodeNumber =>
      $state.composableBuilder(
          column: $state.table.equipmentBarcodeNumber,
          builder: (column, joinBuilders) =>
              ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<int> get clusterId => $state.composableBuilder(
      column: $state.table.clusterId,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<int> get choiceId => $state.composableBuilder(
      column: $state.table.choiceId,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get repairDocument => $state.composableBuilder(
      column: $state.table.repairDocument,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<int> get auditId => $state.composableBuilder(
      column: $state.table.auditId,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<double> get latitude => $state.composableBuilder(
      column: $state.table.latitude,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<double> get longitude => $state.composableBuilder(
      column: $state.table.longitude,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get refId => $state.composableBuilder(
      column: $state.table.refId,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<bool> get safety => $state.composableBuilder(
      column: $state.table.safety,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));
}

typedef $$RepairTableCreateCompanionBuilder = RepairCompanion Function({
  Value<int> repairId,
  Value<int?> customerId,
  Value<String?> equipmentId,
  Value<String?> currentLocationId,
  Value<String?> newLocationId,
  Value<int?> createdBy,
  Value<DateTime?> createdAt,
  Value<int?> status,
  Value<String?> extn,
  Value<String?> parts,
  Value<String?> repairDocument,
  Value<String?> serviceRequestDocument,
  Value<int?> requestId,
  Value<double?> latitude,
  Value<double?> longitude,
  Value<String?> refId,
  Value<bool?> safety,
});
typedef $$RepairTableUpdateCompanionBuilder = RepairCompanion Function({
  Value<int> repairId,
  Value<int?> customerId,
  Value<String?> equipmentId,
  Value<String?> currentLocationId,
  Value<String?> newLocationId,
  Value<int?> createdBy,
  Value<DateTime?> createdAt,
  Value<int?> status,
  Value<String?> extn,
  Value<String?> parts,
  Value<String?> repairDocument,
  Value<String?> serviceRequestDocument,
  Value<int?> requestId,
  Value<double?> latitude,
  Value<double?> longitude,
  Value<String?> refId,
  Value<bool?> safety,
});

class $$RepairTableTableManager extends RootTableManager<
    _$Database,
    $RepairTable,
    RepairData,
    $$RepairTableFilterComposer,
    $$RepairTableOrderingComposer,
    $$RepairTableCreateCompanionBuilder,
    $$RepairTableUpdateCompanionBuilder> {
  $$RepairTableTableManager(_$Database db, $RepairTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          filteringComposer:
              $$RepairTableFilterComposer(ComposerState(db, table)),
          orderingComposer:
              $$RepairTableOrderingComposer(ComposerState(db, table)),
          updateCompanionCallback: ({
            Value<int> repairId = const Value.absent(),
            Value<int?> customerId = const Value.absent(),
            Value<String?> equipmentId = const Value.absent(),
            Value<String?> currentLocationId = const Value.absent(),
            Value<String?> newLocationId = const Value.absent(),
            Value<int?> createdBy = const Value.absent(),
            Value<DateTime?> createdAt = const Value.absent(),
            Value<int?> status = const Value.absent(),
            Value<String?> extn = const Value.absent(),
            Value<String?> parts = const Value.absent(),
            Value<String?> repairDocument = const Value.absent(),
            Value<String?> serviceRequestDocument = const Value.absent(),
            Value<int?> requestId = const Value.absent(),
            Value<double?> latitude = const Value.absent(),
            Value<double?> longitude = const Value.absent(),
            Value<String?> refId = const Value.absent(),
            Value<bool?> safety = const Value.absent(),
          }) =>
              RepairCompanion(
            repairId: repairId,
            customerId: customerId,
            equipmentId: equipmentId,
            currentLocationId: currentLocationId,
            newLocationId: newLocationId,
            createdBy: createdBy,
            createdAt: createdAt,
            status: status,
            extn: extn,
            parts: parts,
            repairDocument: repairDocument,
            serviceRequestDocument: serviceRequestDocument,
            requestId: requestId,
            latitude: latitude,
            longitude: longitude,
            refId: refId,
            safety: safety,
          ),
          createCompanionCallback: ({
            Value<int> repairId = const Value.absent(),
            Value<int?> customerId = const Value.absent(),
            Value<String?> equipmentId = const Value.absent(),
            Value<String?> currentLocationId = const Value.absent(),
            Value<String?> newLocationId = const Value.absent(),
            Value<int?> createdBy = const Value.absent(),
            Value<DateTime?> createdAt = const Value.absent(),
            Value<int?> status = const Value.absent(),
            Value<String?> extn = const Value.absent(),
            Value<String?> parts = const Value.absent(),
            Value<String?> repairDocument = const Value.absent(),
            Value<String?> serviceRequestDocument = const Value.absent(),
            Value<int?> requestId = const Value.absent(),
            Value<double?> latitude = const Value.absent(),
            Value<double?> longitude = const Value.absent(),
            Value<String?> refId = const Value.absent(),
            Value<bool?> safety = const Value.absent(),
          }) =>
              RepairCompanion.insert(
            repairId: repairId,
            customerId: customerId,
            equipmentId: equipmentId,
            currentLocationId: currentLocationId,
            newLocationId: newLocationId,
            createdBy: createdBy,
            createdAt: createdAt,
            status: status,
            extn: extn,
            parts: parts,
            repairDocument: repairDocument,
            serviceRequestDocument: serviceRequestDocument,
            requestId: requestId,
            latitude: latitude,
            longitude: longitude,
            refId: refId,
            safety: safety,
          ),
        ));
}

class $$RepairTableFilterComposer
    extends FilterComposer<_$Database, $RepairTable> {
  $$RepairTableFilterComposer(super.$state);
  ColumnFilters<int> get repairId => $state.composableBuilder(
      column: $state.table.repairId,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<int> get customerId => $state.composableBuilder(
      column: $state.table.customerId,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get equipmentId => $state.composableBuilder(
      column: $state.table.equipmentId,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get currentLocationId => $state.composableBuilder(
      column: $state.table.currentLocationId,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get newLocationId => $state.composableBuilder(
      column: $state.table.newLocationId,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<int> get createdBy => $state.composableBuilder(
      column: $state.table.createdBy,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<DateTime> get createdAt => $state.composableBuilder(
      column: $state.table.createdAt,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<int> get status => $state.composableBuilder(
      column: $state.table.status,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get extn => $state.composableBuilder(
      column: $state.table.extn,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get parts => $state.composableBuilder(
      column: $state.table.parts,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get repairDocument => $state.composableBuilder(
      column: $state.table.repairDocument,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get serviceRequestDocument => $state.composableBuilder(
      column: $state.table.serviceRequestDocument,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<int> get requestId => $state.composableBuilder(
      column: $state.table.requestId,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<double> get latitude => $state.composableBuilder(
      column: $state.table.latitude,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<double> get longitude => $state.composableBuilder(
      column: $state.table.longitude,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get refId => $state.composableBuilder(
      column: $state.table.refId,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<bool> get safety => $state.composableBuilder(
      column: $state.table.safety,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));
}

class $$RepairTableOrderingComposer
    extends OrderingComposer<_$Database, $RepairTable> {
  $$RepairTableOrderingComposer(super.$state);
  ColumnOrderings<int> get repairId => $state.composableBuilder(
      column: $state.table.repairId,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<int> get customerId => $state.composableBuilder(
      column: $state.table.customerId,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get equipmentId => $state.composableBuilder(
      column: $state.table.equipmentId,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get currentLocationId => $state.composableBuilder(
      column: $state.table.currentLocationId,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get newLocationId => $state.composableBuilder(
      column: $state.table.newLocationId,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<int> get createdBy => $state.composableBuilder(
      column: $state.table.createdBy,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<DateTime> get createdAt => $state.composableBuilder(
      column: $state.table.createdAt,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<int> get status => $state.composableBuilder(
      column: $state.table.status,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get extn => $state.composableBuilder(
      column: $state.table.extn,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get parts => $state.composableBuilder(
      column: $state.table.parts,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get repairDocument => $state.composableBuilder(
      column: $state.table.repairDocument,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get serviceRequestDocument =>
      $state.composableBuilder(
          column: $state.table.serviceRequestDocument,
          builder: (column, joinBuilders) =>
              ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<int> get requestId => $state.composableBuilder(
      column: $state.table.requestId,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<double> get latitude => $state.composableBuilder(
      column: $state.table.latitude,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<double> get longitude => $state.composableBuilder(
      column: $state.table.longitude,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get refId => $state.composableBuilder(
      column: $state.table.refId,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<bool> get safety => $state.composableBuilder(
      column: $state.table.safety,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));
}

typedef $$AuditStatusTableCreateCompanionBuilder = AuditStatusCompanion
    Function({
  Value<int> auditId,
  Value<List<Map<String, dynamic>>?> equipmentData,
});
typedef $$AuditStatusTableUpdateCompanionBuilder = AuditStatusCompanion
    Function({
  Value<int> auditId,
  Value<List<Map<String, dynamic>>?> equipmentData,
});

class $$AuditStatusTableTableManager extends RootTableManager<
    _$Database,
    $AuditStatusTable,
    AuditStatusData,
    $$AuditStatusTableFilterComposer,
    $$AuditStatusTableOrderingComposer,
    $$AuditStatusTableCreateCompanionBuilder,
    $$AuditStatusTableUpdateCompanionBuilder> {
  $$AuditStatusTableTableManager(_$Database db, $AuditStatusTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          filteringComposer:
              $$AuditStatusTableFilterComposer(ComposerState(db, table)),
          orderingComposer:
              $$AuditStatusTableOrderingComposer(ComposerState(db, table)),
          updateCompanionCallback: ({
            Value<int> auditId = const Value.absent(),
            Value<List<Map<String, dynamic>>?> equipmentData =
                const Value.absent(),
          }) =>
              AuditStatusCompanion(
            auditId: auditId,
            equipmentData: equipmentData,
          ),
          createCompanionCallback: ({
            Value<int> auditId = const Value.absent(),
            Value<List<Map<String, dynamic>>?> equipmentData =
                const Value.absent(),
          }) =>
              AuditStatusCompanion.insert(
            auditId: auditId,
            equipmentData: equipmentData,
          ),
        ));
}

class $$AuditStatusTableFilterComposer
    extends FilterComposer<_$Database, $AuditStatusTable> {
  $$AuditStatusTableFilterComposer(super.$state);
  ColumnFilters<int> get auditId => $state.composableBuilder(
      column: $state.table.auditId,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnWithTypeConverterFilters<List<Map<String, dynamic>>?,
          List<Map<String, dynamic>>, String>
      get equipmentData => $state.composableBuilder(
          column: $state.table.equipmentData,
          builder: (column, joinBuilders) => ColumnWithTypeConverterFilters(
              column,
              joinBuilders: joinBuilders));
}

class $$AuditStatusTableOrderingComposer
    extends OrderingComposer<_$Database, $AuditStatusTable> {
  $$AuditStatusTableOrderingComposer(super.$state);
  ColumnOrderings<int> get auditId => $state.composableBuilder(
      column: $state.table.auditId,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get equipmentData => $state.composableBuilder(
      column: $state.table.equipmentData,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));
}

typedef $$TasksTableCreateCompanionBuilder = TasksCompanion Function({
  Value<int> TASK_ID,
  required int EQUIPMENT_ID,
  Value<String?> TASK_TYPE,
});
typedef $$TasksTableUpdateCompanionBuilder = TasksCompanion Function({
  Value<int> TASK_ID,
  Value<int> EQUIPMENT_ID,
  Value<String?> TASK_TYPE,
});

class $$TasksTableTableManager extends RootTableManager<
    _$Database,
    $TasksTable,
    Task,
    $$TasksTableFilterComposer,
    $$TasksTableOrderingComposer,
    $$TasksTableCreateCompanionBuilder,
    $$TasksTableUpdateCompanionBuilder> {
  $$TasksTableTableManager(_$Database db, $TasksTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          filteringComposer:
              $$TasksTableFilterComposer(ComposerState(db, table)),
          orderingComposer:
              $$TasksTableOrderingComposer(ComposerState(db, table)),
          updateCompanionCallback: ({
            Value<int> TASK_ID = const Value.absent(),
            Value<int> EQUIPMENT_ID = const Value.absent(),
            Value<String?> TASK_TYPE = const Value.absent(),
          }) =>
              TasksCompanion(
            TASK_ID: TASK_ID,
            EQUIPMENT_ID: EQUIPMENT_ID,
            TASK_TYPE: TASK_TYPE,
          ),
          createCompanionCallback: ({
            Value<int> TASK_ID = const Value.absent(),
            required int EQUIPMENT_ID,
            Value<String?> TASK_TYPE = const Value.absent(),
          }) =>
              TasksCompanion.insert(
            TASK_ID: TASK_ID,
            EQUIPMENT_ID: EQUIPMENT_ID,
            TASK_TYPE: TASK_TYPE,
          ),
        ));
}

class $$TasksTableFilterComposer
    extends FilterComposer<_$Database, $TasksTable> {
  $$TasksTableFilterComposer(super.$state);
  ColumnFilters<int> get TASK_ID => $state.composableBuilder(
      column: $state.table.TASK_ID,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<int> get EQUIPMENT_ID => $state.composableBuilder(
      column: $state.table.EQUIPMENT_ID,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get TASK_TYPE => $state.composableBuilder(
      column: $state.table.TASK_TYPE,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));
}

class $$TasksTableOrderingComposer
    extends OrderingComposer<_$Database, $TasksTable> {
  $$TasksTableOrderingComposer(super.$state);
  ColumnOrderings<int> get TASK_ID => $state.composableBuilder(
      column: $state.table.TASK_ID,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<int> get EQUIPMENT_ID => $state.composableBuilder(
      column: $state.table.EQUIPMENT_ID,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get TASK_TYPE => $state.composableBuilder(
      column: $state.table.TASK_TYPE,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));
}

class $DatabaseManager {
  final _$Database _db;
  $DatabaseManager(this._db);
  $$ConfigTableTableManager get config =>
      $$ConfigTableTableManager(_db, _db.config);
  $$UserTableTableManager get user => $$UserTableTableManager(_db, _db.user);
  $$ServiceRequestTableTableManager get serviceRequest =>
      $$ServiceRequestTableTableManager(_db, _db.serviceRequest);
  $$RepairTableTableManager get repair =>
      $$RepairTableTableManager(_db, _db.repair);
  $$AuditStatusTableTableManager get auditStatus =>
      $$AuditStatusTableTableManager(_db, _db.auditStatus);
  $$TasksTableTableManager get tasks =>
      $$TasksTableTableManager(_db, _db.tasks);
}
