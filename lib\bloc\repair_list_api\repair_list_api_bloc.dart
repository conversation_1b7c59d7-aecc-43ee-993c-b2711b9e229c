import 'dart:async';

import 'package:alink/data/model/repair_service_request.dart';
import 'package:alink/data/repository/api_repository.dart';
import 'package:alink/util/app_constant.dart';
import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';

part 'repair_list_api_event.dart';
part 'repair_list_api_state.dart';

class RepairListApiBloc extends Bloc<RepairListApiEvent, RepairListApiState> {
  final ApiRepository apiRepository;
  RepairListApiBloc({required this.apiRepository})
      : super(RepairListApiInitial());
  int offset = 0;

  @override
  Stream<RepairListApiState> mapEventToState(
    RepairListApiEvent event,
  ) async* {
    if (event is FetchRepairServiceRequestList) {
      print('================CALLED======================');
      if (state is FetchingRepairServiceRequestList) return;

      final currentState = state;

      List<RepairServiceRequest>? oldPosts = <RepairServiceRequest>[];
      if (currentState is FetchedRepairServiceRequestList) {
        oldPosts =
            currentState.repairServiceRequestList.repairServiceRequestList;
        if (event.refresh == true) {
          oldPosts = [];
        }
      }
      if (event.refresh == true) {
        offset = 0;
      }

      emit(FetchingRepairServiceRequestList(oldPosts!,
          isFirstFetch: offset == 0));

      apiRepository
          .fetchRepairServiceRequestList(offset, event.query)
          .then((response) {
        offset += AppConstant.LIMIT;

        final posts = (state as FetchingRepairServiceRequestList).oldList;
        if (response is RepairServiceRequestResponse) {
          //posts.addAll(response);
          posts.addAll(response.repairServiceRequestList!);
          //response.repairServiceRequestList!;
          response.repairServiceRequestList = posts;
          emit(FetchedRepairServiceRequestList(response));
        } else {
          emit(FetchedRepairServiceError(errorMessage: response));
        }
      }).catchError((onError) {
        emit(FetchedRepairServiceError(errorMessage: onError));
      });
    }
    if (event is FetchLopaRepairServiceRequestList) {
      emit(FetchingLopaRepairServiceRequestList());

      apiRepository
          .fetchLopaRepairServiceRequestList(event.query)
          .then((response) {
        if (response is List<RepairServiceRequest>) {
          response.insert(0, defaultRepairServiceRequest());
          emit(FetchedLopaRepairServiceRequestList(response));
        } else {
          emit(FetchedLopaRepairServiceError(errorMessage: response));
        }
      }).catchError((onError) {
        emit(FetchedLopaRepairServiceError(errorMessage: onError));
      });
    }
  }
}
