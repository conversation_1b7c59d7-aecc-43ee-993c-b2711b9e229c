import 'dart:async';

import 'package:alink/bloc/part_items/part_items_bloc.dart';
import 'package:alink/bloc/part_items/selected_part_item_bloc.dart';
import 'package:alink/data/model/part_item_response.dart';
import 'package:alink/pages/auth/login_page.dart';
import 'package:alink/util/app_color.dart';
import 'package:alink/util/application_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import '../../../data/model/singleton_model.dart';
import '../../../logger/logger.dart';

class SearchItemPartPage extends StatefulWidget {
  static const String routeName = "search-item-part";
  final SearchItemParam singleSelectionParam;

  const SearchItemPartPage({Key? key, required this.singleSelectionParam})
      : super(key: key);

  @override
  _SearchItemPartPageState createState() => _SearchItemPartPageState();
}

class _SearchItemPartPageState extends State<SearchItemPartPage> {
  static const String className = '_SearchItemPartPageState';

  List<PartItemResponse> parts = [];
  final scrollController = ScrollController();
  final _controller = TextEditingController();
  Timer? _debounce;

  @override
  void initState() {
    if (widget.singleSelectionParam.bomId != null) {
      partItemsBloc.add(FetchPartItemList(
          refresh: true, query: "", partId: widget.singleSelectionParam.bomId));
    } else {
      partItemsBloc.add(FetchPartItemList(refresh: true, query: ""));
      super.initState();
    }
  }

  PartItemsBloc get partItemsBloc => BlocProvider.of<PartItemsBloc>(context);

  SelectedPartItemBloc get selectedPartBloc =>
      BlocProvider.of<SelectedPartItemBloc>(context);

  void setupScrollController(context) {
    if (widget.singleSelectionParam.bomId == null) {
      scrollController.addListener(() {
        if (scrollController.position.atEdge) {
          if (scrollController.position.pixels != 0) {
            partItemsBloc.add(FetchPartItemList(
                refresh: false,
                query: "",
                partId: widget.singleSelectionParam.bomId ?? ""));
          }
        }
      });
    }
  }

  _onSearchChanged(String value) {
    if (_debounce?.isActive ?? false) _debounce?.cancel();
    _debounce = Timer(const Duration(milliseconds: 500), () {
      // do something with query
      /*_controller.text = value;
                    if (value.isEmpty) {
                      setState(() {});
                    }*/

      partItemsBloc.add(FetchPartItemList(
          refresh: true,
          query: value,
          partId: widget.singleSelectionParam.bomId ?? ""));
    });
  }

  @override
  void dispose() {
    _debounce?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    Logger.i("Class Name: " + className);
    setupScrollController(context);
    return Scaffold(
      body: SafeArea(
        child: Center(
          child: Container(
            constraints: const BoxConstraints(maxWidth: 500),
            child: Column(
              children: [
                ApplicationUtil.displayNotificationWidgetIfExist(
                    context, SearchItemPartPage.routeName),
                !widget.singleSelectionParam.isSingleSelection
                    ? _getItemPartAppBar()
                    : const SizedBox(),
                const SizedBox(
                  height: 10,
                ),
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 15),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(15),
                    border: Border.all(color: AppColor.greyBorderColor),
                  ),
                  child: TextField(
                    controller: _controller,
                    style: const TextStyle(fontSize: 18, height: 1.2),
                    decoration: InputDecoration(
                      border: InputBorder.none,
                      prefixIcon: const Icon(Icons.search),
                      /*suffixIcon: IconButton(
                          onPressed: () {
                            _controller.text = '';
                            setState(() {});
                          },
                          icon: const Icon(Icons.clear_rounded)),*/
                      hintStyle: TextStyle(
                        color: Colors.grey[500],
                        fontSize: 18,
                      ),
                      hintText: AppLocalizations.of(context)!.searchParts,
                    ),
                    onChanged: (value) {
                      _onSearchChanged(value);
                    },
                  ),
                ),
                Expanded(
                    child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 15),
                  child: BlocConsumer<PartItemsBloc, PartItemsState>(
                    listener: (context, state) {
                      if (state is PartItemsError) {
                        if (state.errorMessage == ApiResponse.INVALID_AUTH) {
                          Navigator.pushNamedAndRemoveUntil(
                              context, LoginPage.routeName, (route) => false,
                              arguments: true);
                        }
                      }
                    },
                    builder: (context, state) {
                      if (state is FetchingPartItemsList &&
                          state.isFirstFetch) {
                        return _loadingIndicator();
                      }
                      List<PartItemResponse> partList = [];
                      bool isLoading = false;

                      if (state is FetchingPartItemsList) {
                        partList = state.oldList;
                        isLoading = true;
                      } else if (state is FetchedPartItems) {
                        partList = state.partItemList;
                      } else if (state is PartItemsError) {
                        return Center(
                          child: Padding(
                            padding: const EdgeInsets.all(20.0),
                            child: Text(
                              state.errorMessage,
                              textAlign: TextAlign.center,
                              style: const TextStyle(
                                  color: AppColor.greyBorderColor,
                                  fontSize: 18),
                            ),
                          ),
                        );
                      }
                      return RefreshIndicator(
                        onRefresh: () async {
                          partItemsBloc.add(FetchPartItemList(
                              refresh: true,
                              query: '',
                              partId: widget.singleSelectionParam.bomId ?? ""));
                          return await null;
                        },
                        child: partList.isNotEmpty
                            ? ListView.builder(
                                itemCount:
                                    widget.singleSelectionParam.bomId == null
                                        ? partList.length + (isLoading ? 1 : 0)
                                        : partList.length,
                                controller: scrollController,
                                itemBuilder: (context, index) {
                                  if (widget.singleSelectionParam.bomId ==
                                      null) {
                                    if (index < partList.length) {
                                      return _getSingleRequestPart(
                                          partList[index]);
                                    } else {
                                      Timer(const Duration(milliseconds: 30),
                                          () {
                                        scrollController.jumpTo(scrollController
                                            .position.maxScrollExtent);
                                      });
                                      return _loadingIndicator();
                                    }
                                  } else {
                                    return _getSingleRequestPart(
                                        partList[index]);
                                  }
                                },
                              )
                            : Center(
                                child: Text(
                                  AppLocalizations.of(context)!.noPartFound,
                                ),
                              ),
                      );
                    },
                  ),
                ))
              ],
            ),
          ),
        ),
      ),
      floatingActionButton: ApplicationUtil.getBackButton(
        context,
        onBackPressed: () {
          Navigator.pop(context);
        },
      ),
    );
  }

  _getItemPartAppBar() => Container(
        margin: const EdgeInsets.symmetric(horizontal: 15),
        child: Row(
          children: [
            Expanded(
              child: Text(
                AppLocalizations.of(context)!.addParts,
                style: const TextStyle(
                    color: AppColor.blackTextColor,
                    fontSize: 26,
                    fontWeight: FontWeight.bold),
              ),
            ),
          ],
        ),
      );

  Widget _loadingIndicator() {
    return Container(
      padding: const EdgeInsets.all(8.0),
      child: const Center(child: CircularProgressIndicator()),
    );
  }

  Widget _getSingleRequestPart(PartItemResponse part) {
    return Container(
      padding: const EdgeInsets.only(top: 5),
      child: Column(
        children: [
          InkWell(
            onTap: () {
              part.bomPartId ?? widget.singleSelectionParam.bomId;
              if (part.count > 0) {
                selectedPartBloc.add(UpdatePartItem(part: part, isAdd: false));
              }
              /*   if (part.partId.contains("BOM")) {
                part.partId = part.bomPartId!;
              } else {
                part.partId = part.bomPartId ?? part.partId;
              }
*/
              if (widget.singleSelectionParam.isSingleSelection) {
                Navigator.pop(context, part);
              } else {
                if (selectedPartBloc.selectedPartList.contains(part)) {
                  selectedPartBloc
                      .add(UpdatePartItem(part: part, isAdd: false));
                } else {
                  if (part.count == 0) {
                    part.count = 1;
                  }
                  selectedPartBloc.add(UpdatePartItem(part: part, isAdd: true));
                }
              }
            },
            child: Row(
              children: [
                BlocBuilder<SelectedPartItemBloc, SelectedPartItemState>(
                  builder: (context, state) {
                    if (state is UpdatedPartItem) {
                      state.partItemsList.forEach((element) {
                        if (element?.bomPartId == part.bomPartId) {
                          part.partId = element!.partId;
                        } else {
                          if (element?.partId == part.bomPartId) {
                            element?.partId = element.bomPartId!;
                            element?.bomPartId = part.bomPartId;
                          }
                        }
                      });
                      if (state.partItemsList.contains(part)) {
                        return const FaIcon(
                          FontAwesomeIcons.checkCircle,
                          color: AppColor.greenSentColor,
                          size: 25,
                        );
                      } else {
                        return const FaIcon(
                          FontAwesomeIcons.circle,
                          color: AppColor.greyBorderColor,
                          size: 25,
                        );
                      }
                    } else {
                      return const FaIcon(
                        FontAwesomeIcons.circle,
                        color: AppColor.greyBorderColor,
                        size: 25,
                      );
                    }
                  },
                ),
                const SizedBox(
                  width: 10,
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      part.name,
                      style: const TextStyle(
                          fontSize: 18, color: AppColor.blackTextColor),
                    ),
                    const SizedBox(
                      height: 2,
                    ),
                    Text(part.manufacturerPartNo),
                    const SizedBox(
                      height: 2,
                    ),
                  ],
                )
              ],
            ),
          ),
          const Divider(
            thickness: 2,
          ),
        ],
      ),
    );
  }
}
