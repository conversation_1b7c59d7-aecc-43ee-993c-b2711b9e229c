import 'dart:convert';
import 'dart:io';

import 'package:alink/pages/image_editor/edit_image_home_page.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:image_picker/image_picker.dart';
import 'package:http/http.dart' as http;

import 'package:permission_handler/permission_handler.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import '../logger/logger.dart';

class AfsImagePicker {
  static onImageButtonPressed(ImageSource source,
      {required BuildContext context,
      bool isMultiImage = false,
      required Function(String editedImageBase64) onReturn}) async {
    String? editedImage;
    String? pickedBase64Img;
    try {
      final ImagePicker _picker = ImagePicker();

      final pickedFile = await _picker.pickImage(
        source: source,
        imageQuality: 70,
        maxHeight: 480,
        maxWidth: 640,
      );
      if (kIsWeb) {
        final http.Response responseData =
            await http.get(Uri.parse(pickedFile!.path));
        var uint8list = responseData.bodyBytes;
        pickedBase64Img = base64Encode(uint8list);
      } else {
        var uint8list = File(pickedFile!.path).readAsBytesSync();
        pickedBase64Img = base64Encode(uint8list);
      }
      //NAVIGATE TO EDIT
      editedImage = await Navigator.pushNamed(
              context, EditImageHomePage.routeName, arguments: pickedBase64Img)
          as String;
      if (editedImage is String) {
        onReturn(editedImage);
        /*widget.serviceRequestEquipmentAndPartFromServer!.imageList
            .add({"DOCUMENT_TYPE": "image/jpeg", "DOCUMENT_BLOB": editedImage});
        // imageList.add(pickedImage);
        editedImage = "";
        setState(() {});*/
      }
    } on PlatformException catch (e) {
      if (e.code == 'camera_access_denied') {}
    } catch (e) {
      Logger.e(e.toString());
    }
  }

  static void askCameraPermission(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: Text(AppLocalizations.of(context)!.cameraPermission),
        content: Text(AppLocalizations.of(context)!.thisAppNeedsCameraAcces),
        actions: <Widget>[
          TextButton(
            child: Text(AppLocalizations.of(context)!.cancel),
            onPressed: () => Navigator.of(context).pop(),
          ),
          TextButton(
            child: Text(AppLocalizations.of(context)!.settings),
            onPressed: () {
              Navigator.of(context).pop();
              openAppSettings();
            },
          ),
        ],
      ),
    );
  }

  static void askPhotosPermission(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: Text(AppLocalizations.of(context)!.photosPermission),
        content: Text(AppLocalizations.of(context)!.thisAppNeedsPhotosAcces),
        actions: <Widget>[
          TextButton(
            child: Text(AppLocalizations.of(context)!.cancel),
            onPressed: () => Navigator.of(context).pop(),
          ),
          TextButton(
            child: Text(AppLocalizations.of(context)!.settings),
            onPressed: () {
              Navigator.of(context).pop();
              openAppSettings();
            },
          ),
        ],
      ),
    );
  }
}
