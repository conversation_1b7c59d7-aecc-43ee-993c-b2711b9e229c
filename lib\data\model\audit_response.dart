import 'dart:convert' as convert;

import 'package:alink/pages/airline/model/lopa_model.dart';
import 'package:equatable/equatable.dart';

class AuditResponse {
  int auditCount = 0;
  List<Audit> auditList = [];

  AuditResponse({required this.auditCount, required this.auditList});

  AuditResponse.fromJson(Map<String, dynamic> json) {
    auditCount = json['AUDITS_COUNT'];
    if (json['AUDITS'] != null) {
      auditList = [];
      json['AUDITS'].forEach((v) {
        auditList.add(Audit.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['AUDITS_COUNT'] = auditCount;
    if (auditList.isNotEmpty) {
      data['AUDITS'] = auditList.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Result {
  final String name;
  final String status;
  final int equipmentId;

  Result({
    required this.name,
    required this.status,
    required this.equipmentId,
  });

  factory Result.fromJson(Map<String, dynamic> json) {
    return Result(
      name: json['NAME'],
      status: json['STATUS'],
      equipmentId: json['EQUIPMENT_ID'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};

    data['NAME'] = name;
    data['STATUS'] = status;
    data['EQUIPMENT_ID'] = equipmentId;

    return data;
  }
}

class Audit {
  int? auditId;
  int? refAuditId;
  int? customerId;
  int? auditScheduledId;
  List<AuditEquipment> auditEquipmentList = [];
  String? status;
  dynamic latitude;
  dynamic longitude;
  dynamic extn;
  dynamic createdDate;
  dynamic expiryDate;
  int? updateBy;
  String? updatedAt;
  String? locationId;
  String? description;
  String? schedule;
  String? serviceType;
  List<Map<String, dynamic>>? auditLocationList;
  List<Result>? results;

  Audit(
      {this.auditId,
      this.refAuditId,
      this.customerId,
      this.auditScheduledId,
      required this.auditEquipmentList,
      this.status,
      this.latitude,
      this.longitude,
      this.extn,
      this.createdDate,
      this.expiryDate,
      this.updateBy,
      this.updatedAt,
      this.locationId,
      this.description,
      this.schedule,
      this.serviceType,
      this.results,
      this.auditLocationList});

  Audit.fromJson(Map<String, dynamic> json) {
    auditId = json['AUDIT_ID'];
    refAuditId = json['REF_AUDIT_ID'];
    customerId = json['CUSTOMER_ID'];
    auditScheduledId = json['AUDIT_SCH_ID'];
    if (json['EQUIPMENTS'] != null) {
      auditEquipmentList = [];
      json['EQUIPMENTS'].forEach((v) {
        auditEquipmentList.add(AuditEquipment.fromJson(v));
      });
    }
    status = json['STATUS'];
    latitude = json['LATITUDE'];
    longitude = json['LONGITUDE'];
    extn = json['EXTN'];
    createdDate = json['CREATED_AT'];
    updateBy = json['UPDATED_BY'];
    expiryDate = json['EXPIRY_DATE'];
    updatedAt = json['UPDATED_AT'];
    locationId = json['LOCATION_ID'];
    description = json['DESCRIPTION'];
    schedule = json['SCHEDULE'];
    serviceType = json['SERVICE_TYPE'];
    results = json['RESULTS'] != null
        ? (json['RESULTS'] as List).map((r) => Result.fromJson(r)).toList()
        : null;
    if (json['LOCATION'] != null) {
      auditLocationList = [];
      json['LOCATION'].forEach((v) {
        //auditLocationList!.add(json['LOCATION']);
        auditLocationList!.add(v);
      });
    }
    //auditLocationList = json['LOCATION'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['AUDIT_ID'] = auditId;
    data['REF_AUDIT_ID'] = refAuditId;
    data['CUSTOMER_ID'] = customerId;
    data['AUDIT_SCH_ID'] = auditScheduledId;
    if (auditEquipmentList.isNotEmpty) {
      data['EQUIPMENTS'] = auditEquipmentList.map((v) => v.toJson()).toList();
    }
    data['STATUS'] = status;
    data['LATITUDE'] = latitude;
    data['LONGITUDE'] = longitude;
    data['EXTN'] = extn;
    data['CREATED_AT'] = createdDate;
    data['UPDATED_BY'] = updateBy;
    data['EXPIRY_DATE'] = expiryDate;
    data['UPDATED_AT'] = updatedAt;
    data['LOCATION_ID'] = locationId;
    data['DESCRIPTION'] = description;
    data['SCHEDULE'] = schedule;
    data['SERVICE_TYPE'] = serviceType;
    if (auditLocationList != null) {
      data['LOCATION'] = auditLocationList;
    }
    return data;
  }
}

class AuditEquipment extends Equatable {
  String? tag;
  String? name;
  String? bomId;
  int? equipmentId;
  bool isScanned = false;
  int? serviceRequestCount;
  String? locationId;
  String? categoryName;
  String status = "Unscanned";
  bool? locationConfirm;
  bool? isInActiveEquipment = false;
  String? oldLocationId;
  List<Part> partList = [];
  List<Map<String, dynamic>> imageMapData = [];
  List<Map<String, dynamic>> partsMapData = [];
  DateTime? scannedDateTime;
  double? latitude;
  double? longitude;

  AuditEquipment(
      {this.tag,
      this.name,
      this.bomId,
      this.equipmentId,
      this.serviceRequestCount,
      this.isScanned = false,
      this.locationId,
      this.categoryName,
      this.status = "Unscanned",
      this.locationConfirm,
      this.isInActiveEquipment = false,
      this.oldLocationId,
      this.partList = const [],
      this.imageMapData = const [],
      this.partsMapData = const [],
      this.scannedDateTime,
      this.latitude,
      this.longitude});

  AuditEquipment.fromJson(Map<String, dynamic> json) {
    tag = json['TAG'];
    name = json['NAME'];
    bomId = json['BOM_ID'];
    equipmentId = json['EQUIPMENT_ID'];
    locationId = json['LOCATION_ID'];
    categoryName = json['CATEGORY_NAME'];
    serviceRequestCount = json['SERVICE_REQUEST_COUNT'];
    scannedDateTime = json['SCANNED_DATETIME'] == null
        ? null
        : DateTime.parse(json['SCANNED_DATETIME']);
    if (json['STATUS'] != null) {
      status = json['STATUS'];
    }
    if (json['IS_SCANNED'] != null) {
      isScanned = json['IS_SCANNED'];
    }
    if (json['LOCATION_CONFIRM'] != null) {
      locationConfirm = json['LOCATION_CONFIRM'];
    }
    if (json['isActiveEquipment'] != null) {
      isInActiveEquipment = json['isActiveEquipment'];
    }
    if (json['OLD_LOCATION_ID'] != null) {
      oldLocationId = json['OLD_LOCATION_ID'];
    }
    if (json['PARTS'] != null) {
      partsMapData =
          List<Map<String, dynamic>>.from(convert.json.decode(json['PARTS']));
    }
    if (json['IMAGES'] != null) {
      imageMapData =
          List<Map<String, dynamic>>.from(convert.json.decode(json['IMAGES']));
    }
    if (json['LATITUDE'] != null) {
      latitude = json['LATITUDE'];
    }
    if (json['LONGITUDE'] != null) {
      longitude = json['LONGITUDE'];
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['TAG'] = tag;
    data['NAME'] = name;
    data['BOM_ID'] = bomId;
    data['EQUIPMENT_ID'] = equipmentId;
    data['LOCATION_ID'] = locationId;
    data['CATEGORY_NAME'] = categoryName;
    data['SERVICE_REQUEST_COUNT'] = serviceRequestCount;
    data['SCANNED_DATETIME'] =
        scannedDateTime == null ? null : scannedDateTime?.toIso8601String();
    data['STATUS'] = status;
    data['IS_SCANNED'] = isScanned;
    data['LOCATION_CONFIRM'] = locationConfirm;
    data['isActiveEquipment'] = isInActiveEquipment;
    data['OLD_LOCATION_ID'] = oldLocationId;
    data['PARTS'] = convert.json.encode(partsMapData);
    data['IMAGES'] = convert.json.encode(imageMapData);
    data['LATITUDE'] = latitude;
    data['LONGITUDE'] = longitude;

    return data;
  }

  @override
  // TODO: implement props
  List<Object?> get props => [tag];

  @override
  String toString() {
    return 'AuditEquipment{tag: $tag, name: $name, bomId: $bomId, equipmentId: $equipmentId, isScanned: $isScanned, serviceRequestCount: $serviceRequestCount, locationId: $locationId, categoryName: $categoryName, status: $status, locationConfirm: $locationConfirm, isActiveEquipment: $isInActiveEquipment, oldLocationId: $oldLocationId,latitude: $latitude, longitude: $longitude}';
  }
}
