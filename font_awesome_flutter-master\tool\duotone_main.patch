diff --git a/example/lib/main.dart b/example/lib/main.dart
index e1bc76d..896ad55 100644
--- a/example/lib/main.dart
+++ b/example/lib/main.dart
@@ -67,10 +67,7 @@ class FontAwesomeGalleryHomeState extends State<FontAwesomeGalleryHome> {
                           alignment: Alignment.center,
                           child: Hero(
                             tag: icon,
-                            child: FaIcon(
-                              icon.iconData,
-                              size: 100,
-                            ),
+                            child: _icon(icon.iconData, size: 100),
                           ),
                         ),
                       );
@@ -81,7 +78,7 @@ class FontAwesomeGalleryHomeState extends State<FontAwesomeGalleryHome> {
               child: Column(
                 mainAxisAlignment: MainAxisAlignment.center,
                 children: <Widget>[
-                  Hero(tag: icon, child: FaIcon(icon.iconData)),
+                  <PERSON>(tag: icon, child: _icon(icon.iconData)),
                   Container(
                     padding: EdgeInsets.only(top: 16.0),
                     child: Text(icon.title),
@@ -95,6 +92,18 @@ class FontAwesomeGalleryHomeState extends State<FontAwesomeGalleryHome> {
     );
   }
 
+  Widget _icon(IconData icon, {double? size}) {
+    if(icon is IconDataDuotone) {
+      return FaDuotoneIcon(
+        icon,
+        size: size,
+        primaryColor: Theme.of(context).iconTheme.color!.withOpacity(.3),
+        secondaryColor: Theme.of(context).iconTheme.color,
+      );
+    }
+    return FaIcon(icon, size: size);
+  }
+
   AppBar _titleBar() {
     return AppBar(
       title: Text("Font Awesome Flutter Gallery"),
