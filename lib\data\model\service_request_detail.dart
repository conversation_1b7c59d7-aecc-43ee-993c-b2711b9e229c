import 'package:alink/data/model/part_item_response.dart';

class ServiceRequestDetail {
  int? requestId;
  int? refRequestId;
  int? customerId;
  int? equipmentId;
  String? requestType;
  String? currentLocationId;
  String? newLocationId;
  String? taskType;
  String? taskTypeDesc;
  String? description;
  int? createdBy;
  String? createdDate;
  String? status;
  dynamic extn;
  List<PartItemResponse>? parts;
  List<Document>? document;
  int? remainingTime;
  String? equipmentName;
  String? equipmentCategoryName;
  List<Map<String, dynamic>>? location;
  int? serviceRequestCount;
  int? remainingRequestTime;
  String? requestedName;
  String? tag;
  String? equipmentLocationId;
  String? bomId;
  String? safetyIssue;

  ServiceRequestDetail(
      {this.requestId,
      this.refRequestId,
      this.customerId,
      this.equipmentId,
      this.requestType,
      this.currentLocationId,
      this.newLocationId,
      this.taskType,
      this.taskTypeDesc,
      this.description,
      this.createdBy,
      this.createdDate,
      this.status,
      this.extn,
      this.parts,
      this.document,
      this.remainingTime,
      this.location,
      this.serviceRequestCount,
      this.remainingRequestTime,
      this.requestedName,
      this.tag,
      this.equipmentLocationId,
      this.bomId,
      this.safetyIssue});

  ServiceRequestDetail.fromJson(Map<String, dynamic> json) {
    requestId = json['REQUEST_ID'];
    refRequestId = json['REF_REQUEST_ID'];
    customerId = json['CUSTOMER_ID'];
    equipmentId = json['EQUIPMENT_ID'];
    requestType = json['REQUEST_TYPE'];
    currentLocationId = json['CURRENT_LOCATION_ID'];
    newLocationId = json['NEW_LOCATION_ID'];
    taskType = json['TASK_TYPE'];
    taskTypeDesc = json['TASK_TYPE_DESC'];
    description = json['DESCRIPTION'];
    createdBy = json['CREATED_BY'];
    createdDate = json['CREATED_AT'];
    status = json['STATUS'];
    remainingTime = json['REMAINING_REQUEST_TIME'];
    equipmentName = json['EQUIPMENT_NAME'];
    equipmentCategoryName = json['EQUIPMENT_CATEGORY_NAME'];
    serviceRequestCount = json['SERVICE_REQUESTS_COUNT'];
    remainingRequestTime = json['REMAINING_REQUEST_TIME'];
    requestedName = json['CREATED_BY_NAME'];
    tag = json['TAG'];

    if (json['EXTN'] != null) {
      extn = json['EXTN'];
    }
    if (json['PARTS'] != null) {
      parts = [];
      json['PARTS'].forEach((v) {
        parts!.add(new PartItemResponse.fromMap(v));
      });
    }
    if (json['DOCUMENT'] != null) {
      document = [];
      json['DOCUMENT'].forEach((v) {
        document!.add(new Document.fromJson(v));
      });
    }
    if (json['LOCATION'] != null) {
      location = [];
      json['LOCATION'].forEach((v) {
        location!.add(v);
      });
      //location = json['LOCATION'];
    }
    if (json['EQUIPMENT_LOCATION_ID'] != null) {
      equipmentLocationId = json['EQUIPMENT_LOCATION_ID'];
    }
    if (json['EQUIPMENT_BOM_ID'] != null) {
      bomId = json['EQUIPMENT_BOM_ID'];
    }
    if (json['SAFETY'] != null) {
      safetyIssue = json['SAFETY'];
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['REQUEST_ID'] = requestId;
    data['REF_REQUEST_ID'] = refRequestId;
    data['CUSTOMER_ID'] = customerId;
    data['EQUIPMENT_ID'] = equipmentId;
    data['REQUEST_TYPE'] = requestType;
    data['CURRENT_LOCATION_ID'] = currentLocationId;
    data['NEW_LOCATION_ID'] = newLocationId;
    data['TASK_TYPE'] = taskType;
    data['TASK_TYPE_DESC'] = taskTypeDesc;
    data['DESCRIPTION'] = description;
    data['CREATED_BY'] = createdBy;
    data['CREATED_AT'] = createdDate;
    data['STATUS'] = status;
    data['REMAINING_REQUEST_TIME'] = remainingTime;
    data['EQUIPMENT_NAME'] = equipmentName;
    data['EQUIPMENT_CATEGORY_NAME'] = equipmentCategoryName;
    data['SERVICE_REQUESTS_COUNT'] = serviceRequestCount;
    data['CREATED_BY_NAME'] = requestedName;
    if (extn != null) {
      data['EXTN'] = extn!.map((v) => v.toJson()).toList();
    }
    data['PARTS'] = parts;
    if (document != null) {
      data['DOCUMENT'] = document!.map((v) => v.toJson()).toList();
    }
    if (location != null) {
      data['LOCATION'] = location!;
    }
    if (equipmentLocationId != null) {
      data['EQUIPMENT_LOCATION_ID'] = equipmentLocationId!;
    }
    data['TAG'] = tag;
    if (data['EQUIPMENT_BOM_ID'] != null) {
      data['EQUIPMENT_BOM_ID'] = bomId;
    }
    if (data['SAFETY'] != null) {
      data['SAFETY'] = safetyIssue;
    }
    return data;
  }

  @override
  String toString() {
    return 'ServiceRequestDetail{requestId: $requestId,refRequestId: $refRequestId, customerId: $customerId, equipmentId: $equipmentId, '
        'requestType: $requestType, currentLocationId: $currentLocationId, newLocationId: $newLocationId, taskType: $taskType,taskTypeDesc: $taskTypeDesc, description: $description, createdBy: $createdBy, createdDate: $createdDate, status: $status, extn: $extn, parts: $parts, document: $document, remainingTime: $remainingTime, equipmentName: $equipmentName, equipmentCategoryName: $equipmentCategoryName, location: $location, serviceRequestCount: $serviceRequestCount,}';
  }
}

class Document {
  String? dOCUMENTBLOB;
  String? dOCUMENTTYPE;

  Document({required this.dOCUMENTBLOB, required this.dOCUMENTTYPE});

  Document.fromJson(Map<String, dynamic> json) {
    dOCUMENTBLOB = json['DOCUMENT_BLOB'];
    dOCUMENTTYPE = json['DOCUMENT_TYPE'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['DOCUMENT_BLOB'] = dOCUMENTBLOB;
    data['DOCUMENT_TYPE'] = dOCUMENTTYPE;
    return data;
  }
}
