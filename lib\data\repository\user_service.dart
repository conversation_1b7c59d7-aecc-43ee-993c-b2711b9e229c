import 'dart:convert';

import 'package:alink/data/dao/user_dao.dart';
import 'package:alink/data/model/customer.dart';
import 'package:alink/database/database.dart';
import 'package:alink/service_locator.dart';
import 'package:alink/util/app_constant.dart';
import 'package:alink/util/error.dart';
import 'package:crypto/crypto.dart';
import 'package:drift/native.dart'
    if (dart.library.html) 'package:alink/util/moor_web_exception.dart';
import 'package:http/http.dart' as http;

import 'package:shared_preferences/shared_preferences.dart';
import '../../logger/logger.dart';

import '../../util/application_util.dart';

class UserService {
  final UserDao userDao;
  Logger logger = Logger();

  UserService({required this.userDao});

  Future<dynamic> loginUserWIthEmailAndPassword(
      {required String email,
      required String password,
      required int customerId}) async {
    try {
      var bytes = utf8.encode(password);
      var encryptedPassword = sha256.convert(bytes).toString().toUpperCase();
      String basicAuth =
          'Basic ' + base64Encode(utf8.encode('$email:$encryptedPassword'));
      final response;
      if (customerId < 0) {
        response = await http.get(
            Uri.parse("${AppConstant.BASE_URL}/login?device=android&app=app"),
            headers: <String, String>{'authorization': basicAuth});
        Logger.i(
            '${ApplicationUtil.getFormattedCurrentDateAndTime()} loginUserWIthEmailAndPassword API = > ' +
                "${AppConstant.BASE_URL}/login?device=android&app=app Conversation id: ${response.headers['conversation-id'].toString()}");
      } else {
        response = await http.get(
            Uri.parse(
                "${AppConstant.BASE_URL}/login?device=android&app=app&tenantId=$customerId"),
            headers: <String, String>{'authorization': basicAuth});
        Logger.i(
            '${ApplicationUtil.getFormattedCurrentDateAndTime()} loginUserWIthEmailAndPassword API = > ' +
                "${AppConstant.BASE_URL}/login?device=android&app=app&tenantId=$customerId Conversation id: ${response.headers['conversation-id'].toString()}");
      }

      printInfoLog(
          methodName: 'loginUserWIthEmailAndPassword',
          statusCode: response.statusCode,
          contentLength: response.contentLength ?? 0);
      if (response.statusCode == 200) {
        var decodeBody = jsonDecode(response.body);
        if (decodeBody.containsKey("tenants")) {
          var customerResponseList = decodeBody['tenants'] as List<dynamic>;
          return customerResponseList.map((e) => Customer.fromJson(e)).toList();
        } else {
          UserData userData = UserData.fromJson(decodeBody["USER"]);

          await _storeDataInSharedPreferences(email, userData,
              decodeBody["token"], decodeBody["CUSTOMER_NAME"]);
          //check if user exist in database
          UserData? loggedInUser = await userDao.getUser();
          if (loggedInUser != null) {
            if (loggedInUser.EMAIL == userData.EMAIL &&
                loggedInUser.USER_ID == userData.USER_ID) {
              await userDao.updateUser(userData);
              return userData;
            } else {
              return HttpError(errorMessage: "MULTIPLE USER LOGIN");
            }
          } else {
            await userDao.insertUser(userData);
            return userData;
          }
        }
      } else if (response.statusCode == 401) {
        return HttpError(
            errorMessage:
                "Invalid credentials. Username or password may be incorrect.");
      } else if (response.statusCode == 400) {
        return HttpError(errorMessage: response.body);
      } else if (response.statusCode == 403) {
        return HttpError(errorMessage: "User Disabled");
      } else if (response.statusCode == 404) {
        return HttpError(
            errorMessage:
                "User not found. Make sure that this email id is associated with a company in the ALink admin portal.");
      } else {
        return HttpError(errorMessage: "Internal server error");
      }
    } on SqliteException catch (e) {
      return HttpError(errorMessage: e.message);
    } catch (e) {
      Logger.e(e.toString());
      return HttpError(
        errorMessage: e.toString(),
      );
    }
  }

  Future<dynamic> checkUserLoggedIn() async {
    try {
      Logger.i(
          '${ApplicationUtil.getFormattedCurrentDateAndTime()} checkUserLoggedIn DATABASE');
      dynamic userData = await userDao.getUser();
      return userData;
    } on SqliteException catch (e) {
      return HttpError(errorMessage: e.message);
    } catch (e) {
      Logger.e(e.toString());
    }
  }

  Future<dynamic> logoutUser() async {
    try {
      UserData? userData = await userDao.getUser();
      if (userData != null) {
        int deletedId = await userDao.deleteUser(userData);
        return deletedId;
      }
    } on SqliteException catch (e) {
      return HttpError(errorMessage: e.message);
    } catch (e) {
      Logger.e(e.toString());
      return HttpError(errorMessage: 'Error: ' + e.toString());
    }
  }

  Future<void> _storeDataInSharedPreferences(
      String email, UserData user, decodeToken, String customerName) async {
    SharedPreferences prefs = getIt<SharedPreferences>();
    await prefs.setString('email', email);
    await prefs.setInt('userId', user.USER_ID);
    await prefs.setString('firstName', user.FIRST_NAME!);
    await prefs.setString('lastName', user.LAST_NAME!);
    await prefs.setBool('isAfs', user.AFS_USER ?? false);
    await prefs.setString('customerType', user.CUSTOMER_TYPE ?? '');
    await prefs.setString('token', 'Bearer ' + decodeToken);
    await prefs.setString('customerName', customerName);
    await prefs.setBool('isLoggedIn', true);
    await prefs.setString('phoneNumber', user.PHONE ?? '');
  }

  void printInfoLog(
      {required String methodName,
      required int statusCode,
      required int contentLength}) {
    Logger.i(
        '${ApplicationUtil.getFormattedCurrentDateAndTime()} $methodName RESPOND = > ' +
            "STATUS CODE: $statusCode, LENGTH: $contentLength");
  }
}
