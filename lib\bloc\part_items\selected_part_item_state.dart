part of 'selected_part_item_bloc.dart';

@immutable
abstract class SelectedPartItemState {}

class SelectedPartItemInitial extends SelectedPartItemState {}

class AddingPartItem extends SelectedPartItemState {}

class UpdatedPartItem extends SelectedPartItemState {
  final List<PartItemResponse?> partItemsList;
  UpdatedPartItem(this.partItemsList);
}

class AddPartItemError extends SelectedPartItemState {
  final String error;
  AddPartItemError(this.error);
}

class RemovedPartItem extends SelectedPartItemState {
  final List<PartItemResponse?> partItemsList;
  RemovedPartItem(this.partItemsList);
}

class FetchedSelectedPartItem extends SelectedPartItemState {
  final List<PartItemResponse?> partItemsList;
  FetchedSelectedPartItem(this.partItemsList);
}
