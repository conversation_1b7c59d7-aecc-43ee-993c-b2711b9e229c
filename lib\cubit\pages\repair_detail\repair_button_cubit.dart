import 'package:alink/data/model/service_request_detail.dart';
import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';

part 'repair_button_state.dart';

class RepairButtonCubit extends Cubit<RepairButtonState> {
  ServiceRequestDetail? selectedRequestDetail;
  RepairButtonCubit() : super(RepairButtonInitial());

  void hideRepairButton(ServiceRequestDetail serviceRequestDetail) {
    selectedRequestDetail = serviceRequestDetail;
    emit(HideStartRepairButton(serviceRequestDetail: serviceRequestDetail));
  }

  void resetRepairButton() {
    selectedRequestDetail = null;
    emit(RepairButtonInitial());
  }

  void hidePartUsedConfirmButton() {
    emit(HidePartUsedButtonButton());
  }
}
