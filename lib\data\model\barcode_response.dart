class BarcodeResponse {
  BarcodeResponse? barcodeResponseInstance;
  Equipment? equipment;
  int? recentServiceRequestId;
  int? serviceRequestCount;
  int? auditId;

  BarcodeResponse(
      {this.equipment,
      this.recentServiceRequestId,
      this.serviceRequestCount,
      this.auditId});

  BarcodeResponse.fromJson(Map<String, dynamic> json) {
    equipment = json['EQUIPMENT'] != null
        ? Equipment.fromJson(json['EQUIPMENT'])
        : null;
    if (json['RECENT_SERVICE_REQUEST_ID'] != null) {
      recentServiceRequestId = json['RECENT_SERVICE_REQUEST_ID'];
    }
    if (json['SERVICE_REQUEST_COUNT'] != null) {
      serviceRequestCount = json['SERVICE_REQUEST_COUNT'];
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (equipment != null) {
      data['EQUIPMENT'] = equipment!.toJson();
    }
    if (recentServiceRequestId != null) {
      data['RECENT_SERVICE_REQUEST_ID'] = recentServiceRequestId;
    }
    if (serviceRequestCount != null) {
      data['SERVICE_REQUEST_COUNT'] = serviceRequestCount;
    }
    return data;
  }
}

class Equipment {
  int? equipmentId;
  int? customerId;
  String? locationId;
  String? categoryId;
  String? bomId;
  String? tag;
  String? name;
  String? description;
  Map<String, dynamic>? extn;
  List<Map<String, dynamic>>? location;
  String? categoryName;
  bool? isInActiveEquipment;
  String? oldLocationId;
  String? status;
  Equipment(
      {this.equipmentId,
      this.customerId,
      this.locationId,
      this.categoryId,
      this.bomId,
      this.tag,
      this.name,
      this.description,
      this.extn,
      this.location,
      this.categoryName,
      this.isInActiveEquipment,
      this.oldLocationId,
      this.status});

  Equipment.fromJson(Map<String, dynamic> json) {
    equipmentId = json['EQUIPMENT_ID'];
    customerId = json['CUSTOMER_ID'];
    locationId = json['LOCATION_ID'];
    categoryId = json['CATEGORY_ID'];
    bomId = json['BOM_ID'];
    tag = json['TAG'];
    name = json['NAME'];
    description = json['DESCRIPTION'];
    extn = json['EXTN'];
    categoryName = json['CATEGORY_NAME'];
    status = json['STATUS'];
    if (json["INACTIVE"] != null) {
      if (json["INACTIVE"] == 'Y') {
        isInActiveEquipment = true;
      } else {
        isInActiveEquipment = false;
      }
    }
    if (json['LOCATION'] != null) {
      location = [];
      json['LOCATION'].forEach((v) {
        location!.add(v);
      });
    }
    //location = json['LOCATION'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['EQUIPMENT_ID'] = equipmentId;
    data['CUSTOMER_ID'] = customerId;
    data['LOCATION_ID'] = locationId;
    data['CATEGORY_ID'] = categoryId;
    data['BOM_ID'] = bomId;
    data['TAG'] = tag;
    data['NAME'] = name;
    data['DESCRIPTION'] = description;
    data['EXTN'] = extn;
    data['CATEGORY_NAME'] = categoryName;
    data['STATUS'] = status;
    if (isInActiveEquipment == true) {
      data["INACTIVE"] = "Y";
    }
    if (location != null) {
      data['LOCATION'] = location!;
    }
    return data;
  }

  @override
  String toString() {
    return 'Equipment{equipmentId: $equipmentId, customerId: $customerId, locationId: $locationId, categoryId: $categoryId, bomId: $bomId, tag: $tag, name: $name, description: $description, extn: $extn, location: $location, categoryName: $categoryName, isInActiveEquipment: $isInActiveEquipment, oldLocationId: $oldLocationId, status: $status}';
  }
}

class ServiceRequestRespond {
  int? requestId;
  String? description;

  ServiceRequestRespond({
    this.requestId,
    this.description,
  });

  Map<String, dynamic> toMap() {
    return {
      'REQUEST_ID': requestId,
      'DESCRIPTION': description,
    };
  }

  factory ServiceRequestRespond.fromMap(Map<String, dynamic> map) {
    return ServiceRequestRespond(
      requestId: map['REQUEST_ID'] as int,
      description: map['DESCRIPTION'] as String,
    );
  }
}
