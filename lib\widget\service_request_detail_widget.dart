import 'dart:convert';

import 'package:alink/bloc/part_items/selected_part_item_bloc.dart';
import 'package:alink/cubit/pages/repair_detail/repair_button_cubit.dart';
import 'package:alink/cubit/pages/repair_detail/repair_detail_api_cubit.dart';
import 'package:alink/data/model/service_request_detail.dart';
import 'package:alink/data/repository/api_service.dart';
import 'package:alink/pages/auth/login_page.dart';
import 'package:alink/pages/full_image_view.dart';
import 'package:alink/util/app_color.dart';
import 'package:alink/util/application_util.dart';
import 'package:alink/widget/seperator.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../bloc/api/api_bloc.dart';
import '../bloc/repair_list_api/repair_list_api_bloc.dart';
import '../bloc/service/service_request_bloc.dart';
import '../logger/logger.dart';
import '../pages/airport/service_request/afs_service_repair_page.dart';
import '../service_locator.dart';
import '../util/enums/app_enum.dart';

class ServiceRequestDetailWidget extends StatefulWidget {
  final int equipmentId;
  final bool showCancelIcon;
  final bool isTimedService;

  const ServiceRequestDetailWidget(
      {Key? key,
      required this.equipmentId,
      required this.showCancelIcon,
      required this.isTimedService})
      : super(key: key);

  @override
  _ServiceRequestDetailWidgetState createState() =>
      _ServiceRequestDetailWidgetState();
}

class _ServiceRequestDetailWidgetState extends State<ServiceRequestDetailWidget>
    with TickerProviderStateMixin {
  static const String className = '_ServiceRequestDetailWidgetState';
  String query = '';
  bool isStarted = false;
  RepairDetailApiCubit get repairCubit =>
      BlocProvider.of<RepairDetailApiCubit>(context);

  RepairButtonCubit get startRepairBtnCubit =>
      BlocProvider.of<RepairButtonCubit>(context);

  RepairListApiBloc get repairListBloc =>
      BlocProvider.of<RepairListApiBloc>(context);

  ApiBloc get apiBloc => BlocProvider.of<ApiBloc>(context);
  late TextEditingController commentTextEditingController;

  @override
  void initState() {
    commentTextEditingController = TextEditingController();
    repairCubit.getRepairDetail(widget.equipmentId, widget.isTimedService);
    super.initState();
  }

  int? customerId = getIt<SharedPreferences>().getInt('customerId');

  @override
  void dispose() {
    commentTextEditingController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    Logger.i("Class Name: " + className);
    print("Request ID: ${widget.equipmentId}");
    return BlocConsumer<RepairDetailApiCubit, RepairDetailState>(
      listener: (context, state) {
        if (state is FetchedSingleRepairServiceDetail) {
          if (state.serviceRequestDetail.isNotEmpty) {
            state.serviceRequestDetail.forEach((element) {
              if (element.equipmentId == widget.equipmentId) {
                BlocProvider.of<SelectedPartItemBloc>(context).add(
                    ProPopulateSuggestedPart(suggestedParts: element.parts));
                print('state.serviceRequestDetail.parts');
                print(element.parts);
              }
            });
            /*if (state.serviceRequestDetail[0].parts != null) {
              BlocProvider.of<SelectedPartItemBloc>(context).add(ProPopulateSuggestedPart(suggestedParts: state.serviceRequestDetail[0].parts));
            }*/
          } else {
            Navigator.pop(
              context,
              AppLocalizations.of(context)!.cancelServiceRequestSuccess,
            );
          }
        } else if (state is SingleRepairServiceDetailError) {
          if (state.errorMessage == ApiResponse.INVALID_AUTH) {
            Navigator.pushNamedAndRemoveUntil(
                context, LoginPage.routeName, (route) => false,
                arguments: true);
          }
        }
      },
      builder: (context, state) {
        if (state is FetchingSingleRepairServiceDetail) {
          return _loadingIndicator();
        } else if (state is FetchedSingleRepairServiceDetail) {
          List<ServiceRequestDetail> serviceRequestDetail =
              state.serviceRequestDetail;
          if (serviceRequestDetail.isEmpty) {
            return const Column(
              children: [
                SizedBox(
                  height: 20,
                ),
                Text.rich(TextSpan(children: <InlineSpan>[
                  TextSpan(
                    text: 'No ',
                    style: TextStyle(
                      fontSize: 18,
                    ),
                  ),
                  TextSpan(
                    text: 'Service Request ',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  TextSpan(
                    text: 'or ',
                    style: TextStyle(
                      fontSize: 18,
                    ),
                  ),
                  TextSpan(
                    text: 'Notifications ',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  TextSpan(
                    text: 'found',
                    style: TextStyle(
                      fontSize: 18,
                    ),
                  ),
                ]))
              ],
            );
          } else {
            return _getServiceRequestListviewBuilder(serviceRequestDetail);
          }
        } else if (state is SingleRepairServiceDetailError) {
          return Center(child: Text(state.errorMessage!));
        } else if (state is FetchedRepairsListForEquipment) {
          List<ServiceRequestDetail> serviceRequestDetailList =
              state.serviceRequestDetailList;
          if (serviceRequestDetailList.isEmpty) {
            Text(AppLocalizations.of(context)!.noServiceRequestNotification);
          } else {
            _getServiceRequestListviewBuilder(serviceRequestDetailList);
          }
        }
        return _loadingIndicator();
      },
    );
  }

  _getServiceNumberAndStatus(ServiceRequestDetail serviceRequestDetail) => Row(
        children: [
          serviceRequestDetail.requestType == "NOTIFICATION"
              ? const FaIcon(
                  FontAwesomeIcons.solidBell,
                  color: AppColor.orangeColor,
                  size: 30,
                )
              : const FaIcon(
                  FontAwesomeIcons.solidWrench,
                  color: AppColor.redColor,
                  size: 30,
                ),
          const SizedBox(
            width: 10,
          ),
          Text(
            '${serviceRequestDetail.requestId}: ${serviceRequestDetail.status}',
            style: const TextStyle(
                color: AppColor.greyTextColor,
                fontSize: 22,
                fontWeight: FontWeight.bold),
          ),
        ],
      );

  _getTerminalDetail(ServiceRequestDetail serviceRequestDetail) => Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          //_getCluster(),
          // TODO CLUSTER DETAIL
          Flexible(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  (serviceRequestDetail.equipmentName) != null
                      ? serviceRequestDetail.equipmentName!
                      : 'null',
                  style: const TextStyle(
                      color: AppColor.greyTextColor,
                      fontSize: 16,
                      fontWeight: FontWeight.bold),
                ),
                Text(
                  serviceRequestDetail.equipmentCategoryName == null
                      ? ""
                      : serviceRequestDetail.equipmentCategoryName!,
                  style: const TextStyle(
                      color: AppColor.greyTextColor, fontSize: 12),
                ),
              ],
            ),
          ),
          _getEquipmentLocation(serviceRequestDetail),
          /*Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _getGateName(serviceRequestDetail),
                style: const TextStyle(
                    color: AppColor.greyTextColor,
                    fontSize: 16,
                    fontWeight: FontWeight.bold),
              ),
              Text(
                AppLocalizations.of(context)!.gate,
                style: TextStyle(color: AppColor.greyTextColor, fontSize: 12),
              ),
            ],
          ),*/
          Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                _getDate(serviceRequestDetail),
                style: const TextStyle(
                    color: AppColor.greyTextColor,
                    fontSize: 16,
                    fontWeight: FontWeight.bold),
              ),
              serviceRequestDetail.requestType == "NOTIFICATION"
                  ? const SizedBox()
                  : _getStatusAndTime(serviceRequestDetail),
            ],
          ),
        ],
      );

  _getPhotos(ServiceRequestDetail serviceRequestDetail) => Container(
        height: 90,
        child: ListView.builder(
          scrollDirection: Axis.horizontal,
          itemCount: serviceRequestDetail.document!.length,
          itemBuilder: (context, index) {
            var base64Img = serviceRequestDetail.document![index].dOCUMENTBLOB;
            return _getSingleImage(base64Img!, index, serviceRequestDetail);
          },
        ),
      );

  _startRepairButton(ServiceRequestDetail serviceRequestDetail) {
    print("status of serviceRequestDetail.status " +
        serviceRequestDetail.status.toString());
    if (serviceRequestDetail.status == "PAUSE" &&
        serviceRequestDetail.requestType != "NOTIFICATION") {
      return SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColor.greyBorderColor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(15.0),
            ),
          ),
          onPressed: () {
            print(
                "This service request has been paused, so can not perform start repair. Request ID: ${serviceRequestDetail.requestId}");
          },
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 14),
            child: Text(
              AppLocalizations.of(context)!.startRepair,
              style: const TextStyle(color: Colors.white, fontSize: 18),
            ),
          ),
        ),
      );
    }
    if (serviceRequestDetail.requestType == "NOTIFICATION") {
      return SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          style: ElevatedButton.styleFrom(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(15.0),
              ),
              backgroundColor: isStarted
                  ? AppColor.greyBorderColor
                  : AppColor.primarySwatchColor),
          onPressed: () async {
            if (!isStarted) {
              setState(() {
                isStarted = true;
              });
              print(
                  "Current Equipment Service request is with equipmentId:${serviceRequestDetail.equipmentId} and requestId: ${serviceRequestDetail.requestId}");
              final response = await ApiService().convertToServiceRequest(
                  requestId: serviceRequestDetail.requestId!);
              if (response.statusCode == 204 || response.statusCode == 200) {
                var refRequestId = json.decode(response.body)[0]["REQUEST_ID"];
                print(
                    "Successfully converted notification to Service request. request ID: ${serviceRequestDetail.requestId}");
                List<ServiceRequestDetail> convertedSR = await ApiService()
                    .getServiceRequestDetailById(
                        refRequestId, widget.isTimedService, true, null);
                ServiceRequestDetail firstSR = convertedSR[0];
                print("Successfully fetched converted SR to save");
                BlocProvider.of<RepairButtonCubit>(context)
                    .hideRepairButton(firstSR);
                repairCubit.getRepairDetailByRequestId(refRequestId);
                print(
                    "Current Equipment Service request is with equipmentId:${serviceRequestDetail.equipmentId} and requestId: ${serviceRequestDetail.requestId} and converted "
                    "requestId: $refRequestId");
              } else {
                print(
                    "Unable to convert notification to Service request. request ID: ${serviceRequestDetail.requestId}");
              }
              setState(() {
                isStarted = false;
              });
            }
          },
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 14),
            child: Text(
              AppLocalizations.of(context)!.convertAndStartRepair,
              style: const TextStyle(color: Colors.white, fontSize: 18),
            ),
          ),
        ),
      );
    } else {
      return BlocBuilder<RepairButtonCubit, RepairButtonState>(
        builder: (context, state) {
          if (state is HideStartRepairButton ||
              state is HidePartUsedButtonButton) {
            return Container();
          } else {
            return SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(15.0),
                  ),
                ),
                onPressed: () {
                  setState(() {
                    BlocProvider.of<RepairButtonCubit>(context)
                        .hideRepairButton(serviceRequestDetail);
                    repairCubit.getRepairDetailByRequestId(
                        serviceRequestDetail.requestId!);
                    print(
                        "Current Equipment Service request is with equipmentId:${serviceRequestDetail.equipmentId} and requestId: ${serviceRequestDetail.requestId}");
                  });
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 14),
                  child: Text(
                    AppLocalizations.of(context)!.startRepair,
                    style: const TextStyle(color: Colors.white, fontSize: 18),
                  ),
                ),
              ),
            );
          }
        },
      );
    }
  }

  _getSingleImage(String base64img, int index,
          ServiceRequestDetail serviceRequestDetail) =>
      InkWell(
        onTap: () {
          Navigator.pushNamed(context, ImageViewPage.routeName,
              arguments: ImageWithTag(base64: base64img, index: index));
        },
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 5),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey),
            borderRadius: BorderRadius.circular(10),
          ),
          child: ClipRRect(
            borderRadius: const BorderRadius.all(Radius.circular(10)),
            child: Hero(
              tag: serviceRequestDetail.requestId! + index,
              child: Image.memory(
                base64Decode(base64img),
                fit: BoxFit.cover,
                height: 90,
                width: 90,
              ),
            ),
          ),
        ),
      );

  String _getDate(ServiceRequestDetail serviceRequestDetail) {
    return ApplicationUtil.getFormattedDateFromDate(
        createdDate: serviceRequestDetail.createdDate!);
  }

  _getStatusAndTime(ServiceRequestDetail serviceRequestDetail) {
    if (serviceRequestDetail.remainingTime! > 0) {
      return Container(
        width: 120,
        padding: const EdgeInsets.only(top: 6, bottom: 4),
        decoration: BoxDecoration(
          color: Theme.of(context).primaryColor,
          borderRadius: BorderRadius.circular(5),
        ),
        child: Center(
          child: serviceRequestDetail.requestType == "SCHEDULED"
              ? Text(
                  ' ${ApplicationUtil.getHourAndMinuteOrDayFromMinute(serviceRequestDetail.remainingTime!)} ${AppLocalizations.of(context)!.remaining} ',
                  //overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                      color: Colors.white,
                      fontSize: 11,
                      fontWeight: FontWeight.bold),
                )
              : Text(
                  ' ${ApplicationUtil.getHourAndMinuteFromMinute(serviceRequestDetail.remainingTime!)} ${AppLocalizations.of(context)!.remaining} ',
                  //overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                      color: Colors.white,
                      fontSize: 11,
                      fontWeight: FontWeight.bold),
                ),
        ),
      );
    } else {
      return Container(
        width: 110,
        padding: const EdgeInsets.only(top: 6, bottom: 4),
        decoration: BoxDecoration(
          color: AppColor.redColor,
          borderRadius: BorderRadius.circular(5),
        ),
        child: Center(
          child: serviceRequestDetail.requestType == "SCHEDULED"
              ? Text(
                  ' ${ApplicationUtil.getHourAndMinuteOrDayFromMinute(serviceRequestDetail.remainingTime!)} ${AppLocalizations.of(context)!.overdue} ',
                  overflow: TextOverflow.ellipsis,
                  style: const TextStyle(color: Colors.white, fontSize: 11),
                )
              : Text(
                  ' ${ApplicationUtil.getHourAndMinuteFromMinute(serviceRequestDetail.remainingTime!)} ${AppLocalizations.of(context)!.overdue} ',
                  overflow: TextOverflow.ellipsis,
                  style: const TextStyle(color: Colors.white, fontSize: 11),
                ),
        ),
      );
    }
  }

  _getPhotosUI(ServiceRequestDetail serviceRequestDetail) {
    if (serviceRequestDetail.document != null &&
        serviceRequestDetail.document!.isNotEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '${AppLocalizations.of(context)!.photos} (${serviceRequestDetail.document!.length})',
            style: const TextStyle(
              fontSize: 12,
              color: AppColor.greyTextColor,
            ),
          ),
          const SizedBox(
            height: 10,
          ),
          _getPhotos(serviceRequestDetail),
          const SizedBox(
            height: 10,
          ),
        ],
      );
    }
    return Container();
  }

  Widget _getServiceRequestDetails(ServiceRequestDetail serviceRequestDetail) {
    return AnimatedSize(
      //vsync: this,
      duration: const Duration(milliseconds: 300),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(10),
            width: double.infinity,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(15),
              border: Border.all(color: Theme.of(context).primaryColor),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(
                  height: 10,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _getServiceNumberAndStatus(serviceRequestDetail),
                    //widget.showCancelIcon ? _getCancelSRIcon() : Container(),
                    Spacer(),
                    BlocListener<ApiBloc, ApiState>(
                      listener: (context, state) {
                        if (state is CancelledServiceRequest) {
                          repairCubit.getRepairDetail(
                              widget.equipmentId, widget.isTimedService);
                          setState(
                            () {},
                          );
                          /*Navigator.pop(
                            context,
                            AppLocalizations.of(context)!.cancelServiceRequestSuccess,
                          );*/
                        } else if (state is CancelServiceRequestError) {
                          ApplicationUtil.showSnackBar(
                              context: context, message: state.errorMessage);
                        }
                      },
                      child: IconButton(
                        onPressed: () {
                          ApplicationUtil.showConfirmDialogWithInput(
                            context,
                            title: AppLocalizations.of(context)!
                                .cancelServiceRequest,
                            description: AppLocalizations.of(context)!
                                .doYouWantToCancelServiceRequest,
                            continueString:
                                AppLocalizations.of(context)!.yesCancelRequest,
                            cancelString:
                                AppLocalizations.of(context)!.keepRequest,
                            textEditingController: commentTextEditingController,
                            onContinue: () {
                              int? requestId;
                              requestId = serviceRequestDetail.requestId;
                              apiBloc.add(
                                CancelServiceRequest(
                                    serviceRequestId: requestId!,
                                    comment: commentTextEditingController.text),
                              );
                              commentTextEditingController.clear();
                            },
                          );
                        },
                        icon: const FaIcon(
                          FontAwesomeIcons.ban,
                          color: AppColor.orangeColor,
                          size: 22,
                        ),
                      ),
                    ),
                    _getBarcodeNumber(serviceRequestDetail),
                  ],
                ),
                const SizedBox(
                  height: 10,
                ),
                const DottedDivider(
                  color: AppColor.redColor,
                ),
                const SizedBox(
                  height: 10,
                ),
                _getTerminalDetail(serviceRequestDetail),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(
                      height: 10,
                    ),
                    serviceRequestDetail.taskTypeDesc != null
                        ? Row(
                            children: [
                              Text(
                                serviceRequestDetail.taskTypeDesc!,
                                style: const TextStyle(
                                    color: AppColor.greyTextColor,
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold),
                              ),
                              const Spacer(),
                              serviceRequestDetail.safetyIssue != null
                                  ? Padding(
                                      padding:
                                          const EdgeInsets.only(right: 8.0),
                                      child: Image.asset(
                                        width: 25,
                                        height: 25,
                                        "assets/images/hazard.png",
                                      ),
                                    )
                                  : Container(
                                      width: 0,
                                    ),
                            ],
                          )
                        : serviceRequestDetail.requestType == "SCHEDULED"
                            ? const Text(
                                "TIMED SERVICE",
                                style: TextStyle(
                                    color: AppColor.greyTextColor,
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold),
                              )
                            : Text(
                                serviceRequestDetail.requestType!,
                                style: const TextStyle(
                                    color: AppColor.greyTextColor,
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold),
                              ),
                    Text(
                      AppLocalizations.of(context)!.terminalServices,
                      style: const TextStyle(
                          color: AppColor.greyTextColor,
                          fontSize: 12,
                          fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
                const SizedBox(
                  height: 10,
                ),
                Text(
                  '${serviceRequestDetail.requestedName != null ? serviceRequestDetail.requestedName!.toUpperCase() : ''} REQUESTED',
                  style: const TextStyle(
                      color: AppColor.greyTextColor,
                      fontSize: 12,
                      fontWeight: FontWeight.bold),
                ),
                const SizedBox(
                  height: 10,
                ),
                Text(
                  serviceRequestDetail.description!,
                  style: const TextStyle(
                      color: AppColor.greyTextColor,
                      fontSize: 18,
                      fontWeight: FontWeight.bold),
                ),
                const SizedBox(
                  height: 20,
                ),
                _getPhotosUI(serviceRequestDetail),
                serviceRequestDetail.status != 'CLOSED'
                    ? _startRepairButton(serviceRequestDetail)
                    : Container(),
                const SizedBox(
                  height: 10,
                ),
              ],
            ),
          ),
          const SizedBox(
            height: 10,
          ),
        ],
      ),
    );
  }

  Widget _loadingIndicator() => Center(
        child: Container(
          margin: const EdgeInsets.all(10),
          child: const CircularProgressIndicator(),
        ),
      );

  _getBarcodeNumber(ServiceRequestDetail serviceRequestDetail) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        FaIcon(
          FontAwesomeIcons.solidBarcode,
          color: Theme.of(context).primaryColor,
          size: 30,
        ),
        const SizedBox(
          width: 5,
        ),
        Text(
          serviceRequestDetail.tag ?? '',
          style: const TextStyle(
              height: 1.2,
              color: AppColor.greyTextColor,
              fontSize: 22,
              fontWeight: FontWeight.bold),
        )
      ],
    );
  }

  _getEquipmentLocation(ServiceRequestDetail serviceRequestDetail) {
    if (serviceRequestDetail.location != null) {
      String endLocationName = '', endLocationCategory = '';
      serviceRequestDetail.location!.forEach((element) {
        element.forEach((root, value) {
          if (element['LOCATION_ID'] ==
              serviceRequestDetail.equipmentLocationId) {
            endLocationName = element['NAME'];
            endLocationCategory = element['CATEGORY'];
          }
        });
      });

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            endLocationName,
            style: const TextStyle(
                color: AppColor.greyTextColor,
                fontSize: 16,
                fontWeight: FontWeight.bold),
          ),
          Text(
            endLocationCategory,
            style: const TextStyle(color: AppColor.greyTextColor, fontSize: 12),
          ),
        ],
      );
    } else {
      return Container();
    }
  }

  _getServiceRequestListviewBuilder(
      List<ServiceRequestDetail> serviceRequestDetailList) {
    int notificationCount = 0;
    int serviceRequestCount = 0;
    serviceRequestDetailList.forEach((element) {
      if (element.requestType == "NOTIFICATION") {
        notificationCount++;
      } else {
        serviceRequestCount++;
      }
    });
    print('notification count $notificationCount');
    return Column(
      children: [
        (notificationCount > 0 && serviceRequestCount == 0)
            ? AnimatedSize(
                //vsync: this,
                duration: const Duration(milliseconds: 300),
                child: Column(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(10),
                      width: double.infinity,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(15),
                        border:
                            Border.all(color: Theme.of(context).primaryColor),
                      ),
                      child: Column(
                        children: [
                          Text(
                            AppLocalizations.of(context)!.noServiceFound,
                            style: const TextStyle(
                                fontSize: 16, fontWeight: FontWeight.bold),
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          Text(
                            customerId == 15
                                ? AppLocalizations.of(context)!
                                    .noServiceRequestDescForStadium
                                : AppLocalizations.of(context)!
                                    .noServiceRequestDesc,
                            style: const TextStyle(height: 1.2),
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton(
                              style: ElevatedButton.styleFrom(
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(15.0),
                                ),
                              ),
                              onPressed: () async {
                                Navigator.of(context).pop();
                                //ApplicationUtil.showLoaderDialog(context, AppLocalizations.of(context)!.pleaseWait);
                                getIt<ServiceType>().type =
                                    ServiceRequestType.DIRECT_REPAIR;

                                /// RESET SERVICE REQUEST BLOC
                                BlocProvider.of<ServiceRequestBloc>(context)
                                    .add(ResetServiceRequestBloc());
                                //Navigator.of(context).pop();
                                Navigator.pushNamed(
                                        context,
                                        AfsServiceRequestAndRepairPage
                                            .routeName,
                                        arguments: ServiceType(
                                            type: ServiceRequestType
                                                .DIRECT_REPAIR))
                                    .then((shouldRefresh) {
                                  if (shouldRefresh is bool &&
                                      shouldRefresh == true) {
                                    repairListBloc.add(
                                        FetchRepairServiceRequestList(
                                            refresh: true, query: query));
                                  }
                                });
                              },
                              child: Container(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 14),
                                child: Text(
                                  AppLocalizations.of(context)!.startRepair,
                                  style: const TextStyle(
                                      color: Colors.white, fontSize: 18),
                                ),
                              ),
                            ),
                          )
                        ],
                      ),
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                  ],
                ),
              )
            : Container(),
        ListView.builder(
          padding: const EdgeInsets.all(0),
          shrinkWrap: true,
          physics: const ScrollPhysics(),
          itemCount: serviceRequestDetailList.length,
          itemBuilder: (context, index) {
            ServiceRequestDetail serviceRequestDetail =
                serviceRequestDetailList[index];
            return BlocBuilder<RepairButtonCubit, RepairButtonState>(
              builder: (context, state) {
                if (state is HideStartRepairButton ||
                    state is HidePartUsedButtonButton) {
                  if (BlocProvider.of<RepairButtonCubit>(context)
                          .selectedRequestDetail ==
                      serviceRequestDetail) {
                    return _getServiceRequestDetails(serviceRequestDetail);
                  }
                  return Container();
                } else {
                  return _getServiceRequestDetails(serviceRequestDetail);
                }
              },
            );
          },
        ),
      ],
    );
  }
}
