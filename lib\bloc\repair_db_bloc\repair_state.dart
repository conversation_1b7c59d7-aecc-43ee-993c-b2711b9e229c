part of 'repair_bloc.dart';

@immutable
abstract class RepairState {}

class RepairInitial extends RepairState {
  RepairInitial();
}

class RepairRequestSaving extends RepairState {}

class RepairSaved extends RepairState {
  final value;
  RepairSaved(this.value);
}

class RepairError extends RepairState {
  final String error;
  RepairError({required this.error});
}

class SendingPendingRepairToServer extends RepairState {}

class SentPendingRepairToServer extends RepairState {
  final int? count;
  SentPendingRepairToServer({this.count});
}

class NoPendingRepairInDatabase extends RepairState {
  final String? error;
  NoPendingRepairInDatabase({this.error});
}

class NetworkErrorInRepair extends RepairState {
  final String error;
  final int pendingCount;
  NetworkErrorInRepair({required this.error, required this.pendingCount});
}
