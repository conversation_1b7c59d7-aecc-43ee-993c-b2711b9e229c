import 'package:alink/widget/seperator.dart';
import 'package:alink/widget/service_type.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import '../data/model/audit_location.dart';
import '../data/model/audit_schedule.dart';
import '../data/repository/api_service.dart';
import '../util/app_color.dart';

class AuditSelectionSection extends StatefulWidget {
  int? selectedSegmentValue;
  String selectedAuditDescription;
  final List<AuditSchedules> auditSchedules;

  AuditSelectionSection(
      {Key? key,
      required this.selectedSegmentValue,
      required this.auditSchedules,
      required this.selectedAuditDescription})
      : super(key: key);

  @override
  _AuditSelectionSectionState createState() {
    return _AuditSelectionSectionState();
  }
}

class _AuditSelectionSectionState extends State<AuditSelectionSection> {
  String dropDownFirstElement = "";
  late int selectedSegmentValue;
  List<AuditSchedules> auditsSchedulesListTemporary = [];

  @override
  void initState() {
    if (InMemoryAudiData.selectedSegmentValueInMemory == null) {
      InMemoryAudiData.selectedSegmentValueInMemory = 0;
      selectedSegmentValue = 0;
      _setTitleValueDropDown(InMemoryAudiData.selectedSegmentValueInMemory!);
    } else {
      _setTitleValueDropDown(InMemoryAudiData.selectedSegmentValueInMemory!);
    }
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
// TODO: implement build
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Theme.of(context).primaryColor),
      ),
      child: Column(
        children: [
          const SizedBox(
            height: 15,
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                child: Text(
                  AppLocalizations.of(context)!.selectServiceType,
                  style: TextStyle(
                      color: Theme.of(context).primaryColor,
                      fontSize: 18,
                      fontWeight: FontWeight.bold),
                ),
              ),
            ],
          ),
          const SizedBox(
            height: 15,
          ),
          const DottedDivider(
            color: AppColor.redColor,
          ),
          const SizedBox(
            height: 15,
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 5),
            child: Container(
              width: double.infinity,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                border: Border.all(color: Theme.of(context).primaryColor),
              ),
              child: CupertinoSlidingSegmentedControl<int>(
                thumbColor: Theme.of(context).primaryColor,
                groupValue: widget.selectedSegmentValue,
                children: {
                  0: buildSegment("All", 0, widget.selectedSegmentValue!),
                  1: buildSegment("Audits", 1, widget.selectedSegmentValue!),
                  2: buildSegment("Tasks", 2, widget.selectedSegmentValue!),
                },
                onValueChanged: (value) {
                  setState(() {
                    widget.selectedSegmentValue = value;
                    InMemoryAudiData.selectedSegmentValueInMemory =
                        widget.selectedSegmentValue;
                    selectedSegmentValue = widget.selectedSegmentValue!;
                    print(auditsSchedulesListTemporary);
                    auditsSchedulesListTemporary = [];
                    print(widget.selectedSegmentValue);
                    print(auditsSchedulesListTemporary);
                    _setTitleValueDropDown(value!);
                  });
                },
              ),
            ),
          ),
          _getDescriptionDropdown(),
        ],
      ),
    );
  }

  Widget buildSegment(String text, int index, int groupValue) {
    return Container(
      width: double.maxFinite,
      alignment: Alignment.center,
      child: Text(
        text,
        style: TextStyle(
            fontSize: 18,
            color: index == groupValue
                ? Colors.white
                : Theme.of(context).primaryColor),
      ),
    );
  }

  _getDescriptionDropdown() {
    return FutureBuilder(
      future: getAllScheduledServiceDescription(),
      builder: (BuildContext context, AsyncSnapshot<dynamic> snapshot) {
        // auditsSchedulesListTemporary = [];
        if (auditsSchedulesListTemporary.isNotEmpty) {
          if (snapshot.hasData) {
            List<AuditSchedules> auditsSchedulesListTemporary = [];
            auditsSchedulesListTemporary = snapshot.data;
            if (widget.auditSchedules.isEmpty) {
              snapshot.data.forEach((element) {
                widget.auditSchedules.add(element);
              });
            }
            int res = auditsSchedulesListTemporary.indexWhere((element) =>
                element.description ==
                InMemoryAudiData.selectedAuditDescriptionInMemory);
            if (res >= 0) {
              widget.selectedAuditDescription =
                  auditsSchedulesListTemporary[res].description!;
            } else {
              widget.selectedAuditDescription =
                  auditsSchedulesListTemporary[0].description!;
              InMemoryAudiData.selectedAuditDescriptionInMemory =
                  auditsSchedulesListTemporary[0].description!;
            }
            auditsSchedulesListTemporary =
                auditsSchedulesListTemporary.toSet().toList();
            return AuditDescriptionDropdownWidget(
              auditDescriptionList: auditsSchedulesListTemporary,
              defaultAuditDescriptionType: widget.selectedAuditDescription == ""
                  ? auditsSchedulesListTemporary[0].description
                  : widget.selectedAuditDescription,
              selectedAuditDescription: (selectedServiceTypeInReturn) {
                setState(() {
                  InMemoryAudiData.selectedAuditDescriptionInMemory =
                      selectedServiceTypeInReturn;
                  int res = auditsSchedulesListTemporary.indexWhere((element) =>
                      (element.description == selectedServiceTypeInReturn));
                  if (res >= 0) {
                    InMemoryAudiData.selectedAuditDescriptionInMemory =
                        auditsSchedulesListTemporary[res].description;
                  } else {
                    InMemoryAudiData.selectedAuditDescriptionInMemory =
                        auditsSchedulesListTemporary[0].description;
                  }
                  widget.selectedAuditDescription = selectedServiceTypeInReturn;
                });
              },
            );
          } else {
            return Container();
          }
        } else {
          return Container(
            padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 5),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: DropdownButton(
                    hint: Container(
                        margin: const EdgeInsets.only(left: 20),
                        child: const Text(
                          "Loading...",
                          style: TextStyle(
                              color: Colors.white, fontWeight: FontWeight.bold),
                        )),
                    value: widget.selectedAuditDescription,
                    underline: Container(),
                    isExpanded: true,
                    focusColor: Colors.white,
                    items: <String>[].map((String value) {
                      return DropdownMenuItem<String>(
                        value: value,
                        child: Text(value),
                      );
                    }).toList(),
                    icon: const Icon(
                      Icons.arrow_drop_down,
                      color: Colors.white,
                      size: 35, // Add this
                    ),
                    onChanged: (Object? value) {
                      print("");
                    },
                  ),
                ),
              ],
            ),
          );
        }
      },
    );
  }

  Future<List<AuditSchedules>> getAllScheduledServiceDescription() async {
    auditsSchedulesListTemporary =
        await ApiService().fetchAuditScheduleDescription();
    //auditsSchedulesListTemporary = auditSchedules;
    if (InMemoryAudiData.selectedSegmentValueInMemory == 0 ||
        InMemoryAudiData.selectedSegmentValueInMemory == null) {
      auditsSchedulesListTemporary.sort((a, b) {
        if (a.description == "All Audits and Tasks") {
          return -1; // "All Audits and Tasks" should come before any other description
        } else if (b.description == "All Audits and Tasks") {
          return 1; // "All Audits and Tasks" should come before any other description
        } else {
          return a.description!
              .compareTo(b.description!); // Sort alphabetically
        }
      });
      //auditsSchedulesListTemporary.sort((a, b) => a.serviceType!.compareTo(b.serviceType!),);
      //auditsSchedulesListTemporary.removeAt(0);
      /*auditsSchedulesListTemporary.insert(
          0,
          AuditSchedules(
            description: dropDownFirstElement =="" ? _setTitleValueDropDown(InMemoryAudiData.selectedSegmentValueInMemory!) : dropDownFirstElement,
            serviceType: "ALL",
          ));*/
      print(auditsSchedulesListTemporary);
      return auditsSchedulesListTemporary;
    } else if (InMemoryAudiData.selectedSegmentValueInMemory == 1) {
      List<AuditSchedules> taskDescription = [];
      auditsSchedulesListTemporary
          .where((item) => item.serviceType == 'AUDIT')
          .forEach((item) => taskDescription.add(item));
      taskDescription.sort(
        (a, b) => a.description!.compareTo(b.description!),
      );
      //taskDescription.removeAt(0);
      taskDescription.insert(
          0,
          AuditSchedules(
            description: dropDownFirstElement == ""
                ? _setTitleValueDropDown(
                    InMemoryAudiData.selectedSegmentValueInMemory!)
                : dropDownFirstElement,
            serviceType: "AUDIT",
          ));
      print(taskDescription);
      return taskDescription;
    } else {
      List<AuditSchedules> auditDescription = [];
      auditsSchedulesListTemporary
          .where((item) => item.serviceType == 'TASK')
          .forEach((item) => auditDescription.add(item));
      auditDescription.sort(
        (a, b) => a.description!.compareTo(b.description!),
      );
      //auditDescription.removeAt(0);
      auditDescription.insert(
          0,
          AuditSchedules(
            description: dropDownFirstElement == ""
                ? _setTitleValueDropDown(
                    InMemoryAudiData.selectedSegmentValueInMemory!)
                : dropDownFirstElement,
            serviceType: "TASK",
          ));
      print(auditDescription);
      return auditDescription;
    }
  }

  String _setTitleValueDropDown(int value) {
    dropDownFirstElement = '';
    if (value == 0) {
      dropDownFirstElement += "All Audits and Tasks";
    } else if (value == 1) {
      dropDownFirstElement += "All Audits";
    } else if (value == 2) {
      dropDownFirstElement += "All Tasks";
    }
    return dropDownFirstElement;
  }
}
