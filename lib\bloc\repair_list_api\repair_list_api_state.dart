part of 'repair_list_api_bloc.dart';

@immutable
abstract class RepairListApiState {}

class RepairListApiInitial extends RepairListApiState {}

class FetchingRepairServiceRequestList extends RepairListApiState {
  final List<RepairServiceRequest> oldList;
  final bool isFirstFetch;

  FetchingRepairServiceRequestList(this.oldList, {this.isFirstFetch = false});
}

class FetchedRepairServiceError extends RepairListApiState {
  final String errorMessage;

  FetchedRepairServiceError({required this.errorMessage});
}

class FetchedRepairServiceRequestList extends RepairListApiState {
  final RepairServiceRequestResponse repairServiceRequestList;

  FetchedRepairServiceRequestList(this.repairServiceRequestList);
}

class FetchingLopaRepairServiceRequestList extends RepairListApiState {
  FetchingLopaRepairServiceRequestList();
}

class FetchedLopaRepairServiceError extends RepairListApiState {
  final String errorMessage;

  FetchedLopaRepairServiceError({required this.errorMessage});
}

class FetchedLopaRepairServiceRequestList extends RepairListApiState {
  final List<RepairServiceRequest> repairServiceRequestList;

  FetchedLopaRepairServiceRequestList(this.repairServiceRequestList);
}
