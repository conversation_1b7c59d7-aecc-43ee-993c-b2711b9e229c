part of 'service_request_bloc.dart';

@immutable
abstract class ServiceRequestEvent {}

class FetchServiceRequest extends ServiceRequestEvent {}

class SaveServiceRequest extends ServiceRequestEvent {
  final ServiceRequestCompanion serviceRequestCompanion;
  SaveServiceRequest({required this.serviceRequestCompanion});
}

class UpdateServiceRequest extends ServiceRequestEvent {
  final ServiceRequestData serviceRequestData;
  UpdateServiceRequest({required this.serviceRequestData});
}

class DeleteServiceRequest extends ServiceRequestEvent {
  final ServiceRequestCompanion serviceRequestCompanion;
  DeleteServiceRequest({required this.serviceRequestCompanion});
}

class ServiceRequestSendNotification extends ServiceRequestEvent {
  final int count;
  final String message;
  ServiceRequestSendNotification({required this.count, required this.message});
}

class FetchPendingServiceRequestFromDB extends ServiceRequestEvent {}

class SendPendingServiceRequestToServer extends ServiceRequestEvent {}

class DismissServiceRequestNotification extends ServiceRequestEvent {}

class DismissServiceRequestErrorNotification extends ServiceRequestEvent {}

class ServiceRequestSendErrorNotification extends ServiceRequestEvent {
  final String message;
  ServiceRequestSendErrorNotification({required this.message});
}

///Barcode DATABASE
///save scanned barcode to database(new equipments and existing equipments)
class UpdateBarcodeDataInAudit extends ServiceRequestEvent {
  final int auditId;
  final String barcodeNumber;
  final bool? isActiveEquipment;
  final String? oldLocationId;
  final String? newLocationId;
  final List<AuditEquipment> auditEquipmentList;
  final double? latitude;
  final double? longitude;
  UpdateBarcodeDataInAudit(
      this.auditId,
      this.barcodeNumber,
      this.isActiveEquipment,
      this.oldLocationId,
      this.newLocationId,
      this.auditEquipmentList,
      {this.latitude,
      this.longitude});
}

class UpdateEquipmentList extends ServiceRequestEvent {
  final int auditId;
  final List<AuditEquipment> auditList;
  UpdateEquipmentList(this.auditId, this.auditList);
}

class SaveEquipmentDataFromAuditStatus extends ServiceRequestEvent {
  final int auditId;
  final List<AuditEquipment> auditEquipment;
  final bool updateRequired;
  SaveEquipmentDataFromAuditStatus(this.auditEquipment, this.auditId,
      {this.updateRequired = true});
}

class GetEquipmentDataFromAuditStatus extends ServiceRequestEvent {
  final int auditId;
  GetEquipmentDataFromAuditStatus(this.auditId);
}

class GetEquipmentDataFromAuditStatusForMap extends ServiceRequestEvent {
  final int auditId;
  GetEquipmentDataFromAuditStatusForMap(this.auditId);
}

class UpdateEquipmentDataFromAuditStatus extends ServiceRequestEvent {
  final int auditId;
  final String barcode;
  final bool? isEquipmentFromConfirmAuditPage;
  UpdateEquipmentDataFromAuditStatus(
      this.auditId, this.barcode, this.isEquipmentFromConfirmAuditPage);
}

class DeleteAuditFromTable extends ServiceRequestEvent {
  final int auditId;
  DeleteAuditFromTable({required this.auditId});
}

class UpdateEquipmentDataFromAuditStatusByName extends ServiceRequestEvent {
  final int auditId;
  final String equipmentName;
  final AuditEquipment auditEquipment;
  final List<AuditEquipment> auditEquipmentList;
  final bool? isActiveEquipment;
  final String? oldLocationId;
  UpdateEquipmentDataFromAuditStatusByName(
      this.auditId,
      this.equipmentName,
      this.auditEquipment,
      this.auditEquipmentList,
      this.isActiveEquipment,
      this.oldLocationId);
}

class ResetServiceRequestBloc extends ServiceRequestEvent {}
