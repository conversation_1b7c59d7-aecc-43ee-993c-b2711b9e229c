import 'dart:async';
import 'dart:convert';

import 'package:alink/data/model/audit_response.dart';
import 'package:alink/data/model/barcode_response.dart';
import 'package:alink/data/model/customization.dart';
import 'package:alink/data/model/equipment.dart' as eq;
import 'package:alink/data/model/service_request_detail.dart';
import 'package:alink/data/model/terminal.dart';
import 'package:alink/data/repository/api_repository.dart';
import 'package:alink/logger/logger.dart';
import 'package:bloc/bloc.dart';
import 'package:flutter/foundation.dart';

part 'api_event.dart';
part 'api_state.dart';

class ApiBloc extends Bloc<ApiEvent, ApiState> {
  final ApiRepository apiRepository;
  ApiBloc({required this.apiRepository}) : super(ApiInitial());
  int offset = 0;
  static int? serviceCount;
  static int? serviceCompletedCount;
  List<Equipment> validatedEquipmentList = [];
  @override
  Stream<ApiState> mapEventToState(
    ApiEvent event,
  ) async* {
    if (event is CallBarCodeApi) {
      yield BarcodeApiCalling();
      try {
        var response = await apiRepository.fetchDatafromBarcodeNumber(
            event.barcodeNumber, true,
            latitude: event.lattitude, longitude: event.longitude);
        if (response is BarcodeResponse) {
          yield BarcodeApiCalled(response, isFromMap : event.isFromMap);
        } else {
          yield BarcodeApiError(
              errorMessage: response, barcodeNumber: event.barcodeNumber);
        }
      } catch (_) {}
    }
    if (event is ResetBarCodeApi) {
      yield BarCodeApiInitial();
    }

    if (event is CheckBarCodeApi) {
      yield CheckBarcodeApiCalling();
      try {
        var response = await apiRepository.fetchDatafromBarcodeNumber(
            event.barcodeNumber, false,
            latitude: event.lattitude, longitude: event.longitude);
        if (response is BarcodeResponse) {
          yield CheckBarcodeApiCalled(response);
        } else {
          yield CheckBarcodeApiError(
              errorMessage: response, barcodeNumber: event.barcodeNumber);
        }
      } catch (_) {}
    }

    if (event is GetCustomizationData) {
      yield GettingCustomizationData();
      try {
        var response = await apiRepository.fetchCustomizationData();
        if (response is Customization) {
          yield CustomizationDataFetched(response);
        } else {
          yield CustomizationDataError(errorMessage: response);
        }
      } catch (_) {}
    }

    if (event is FetchTerminalData) {
      yield FetchingTerminalData();
      try {
        var response = await apiRepository.fetchTerminalData(event.locationId);
        if (response is List<Terminal>) {
          yield FetchedTerminalData(response);
        } else if (response is FetchTerminalError) {
          yield FetchTerminalError(
              errorMessage: 'Error while fetching: ' + response.errorMessage);
        }
      } catch (_) {}
    }
    if (event is InitialTerminalData) {
      yield TerminalDataInitial();
    }

    if (event is FetchSingleServiceDetail) {
      yield FetchingSingleServiceDetail();
      try {
        var response = await apiRepository.fetchServiceRequestDetailById(
            event.requestId,
            event.isTimedService!,
            event.isFromAudit,
            event.isRestricted);
        if (response is List<ServiceRequestDetail>) {
          yield FetchedSingleServiceDetail(response);
        } else {
          yield SingleServiceDetailError(errorMessage: response);
        }
      } catch (error) {
        yield SingleServiceDetailError(errorMessage: error.toString());
      }
    }

    if (event is CreateNewAudit) {
      yield CreatingNewAudit();
      try {
        var serviceType = "";
        if (event.serviceType == "0" ||
            event.serviceType == null /*|| event.serviceType=="1"*/) {
          serviceType = "AUDIT";
        } else {
          serviceType = "TASK";
        }
        Logger.i(
            "creating audit with service type: $serviceType at location ${event.locationId}");
        final response = await apiRepository.createNewAudit(
            event.locationId, event.comment, serviceType);
        Logger.i("RESPONSE => $response");
        if (response is String && response == 'SAVED') {
          yield NewAuditCreated(response: response);
        } else {
          yield ErrorCreatingNewAudit(errorMessage: jsonDecode(response));
        }
      } catch (error) {
        yield ErrorCreatingNewAudit(errorMessage: error.toString());
      }
    }
    if (event is ValidateAuditBarcodes) {
      yield ValidatingAuditBarcodes();
      try {
        var response = await apiRepository.validateAuditBarcode(
            event.equipmentList, event.auditId);
        if (response is List<Equipment>) {
          validatedEquipmentList = response;
          yield ValidatedAuditBarcodes(equipmentList: validatedEquipmentList);
        } else {
          yield ValidateError(errorMessage: response);
        }
      } catch (error) {
        yield ValidateError(errorMessage: error.toString());
      }
    }
    if (event is RefreshValidatedAuditData) {
      yield ValidatingAuditBarcodes();
      try {
        yield ValidatedAuditBarcodes(equipmentList: validatedEquipmentList);
      } catch (error) {
        yield ValidateError(errorMessage: error.toString());
      }
    }
    if (event is GetServiceRequestCount) {
      yield GettingServiceRequestCount();
      try {
        var response = await apiRepository.getServiceRequestCount();
        if (response is Map<String, dynamic>) {
          serviceCount = response['CREATED_SERVICE_REQUESTS_COUNT'];
          serviceCompletedCount = response['CLOSED_SERVICE_REQUESTS_COUNT'];
          if (kDebugMode) {
            print('response');
            print(response);
          }
          yield FetchedServiceRequestCount(data: response);
        } else {
          yield FetchedServiceRequestCountError(errorMessage: response);
        }
      } catch (error) {
        yield FetchedServiceRequestCountError(errorMessage: error.toString());
      }
    }
    if (event is GetAirCraftTails) {
      yield FetchingAirCraftTails();
      try {
        var response = await apiRepository.fetchAirCraftTails(
            fleetName: event.fleetName, subFleetName: event.subFleetName);
        if (response is List<dynamic>) {
          yield FetchedAirCraftTails(data: response);
        } else {
          yield FetchAirCraftTailsApiError(errorMessage: response);
        }
      } catch (_) {}
    }
    if (event is GetAirCraftFleet) {
      yield FetchingAirCraftFleet();
      try {
        var response = await apiRepository.fetchAirCraftFleet();
        if (response is Map<String, List<String>>) {
          yield FetchedAirCraftFleet(data: response);
        } else {
          yield FetchAirCraftFleetApiError(errorMessage: response);
        }
      } catch (error) {
        yield CancelServiceRequestError(errorMessage: error.toString());
      }
    }
    if (event is CancelServiceRequest) {
      yield CancellingServiceRequest();
      try {
        var response = await apiRepository.cancelServiceRequest(
            event.serviceRequestId, event.comment);
        if (response is int) {
          yield CancelledServiceRequest();
        } else {
          yield CancelServiceRequestError(errorMessage: response.toString());
        }
      } catch (error) {
        yield CancelServiceRequestError(errorMessage: error.toString());
      }
    }
  }
}
