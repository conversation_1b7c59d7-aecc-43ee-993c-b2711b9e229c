import 'package:alink/bloc/part_items/part_items_bloc.dart';
import 'package:alink/cubit/lopa/selected_equipment_cubit.dart';
import 'package:alink/data/model/available_part.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class PartListDialog extends StatefulWidget {
  final String query;
  final Function() onClose;
  const PartListDialog({Key? key, required this.query, required this.onClose})
      : super(key: key);

  @override
  _PartListDialogState createState() => _PartListDialogState();
}

class _PartListDialogState extends State<PartListDialog> {
  @override
  void initState() {
    partItemsBloc.add(FetchAvailablePartList(query: widget.query));
    super.initState();
  }

  PartItemsBloc get partItemsBloc => BlocProvider.of<PartItemsBloc>(context);

  @override
  Widget build(BuildContext context) {
    return Dialog(
      insetPadding: const EdgeInsets.symmetric(horizontal: 10),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      elevation: 0,
      backgroundColor: Colors.transparent,
      child: Container(
        constraints: const BoxConstraints(maxWidth: 500),
        child: Stack(
          alignment: Alignment.topRight,
          children: <Widget>[
            Container(
              //width: MediaQuery.of(context).size.width,
              padding: const EdgeInsets.only(
                  left: 20,
                  top: 20,
                  //top: avatarRadius + padding,
                  right: 20,
                  bottom: 10),
              //margin: EdgeInsets.only(top: avatarRadius),
              decoration: BoxDecoration(
                  shape: BoxShape.rectangle,
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(15),
                  boxShadow: const [
                    BoxShadow(
                        color: Colors.black,
                        offset: Offset(0, 10),
                        blurRadius: 10),
                  ]),
              child: BlocConsumer<PartItemsBloc, PartItemsState>(
                listener: (context, state) {
                  if (state is FetchedAvailablePartList) {
                    //Update repait lopa part count in shopping bag
                    if (SelectedFleetCubit.selectedTail != null) {
                      SelectedFleetCubit.selectedTail!.serviceRequestCount =
                          getCount(state.partList);
                      //Update list in aircraft selection page
                      /* SelectedFleetCubit.tailList
                          .where((element) =>
                              SelectedFleetCubit.selectedTail!.tailNo ==
                              element.tailNo)
                          .first
                          .serviceRequestCount = getCount(state.partList);*/
                      widget.onClose();
                    }
                  }
                },
                builder: (context, state) {
                  return Column(
                    mainAxisSize: MainAxisSize.min,
                    children: <Widget>[
                      const SizedBox(
                        height: 5,
                      ),
                      _getPartTitle(state),
                      _getPartList(state),
                      Column(
                        children: [
                          const Divider(
                            thickness: 1.5,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              Align(
                                alignment: Alignment.bottomRight,
                                child: TextButton(
                                  onPressed: () {
                                    Navigator.pop(context);
                                  },
                                  child: Text(
                                    AppLocalizations.of(context)!.okay,
                                    style: const TextStyle(fontSize: 18),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ],
                  );
                },
              ),
            ),
            IconButton(
              onPressed: () {
                Navigator.pop(context);
              },
              icon: const FaIcon(FontAwesomeIcons.times),
            ),
          ],
        ),
      ),
    );
  }

  _getPartTitle(PartItemsState state) {
    if (state is FetchedAvailablePartList) {
      return Column(
        children: [
          Text(
            state.partList.isNotEmpty
                ? '${AppLocalizations.of(context)!.partList} (${getCount(state.partList)})'
                : '${AppLocalizations.of(context)!.partList}',
            style: const TextStyle(
                fontSize: 20, fontWeight: FontWeight.w600, height: 1.2),
          ),
          const Divider(
            thickness: 1.5,
          ),
        ],
      );
    }
    return Column(
      children: [
        Text(
          AppLocalizations.of(context)!.fetchingParts,
          style: const TextStyle(fontSize: 22, fontWeight: FontWeight.w600),
        ),
        const Divider(
          thickness: 1.5,
        ),
      ],
    );
  }

  _getPartList(PartItemsState state) {
    if (state is FetchedAvailablePartList) {
      if (state.partList.isNotEmpty) {
        return Flexible(
          child: ListView.separated(
            physics: const BouncingScrollPhysics(),
            separatorBuilder: (context, index) => const Divider(),
            shrinkWrap: true,
            itemCount: state.partList.length,
            itemBuilder: (context, index) {
              AvailablePart availablePart = state.partList[index];
              return ListTile(
                contentPadding: EdgeInsets.zero,
                visualDensity: const VisualDensity(horizontal: 0, vertical: -4),
                //leading: Icon(widget.seatItemList[index].icon),
                title: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${AppLocalizations.of(context)!.partName}: ${availablePart.partName}',
                      style: TextStyle(
                        fontSize: 18,
                        color: Colors.blue[800],
                      ),
                    ),
                    const SizedBox(
                      height: 2,
                    ),
                    Text(
                      '${availablePart.partLabel}: ${availablePart.partId}',
                      style: const TextStyle(
                        fontSize: 16,
                        color: Colors.black87,
                      ),
                    ),
                    const SizedBox(
                      height: 5,
                    )
                  ],
                ),
                subtitle: Text(
                  '${AppLocalizations.of(context)!.quantity}: ${availablePart.quantity}',
                  style: const TextStyle(fontSize: 16),
                ),
              );
            },
          ),
        );
      } else {
        return Center(
            child: Text(
          AppLocalizations.of(context)!.noPartFound,
          style: const TextStyle(fontSize: 22),
        ));
      }
    } else if (state is FetchAvailablePartListError) {
      return Center(
        child: Text(state.errorMessage),
      );
    }
    return const Center(
      child: CircularProgressIndicator(),
    );
  }

  int getCount(List<AvailablePart> partList) {
    int sum = 0;
    for (var e in partList) {
      sum += e.quantity;
    }
    return sum;
  }
}
