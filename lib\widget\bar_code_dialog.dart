import 'package:alink/bloc/api/api_bloc.dart';
import 'package:alink/data/model/barcode_response.dart';
import 'package:alink/pages/airport/service_request/add_service_request_page.dart';
import 'package:alink/pages/airport/service_request/service_result_page.dart';
import 'package:alink/service_locator.dart';
import 'package:alink/util/application_util.dart';
import 'package:alink/util/enums/app_enum.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

class BarCodeDialog extends StatefulWidget {
  Function callback;
  VoidCallback onClose;
  BarCodeDialog({Key? key, required this.callback, required this.onClose})
      : super(key: key);

  @override
  _BarCodeDialogState createState() => _BarCodeDialogState();
}

class _BarCodeDialogState extends State<BarCodeDialog> {
  late BarcodeSearchResult searchType;
  var _okayText = "Okay";
  late TextEditingController _bardcodeCcontroller;

  @override
  void initState() {
    searchType = BarcodeSearchResult.INITIAL;
    _bardcodeCcontroller = TextEditingController();
    super.initState();
  }

  ApiBloc get apiBloc => BlocProvider.of<ApiBloc>(context);

  @override
  Widget build(BuildContext ctx) {
    _okayText = AppLocalizations.of(context)!.okay;
    return AlertDialog(
      titlePadding: const EdgeInsets.only(left: 20),
      title: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
              padding: const EdgeInsets.only(right: 120, top: 30),
              child: Text(AppLocalizations.of(context)!.barcode)),
          Container(
            margin: const EdgeInsets.only(
              right: 10,
            ),
            child: GestureDetector(
                onTap: () {
                  Navigator.pop(context);
                  widget.onClose();
                },
                child: const Icon(Icons.close)),
          ),
        ],
      ),
      contentPadding: const EdgeInsets.only(top: 20),
      content: Container(
        padding: const EdgeInsets.symmetric(vertical: 0, horizontal: 20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const SizedBox(
              height: 10,
            ),
            TextField(
              controller: _bardcodeCcontroller,
              decoration: InputDecoration(
                border: const OutlineInputBorder(
                  borderRadius: BorderRadius.all(
                    Radius.circular(5.0),
                  ),
                ),
                prefixIcon: Icon(FontAwesomeIcons.barcode),
                hintText: AppLocalizations.of(context)!.enterBarcode,
                labelText: AppLocalizations.of(context)!.enterBarcode,
              ),
              keyboardType: TextInputType.text,
            ),
            const SizedBox(
              height: 20,
            ),
            searchType == BarcodeSearchResult.SEARCHING
                ? _getLinearProgressBar()
                : Container()
          ],
        ),
      ),
      actions: [
        BlocConsumer<ApiBloc, ApiState>(
          listener: (listenerContext, state) {
            if (state is BarcodeApiCalled &&
                getIt<ServiceType>().type == ServiceRequestType.ADD) {
              apiBloc.add(ResetBarCodeApi());
              //Navigator.pop(ctx);
              getIt<BarcodeResponse>().barcodeResponseInstance =
                  state.barcodeResponse;
              if (state.barcodeResponse.serviceRequestCount == null ||
                  state.barcodeResponse.serviceRequestCount == 0) {
                /// GOTO NEW ADD SERVICE PAGE IF THERE IS NO EXISTING SERVICE
                Navigator.pushNamed(context, AddServiceRequestPage.routeName,
                    arguments: ServiceType(
                      type: ServiceRequestType.ADD,
                    ));
              } else {
                /// GOTO NEW RESULT PAGE PAGE IF THERE IS  EXISTING SERVICE
                Navigator.pushNamed(context, BardCodeResultPage.routeName,
                    arguments: ServiceType(
                      type: ServiceRequestType.ADD,
                    ));
              }
            }
            /*    else if (state is BarcodeApiCalled &&
                getIt<ServiceType>().type ==
                    ServiceRequestType.REPLACE_BARCODE) {
              print('REPLACING============DIALOG>');
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content:
                      Text('Barcode already assigned to an equipment replace'),
                      Text('Barcode already assigned to an equipment replace'),
                ),
              );
            } */
            else if (state is BarcodeApiCalled &&
                getIt<ServiceType>().type ==
                    ServiceRequestType.MOVE_EQUIPMENT) {
              print('MOVED CALLED FROM DIALOG');
              Navigator.pushNamed(context, AddServiceRequestPage.routeName,
                  arguments:
                      ServiceType(type: ServiceRequestType.MOVE_EQUIPMENT));
            }
            /*  else if (state is BarcodeApiCalled &&
                getIt<ServiceType>().type ==
                    ServiceRequestType.ASSIGN_EQUIPMENT) {
              ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                  content: Text('Barcode already assigned to an equipment')));
            }*/
          },
          builder: (context, state) {
            bool isCalling = state is BarcodeApiCalling;

            return AnimatedContainer(
              duration: const Duration(milliseconds: 500),
              height: isCalling ? 0 : 50,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  ElevatedButton(
                    onPressed: () {
                      if (_okayText == AppLocalizations.of(context)!.okay) {
                        if (_bardcodeCcontroller.text.isEmpty) {
                          ApplicationUtil.showSnackBar(
                              context: context,
                              message:
                                  AppLocalizations.of(context)!.enterBarcodeNo);
                        } else {
                          widget.callback(_bardcodeCcontroller.text);
                          /*apiBloc.add(CallBarCodeApi(
                              barcodeNumber: _bardcodeCcontroller.text));*/
                        }
                      }
                      /*if (_okayText == "Continue") {
                        _okayText = "Okay";
                      }*/
                    },
                    child: Text(_okayText),
                  ),
                ],
              ),
            );
          },
        ),
        /* Container(
          height: 25,
          width: 25,
          child: CircularProgressIndicator(),
        )*/
      ],
    );
  }

  _getLinearProgressBar() => const Column(
        children: [
          LinearProgressIndicator(),
          SizedBox(
            height: 10,
          ),
        ],
      );
}
