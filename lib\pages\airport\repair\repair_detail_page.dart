import 'dart:convert';
import 'dart:io';

import 'package:alink/bloc/api/api_bloc.dart';
import 'package:alink/bloc/part_items/selected_part_item_bloc.dart';
import 'package:alink/bloc/repair_db_bloc/repair_bloc.dart';
import 'package:alink/cubit/location/location_cubit.dart';
import 'package:alink/cubit/pages/repair_detail/repair_button_cubit.dart';
import 'package:alink/cubit/pages/repair_detail/repair_detail_api_cubit.dart';
import 'package:alink/data/model/barcode_response.dart';
import 'package:alink/data/model/customization.dart';
import 'package:alink/data/model/drop_down_option.dart';
import 'package:alink/data/model/extension.dart';
import 'package:alink/data/model/location_detail.dart';
import 'package:alink/data/model/part_item_response.dart';
import 'package:alink/data/model/repair_service_request.dart';
import 'package:alink/data/model/service_request_detail.dart';
import 'package:alink/database/database.dart';
import 'package:alink/pages/airport/repair/search_item_part_page.dart';
import 'package:alink/pages/airport/service_request/add_service_request_page.dart';
import 'package:alink/pages/airport/service_request/widget/location_label.dart';
import 'package:alink/pages/auth/login_page.dart';
import 'package:alink/pages/full_image_view.dart';
import 'package:alink/pages/image_editor/edit_image_home_page.dart';
import 'package:alink/provider/locationProvider.dart';
import 'package:alink/scanner/barcode/ai_scanner_page.dart';
import 'package:alink/service_locator.dart';
import 'package:alink/util/app_color.dart';
import 'package:alink/util/app_constant.dart';
import 'package:alink/util/application_util.dart';
import 'package:alink/util/enums/app_enum.dart';
import 'package:alink/widget/buttons.dart';
import 'package:alink/widget/location_widget.dart';
import 'package:alink/widget/seperator.dart';
import 'package:alink/widget/service_request_detail_widget.dart';
import 'package:alink/widget/tooltip_shape.dart';
import 'package:drift/drift.dart' as moor;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'package:http/http.dart' as http;
import 'package:image_picker/image_picker.dart';
import 'package:location/location.dart' as gps;
import 'package:provider/provider.dart';

import 'package:shared_preferences/shared_preferences.dart';

import '../../../data/model/singleton_model.dart';
import '../../../logger/logger.dart';

class RepairDetailPage extends StatefulWidget {
  static const routeName = "repair-detail";
  final RepairDetailPageParams? repairServiceRequest;
  const RepairDetailPage({Key? key, this.repairServiceRequest})
      : super(key: key);

  @override
  _RepairDetailPageState createState() => _RepairDetailPageState();
}

class _RepairDetailPageState extends State<RepairDetailPage> {
  static const String className = '_RepairDetailPageState';

  String? pickedImage;
  var imageList = [];
  bool showAlertDialogWhileExist = false;

  bool isLocationConfirmed = false;
  bool isLocationFreezed = true;

  bool changeTerminalControlDisabled = true;
  LocationProvider? locationProvider;

  List<Map<String, dynamic>> selectedLocation = [];
  String selectedLocationId = '';
  String endLocationCategory = '';
  String endLocationName = '';
  List<LocationDetail> locationList = [];
  final ImagePicker _picker = ImagePicker();
  String? base64Img;

  // EXTENSION LIST FROM SERVER
  List<Extension> extensionList = [];

  // Extension submit response map
  Map<String, dynamic> extensionMap = {};
  bool isValueNotExist = true;
  gps.LocationData? locationData;
  Logger logger = Logger();
  bool fetchingLocation = false;

  bool isFirstTime = true;

  @override
  void initState() {
    commentTextEditingController = TextEditingController();
    _initEquipmentLocation(widget.repairServiceRequest!.repairServiceRequest);
    _initExtentionData();
    _initLocation();
    super.initState();
  }

  @override
  void dispose() {
    commentTextEditingController.dispose();
    super.dispose();
  }

  RepairBloc get repairBloc => BlocProvider.of<RepairBloc>(context);

  ApiBloc get apiBloc => BlocProvider.of<ApiBloc>(context);

  LocationCubit get locationCubit => BlocProvider.of<LocationCubit>(context);

  RepairDetailApiCubit get repairDetailApiCubit =>
      BlocProvider.of<RepairDetailApiCubit>(context);

  RepairButtonCubit get startRepairBtnCubit =>
      BlocProvider.of<RepairButtonCubit>(context);

  SelectedPartItemBloc get selectedPartBloc =>
      BlocProvider.of<SelectedPartItemBloc>(context);
  late TextEditingController commentTextEditingController;

  @override
  Widget build(BuildContext context) {
    print('====================widget.repairServiceRequest===================');
    print(widget.repairServiceRequest);
    Logger.i("Class Name: " + className);
    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: WillPopScope(
        child: Scaffold(
          body: Center(
            child: Container(
              constraints: const BoxConstraints(maxWidth: 500),
              child: Column(
                children: [
                  ApplicationUtil.displayNotificationWidgetIfExist(
                      context, RepairDetailPage.routeName),
                  _getRepairDetailAppBar(),
                  _getRepairContentBody()
                ],
              ),
            ),
          ),
          floatingActionButton:
              BlocBuilder<RepairButtonCubit, RepairButtonState>(
            builder: (context, state) {
              return FloatingActionButton(
                onPressed: () {
                  if (state is HideStartRepairButton ||
                      state is HidePartUsedButtonButton) {
                    showAlertDialogWhileExist = true;
                    ApplicationUtil.showUnsavedWarningAlertDialog(context);
                  } else {
                    showAlertDialogWhileExist = false;
                    Navigator.pop(context);
                  }
                },
                child: Container(
                    margin: const EdgeInsets.only(right: 5),
                    child: const FaIcon(FontAwesomeIcons.chevronLeft)),
              );
            },
          ),
        ),
        onWillPop: () async {
          if (showAlertDialogWhileExist) {
            ApplicationUtil.showUnsavedWarningAlertDialog(context);
          } else {
            Navigator.pop(context);
          }
          return false;
        },
      ),
    );
  }

  _getRepairContentBody() {
    return Expanded(
      child: SingleChildScrollView(
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 10),
          child: Column(
            children: [
              Column(
                children: [
                  ServiceRequestDetailWidget(
                    equipmentId: widget.repairServiceRequest!
                        .repairServiceRequest!.equipmentId!,
                    showCancelIcon: true,
                    isTimedService:
                        widget.repairServiceRequest!.isTimedService!,
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  BlocConsumer<RepairButtonCubit, RepairButtonState>(
                    listener: (context, state) {
                      if (state is HideStartRepairButton ||
                          state is HidePartUsedButtonButton) {
                        print('==============INIT===============');

                        _initNewTerminalAndGatesForSelectedService(
                            startRepairBtnCubit.selectedRequestDetail);
                      }
                    },
                    builder: (context, state) {
                      if (state is HideStartRepairButton ||
                          state is HidePartUsedButtonButton) {
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _getLocationBox(),
                            const SizedBox(
                              height: 10,
                            ),
                            isLocationConfirmed
                                ? _getPartUsedBox()
                                : Container(),
                            const SizedBox(
                              height: 10,
                            ),
                          ],
                        );
                      } else {
                        return Container();
                      }
                    },
                  ),
                ],
              ),
              BlocBuilder<RepairButtonCubit, RepairButtonState>(
                builder: (context, state) {
                  if (state is HidePartUsedButtonButton) {
                    return Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [_generateExtensionUI(), _getImagesBox()],
                    );
                  } else {
                    return Container();
                  }
                },
              ),
              const SizedBox(
                height: 10,
              ),
            ],
          ),
        ),
      ),
    );
  }

  _getImages() {
    if (imageList == null || imageList.isEmpty) {
      return _imagePlaceHolder();
    }
    return SizedBox(
      height: 90,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: imageList.length,
        itemBuilder: (context, index) {
          var base64Img = imageList[index];
          if (index == 0) {
            return Row(
              children: [
                _imagePlaceHolder(),
                _getSingleImage(base64Img, index)
              ],
            );
          } else {
            return _getSingleImage(base64Img, index);
          }
        },
      ),
    );
  }

  _imagePlaceHolder() => InkWell(
        onTap: () async {
          SharedPreferences sharedPreference = getIt<SharedPreferences>();
          String type =
              sharedPreference.getString("cameraPref") ?? AppConstant.CAMERA;
          if (type == AppConstant.CAMERA) {
            _onImageButtonPressed(ImageSource.camera, context: context);
          }
        },
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 10),
          decoration: BoxDecoration(
              border: Border.all(color: Colors.grey),
              borderRadius: BorderRadius.circular(15)),
          width: 85,
          height: 85,
          child: Center(
            child: FaIcon(
              FontAwesomeIcons.solidCamera,
              size: 30,
              color: Theme.of(context).primaryColor,
            ),
          ),
        ),
      );

  _getLocationBox() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Theme.of(context).primaryColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(
            height: 10,
          ),
          Text(
            AppLocalizations.of(context)!.confirmTerminalAndGate,
            style: TextStyle(
                color: Theme.of(context).primaryColor,
                fontSize: 20,
                fontWeight: FontWeight.bold),
          ),
          const SizedBox(
            height: 10,
          ),
          const DottedDivider(
            color: AppColor.redColor,
          ),
          const SizedBox(
            height: 10,
          ),
          Text(
            AppLocalizations.of(context)!.confirmLocationMessage,
            style: const TextStyle(
              color: AppColor.greyTextColor,
              fontSize: 16,
            ),
          ),
          const SizedBox(
            height: 10,
          ),
          _getLocationDropDown(),
          const SizedBox(
            height: 10,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              _confirmLocationButton(),
              const SizedBox(
                width: 18,
              ),
              _getChangeLocationButton()
            ],
          )
        ],
      ),
    );
  }

  _getLocationDropDown() {
    if (isFirstTime) {
      locationCubit.getLocationData();
      print("_getLocationDropDown");

      isFirstTime = false;
    }
    return BlocConsumer<LocationCubit, LocationState>(
      listener: (context, state) {
        if (state is FetchLocationDataError) {
          if (state.errorMessage == ApiResponse.INVALID_AUTH) {
            Navigator.pushNamedAndRemoveUntil(
                context, LoginPage.routeName, (route) => false,
                arguments: true);
          } else {
            ApplicationUtil.showSnackBar(
                context: context, message: state.errorMessage);
          }
        }
        if (state is FetchedLocationData) {
          locationList = state.locationList;
          //GATE INIT FOR EQUIPMENT MOVEMENT
          /* if (getIt<ServiceType>().type == ServiceRequestType.MOVE_EQUIPMENT) {
            gatesList = [];
            gatesList.add(selectedGate);
            for (var element in dataList) {
              if (selectedTerminal.name == element.name) {
                for (var element in element.gate!) {
                  if (gatesList.indexWhere(
                          (temp) => temp.locationId == element.locationId) <
                      0) {
                    gatesList.add(LocationChoice(
                        name: element.name!, locationId: element.locationId!));
                  }
                }
              }
            }
          }*/
        }
      },
      builder: (context, state) {
        if (state is FetchedLocationData) {
          return locationList.isNotEmpty
              ? LocationDropdownWidget(
                  locationList: locationList,
                  defaultLocationId: widget.repairServiceRequest!
                      .repairServiceRequest!.equipmentLocationId,
                  /*widget.repairServiceRequest!.newLocationId ??
                          widget.repairServiceRequest!.currentLocationId,*/
                  disableDropDown: isLocationFreezed,
                  onLocationSelected: (value, name, category) {
                    selectedLocationId = value;
                    endLocationName = name;
                    endLocationCategory = category;
                  },
                )
              : _getDefaultLocationLabel();
        } else {
          return _getDefaultLocationLabel();
        }
      },
    );
  }

  Widget _getDefaultLocationLabel() => LocationLabel(
        locationMap: selectedLocation,
      );

  _confirmLocationButton() => !isLocationConfirmed
      ? SizedBox(
          width: 140,
          child: ElevatedButton(
            style: ButtonStyle(
              padding: MaterialStateProperty.all(
                const EdgeInsets.symmetric(vertical: 14, horizontal: 20),
              ),
              backgroundColor:
                  MaterialStateProperty.all(AppColor.greenSentColor),
              shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(15.0),
                ),
              ),
            ),
            onPressed: () {
              if (selectedLocationId.isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                        AppLocalizations.of(context)!.pleaseSelectLocation),
                  ),
                );
              } else {
                setState(() {
                  isLocationConfirmed = true;
                  isLocationFreezed = true;
                });
              }
            },
            child: Text(
              AppLocalizations.of(context)!.confirm,
              style: const TextStyle(color: Colors.white, fontSize: 16),
            ),
          ),
        )
      : Container();

  _getChangeLocationButton() => !isLocationConfirmed
      ? SizedBox(
          width: 140,
          child: ElevatedButton(
            style: ButtonStyle(
              padding: MaterialStateProperty.all(
                const EdgeInsets.symmetric(vertical: 14, horizontal: 20),
              ),
              backgroundColor:
                  MaterialStateProperty.all(Theme.of(context).primaryColor),
              shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(15.0),
                ),
              ),
            ),
            onPressed: () {
              //changeTerminalControlDisabled = false;
              setState(() {
                isLocationFreezed = false;
              });
              locationCubit.getLocationData();
            },
            child: Text(
              AppLocalizations.of(context)!.change,
              style: const TextStyle(color: Colors.white, fontSize: 16),
            ),
          ),
        )
      : Container();

  _getPartUsedBox() {
    List<PartItemResponse?>? partsUsedList = [];
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 0, horizontal: 15),
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: Theme.of(context).primaryColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          BlocBuilder<SelectedPartItemBloc, SelectedPartItemState>(
            builder: (context, state) {
              int partCount = BlocProvider.of<SelectedPartItemBloc>(context)
                  .selectedPartList
                  .fold(0, (sum, part) => (sum) + part!.count);
              BlocProvider.of<SelectedPartItemBloc>(context)
                  .selectedPartList
                  .forEach((element) {
                if (element!.bomPartId == null || element.bomPartId == "") {
                  //  element.bomPartId =  startRepairBtnCubit.selectedRequestDetail!.bomId;
                }
              });
              partsUsedList = BlocProvider.of<SelectedPartItemBloc>(context)
                  .selectedPartList;
              return Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    partCount > 0
                        ? '${AppLocalizations.of(context)!.partsUsed} ($partCount)'
                        : AppLocalizations.of(context)!.partsUsed,
                    style: TextStyle(
                        color: Theme.of(context).primaryColor,
                        fontSize: 20,
                        fontWeight: FontWeight.bold),
                  ),
                  IconButton(
                      onPressed: () {
                        String? bomId;
                        if (startRepairBtnCubit.selectedRequestDetail != null) {
                          bomId =
                              startRepairBtnCubit.selectedRequestDetail!.bomId;
                        } else {
                          bomId = widget.repairServiceRequest!
                              .repairServiceRequest!.bomId;
                        }
                        print('bomId');
                        print(bomId);
                        Navigator.pushNamed(
                          context,
                          SearchItemPartPage.routeName,
                          arguments: SearchItemParam(
                              bomId: bomId, partsUsedList: partsUsedList ?? []),
                        );
                      },
                      icon: FaIcon(
                        FontAwesomeIcons.plus,
                        color: Theme.of(context).primaryColor,
                        size: 28,
                      ))
                ],
              );
            },
          ),
          const SizedBox(
            height: 10,
          ),
          const DottedDivider(
            color: AppColor.redColor,
          ),
          const SizedBox(
            height: 10,
          ),
          BlocBuilder<SelectedPartItemBloc, SelectedPartItemState>(
            builder: (context, state) {
              print(state);
              if (state is UpdatedPartItem) {
                return ListView.separated(
                  shrinkWrap: true,
                  physics: const ScrollPhysics(),
                  itemCount: state.partItemsList.length,
                  itemBuilder: (context, index) {
                    PartItemResponse part = state.partItemsList[index]!;
                    print(
                        "++++++++++++++++++++++++++++++++++++part++++++++++++++++++++++++++++++++++");
                    print(part);
                    //selectedPartBloc.add(UpdatePartItem(part: part, isAdd: true));
                    return Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          flex: 2,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Flexible(
                                    child: Text(
                                      part.name,
                                      //overflow: TextOverflow.ellipsis,
                                      style: const TextStyle(fontSize: 18),
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(
                                height: 3,
                              ),
                              Text(
                                part.manufacturerPartNo,
                                style: const TextStyle(fontSize: 16),
                              ),
                            ],
                          ),
                        ),
                        Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Row(
                              children: [
                                Container(
                                  //height: 35,
                                  decoration: BoxDecoration(
                                    border: Border.all(
                                        color: AppColor.greyBorderColor),
                                    borderRadius: BorderRadius.circular(5),
                                  ),
                                  child: BlocBuilder<RepairButtonCubit,
                                      RepairButtonState>(
                                    builder: (context, state) {
                                      return Column(
                                        children: [
                                          Row(
                                            children: [
                                              Container(
                                                width: 30,
                                                //height: 35,
                                                decoration: const BoxDecoration(
                                                  border: Border(
                                                    right: BorderSide(
                                                        color: AppColor
                                                            .greyBorderColor),
                                                  ),
                                                ),
                                                child: IconButton(
                                                  onPressed: state
                                                          is HidePartUsedButtonButton
                                                      ? null
                                                      : () {
                                                          if (part.count > 0) {
                                                            part.count -= 1;
                                                            setState(() {});
                                                          }
                                                        },
                                                  icon: FaIcon(
                                                    FontAwesomeIcons.minus,
                                                    color: state
                                                            is HidePartUsedButtonButton
                                                        ? AppColor
                                                            .greyBorderColor
                                                        : Theme.of(context)
                                                            .primaryColor,
                                                    size: 20,
                                                  ),
                                                ),
                                              ),
                                              Container(
                                                  padding: const EdgeInsets
                                                      .symmetric(
                                                      horizontal: 10),
                                                  child: Text(
                                                    '${part.count}',
                                                    style: const TextStyle(
                                                        fontSize: 16,
                                                        height: 1.2),
                                                  )),
                                              Container(
                                                width: 30,
                                                //height: 30,
                                                decoration: const BoxDecoration(
                                                  border: Border(
                                                    left: BorderSide(
                                                        color: AppColor
                                                            .greyBorderColor),
                                                  ),
                                                ),
                                                child: IconButton(
                                                  onPressed: state
                                                          is HidePartUsedButtonButton
                                                      ? null
                                                      : () {
                                                          part.count += 1;
                                                          setState(() {});
                                                        },
                                                  icon: FaIcon(
                                                    FontAwesomeIcons.plus,
                                                    color: state
                                                            is HidePartUsedButtonButton
                                                        ? AppColor
                                                            .greyBorderColor
                                                        : Theme.of(context)
                                                            .primaryColor,
                                                    size: 20,
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ],
                                      );
                                    },
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 3),
                            Text(
                              part.uom != null ? part.uom! : 'null',
                              style: const TextStyle(fontSize: 16),
                            ),
                          ],
                        )
                      ],
                    );
                  },
                  separatorBuilder: (BuildContext context, int index) {
                    return const Divider();
                  },
                );
              }
              return Container();
            },
          ),
          const SizedBox(
            height: 20,
          ),
          BlocBuilder<RepairButtonCubit, RepairButtonState>(
            builder: (context, state) {
              print(state);
              if (state is HidePartUsedButtonButton) {
                return Container();
              }
              return SizedBox(
                width: double.infinity,
                child: AppButton.getAcceptGreenButton(
                    onTap: () {
                      BlocProvider.of<RepairButtonCubit>(context)
                          .hidePartUsedConfirmButton();
                    },
                    label: AppLocalizations.of(context)!.done,
                    color: AppColor.greenSentColor),
              );
            },
          ),
          const SizedBox(
            height: 10,
          ),
        ],
      ),
    );
  }

  _getRepairDetailAppBar() => Container(
        margin: const EdgeInsets.only(left: 10),
        child: Row(
          children: [
            Expanded(
              child: Text(
                AppLocalizations.of(context)!.repairDetails,
                style: const TextStyle(
                    color: AppColor.blackTextColor,
                    fontSize: AppConstant.toolbarTitleFontSize,
                    fontWeight: FontWeight.bold),
              ),
            ),
            PopupMenuButton<String>(
              offset: const Offset(0, 50),
              shape: const TooltipShape(),
              icon: FaIcon(
                FontAwesomeIcons.ellipsisV,
                color: Theme.of(context).primaryColor,
              ),
              onSelected: (value) async {
                if (value == 'create_request') {
                  _createNewServiceRequest();
                } else if (value == 'replace_barcode') {
                  getIt<BarcodeResponse>().barcodeResponseInstance =
                      BarcodeResponse(
                    equipment: Equipment(
                        tag: widget
                            .repairServiceRequest!.repairServiceRequest!.tag,
                        equipmentId: widget.repairServiceRequest!
                            .repairServiceRequest!.equipmentId,
                        locationId: widget.repairServiceRequest!
                            .repairServiceRequest!.currentLocationId,
                        name: widget.repairServiceRequest!.repairServiceRequest!
                            .equipmentName,
                        location: widget.repairServiceRequest!
                            .repairServiceRequest!.location,
                        categoryName: widget.repairServiceRequest!
                            .repairServiceRequest!.equipmentCategoryName),
                  );
                  getIt<ServiceType>().type =
                      ServiceRequestType.REPLACE_BARCODE;
                  var result = await Navigator.pushNamed(
                    context,
                    AIBarCodeScannerPage.routeName,
                  );
                  if (result is Map) {
                    print('--------------' + result["code"]);
                    Map<String, dynamic> resultData =
                        result as Map<String, dynamic>;
                    String returnedCode = resultData['code'];
                    bool isScanned = resultData['isScanned'];
                    if (isScanned) {
                      locationProvider = Provider.of<LocationProvider>(context);
                      if (locationProvider?.currentPosition != null) {
                        apiBloc.add(CallBarCodeApi(
                            barcodeNumber: returnedCode,
                            isTimedService: false,
                            lattitude:
                                locationProvider?.currentPosition!.latitude!,
                            longitude:
                                locationProvider?.currentPosition!.longitude));
                      }
                    } else {
                      apiBloc.add(CallBarCodeApi(
                          barcodeNumber: returnedCode, isTimedService: false));
                    }
                  }
                }
              },
              itemBuilder: (BuildContext context) {
                var options = {
                  {
                    'value': 'create_request',
                    'name':
                        AppLocalizations.of(context)!.createNewServiceRequest,
                    'icon': FontAwesomeIcons.lightPlusCircle
                  },
                  // {'value': 'replace_barcode', 'name': AppLocalizations.of(context)!.replaceBarcode, 'icon': FontAwesomeIcons.lightBarcode}
                };
                return options.map((Map<String, dynamic> map) {
                  return PopupMenuItem<String>(
                    value: map['value'],
                    child: Row(
                      children: [
                        FaIcon(
                          map['icon'],
                          color: Theme.of(context).primaryColor,
                        ),
                        const SizedBox(
                          width: 10,
                        ),
                        Expanded(
                          child: Text(
                            map['name'],
                            overflow: TextOverflow.ellipsis,
                            /*style: const TextStyle(
                                overflow: TextOverflow.ellipsis),*/
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList();
              },
            ),
          ],
        ),
      );

  _getImagesBox() => Container(
        color: Colors.white,
        child: Column(
          children: [
            const SizedBox(
              height: 10,
            ),
            Container(
              padding: const EdgeInsets.all(10),
              width: double.infinity,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                border: Border.all(color: Theme.of(context).primaryColor),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    imageList.isNotEmpty
                        ? '${AppLocalizations.of(context)!.photosOfRepair} (${imageList.length})'
                        : AppLocalizations.of(context)!.photosOfRepair,
                    style: TextStyle(
                        color: Theme.of(context).primaryColor,
                        fontSize: 20,
                        fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  const DottedDivider(
                    color: AppColor.redColor,
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      RichText(
                        textAlign: TextAlign.start,
                        text: TextSpan(
                            style: const TextStyle(fontSize: 18),
                            children: <TextSpan>[
                              TextSpan(
                                text: AppLocalizations.of(context)!.addPhotos,
                                style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color: Theme.of(context).primaryColor),
                              ),
                            ]),
                      ),
                      TextButton(
                        style: ButtonStyle(
                          shape: MaterialStateProperty.all(
                            RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(15.0),
                              side: BorderSide(
                                  color: Theme.of(context).primaryColor),
                            ),
                          ),
                          padding: MaterialStateProperty.all(
                            const EdgeInsets.symmetric(horizontal: 10),
                          ),
                        ),
                        onPressed: () {
                          _onImageButtonPressed(ImageSource.gallery,
                              context: context);
                        },
                        child: Text(
                          AppLocalizations.of(context)!.browse,
                          style: const TextStyle(
                            fontSize: 18,
                          ),
                        ),
                      )
                    ],
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  _getImages(),
                  const SizedBox(
                    height: 20,
                  ),
                  _getCompleteButton()
                ],
              ),
            ),
          ],
        ),
      );

  _getCompleteButton() => BlocConsumer<RepairBloc, RepairState>(
        listener: (context, state) {
          print(state.toString());
          if (state is RepairError) {
            ApplicationUtil.showSnackBar(
                context: context, message: state.error);
          }
          if (state is RepairSaved) {
            //ApplicationUtil.showToast(msg: 'Service Request Created');
            _clearBlocList();
            sendDataToServer();
            // Navigator.popAndPushNamed(context, DashboardPage.routeName);
            /*Navigator.pushNamedAndRemoveUntil(context, DashboardPage.routeName,
                (Route<dynamic> route) => false);*/
            Navigator.pop(context, 'SAVED');
          }
        },
        builder: (context, state) {
          if (state is RepairInitial) {
            return _buildCompleteButton();
          } else if (state is RepairRequestSaving) {
            return _buildProgressButton();
          } else {
            return _buildCompleteButton();
          }
        },
      );

  void _initEquipmentLocation(RepairServiceRequest? selectedRequestDetail) {
    //terminalList = [];
    //gatesList = [];

    if (selectedRequestDetail != null) {
      selectedLocationId = selectedRequestDetail.currentLocationId!;
      //location for label dropdown
      selectedLocation = selectedRequestDetail.location!;
      //set end location and categoryName
      selectedLocation.reversed.forEach((element) {
        element.forEach((root, value) {
          if (element['LOCATION_ID'] == selectedLocationId) {
            endLocationName = element['NAME'];
            endLocationCategory = element['CATEGORY'];
          }
        });
      });
    }
  }

  void _onImageButtonPressed(ImageSource source,
      {required BuildContext context, bool isMultiImage = false}) async {
    try {
      final pickedFile = await _picker.pickImage(
        source: source,
        imageQuality: 70,
        maxHeight: 480,
        maxWidth: 640,
      );
      if (kIsWeb) {
        final http.Response responseData =
            await http.get(Uri.parse(pickedFile!.path));
        var uint8list = responseData.bodyBytes;
        base64Img = base64Encode(uint8list);
      } else {
        var uint8list = File(pickedFile!.path).readAsBytesSync();
        base64Img = base64Encode(uint8list);
      }
      //NAVIGATE TO EDIT
      pickedImage = await Navigator.pushNamed(
          context, EditImageHomePage.routeName,
          arguments: base64Img) as String;
      if (pickedImage != null) {
        imageList.add(pickedImage);
        pickedImage = "";
        isValueNotExist = false;
        setState(() {});
      }
    } catch (e) {
      Logger.e(e.toString());
      setState(() {
        isValueNotExist = false;
      });
    }
  }

  _getSingleImage(base64imageString, int index) {
    return SizedBox(
      height: 90,
      child: Stack(
        alignment: Alignment.center,
        children: [
          InkWell(
            onTap: () {
              print(index);
              Navigator.pushNamed(context, ImageViewPage.routeName,
                  arguments:
                      ImageWithTag(base64: base64imageString, index: index));
            },
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 5),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(10),
              ),
              child: ClipRRect(
                borderRadius: const BorderRadius.all(Radius.circular(10)),
                child: Hero(
                  tag: index.toString(),
                  child: Image.memory(
                    base64Decode(base64imageString),
                    fit: BoxFit.cover,
                    height: 80,
                    width: 80,
                  ),
                ),
              ),
            ),
          ),
          Positioned(
            top: 0,
            right: 0,
            child: InkWell(
              onTap: () {
                imageList.removeAt(index);
                setState(() {});
              },
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(15),
                  color: Colors.white,
                ),
                child: const FaIcon(
                  FontAwesomeIcons.solidTimesCircle,
                  color: Colors.red,
                  size: 20,
                ),
              ),
            ),
          )
        ],
      ),
    );
  }

  _generateExtensionUI() {
    Widget widget = ListView.builder(
      padding: const EdgeInsets.all(0),
      shrinkWrap: true,
      physics: const ScrollPhysics(),
      itemCount: extensionList.length,
      itemBuilder: (context, index) {
        Extension extension = extensionList[index];
        return _buildWidgetFromExtension(
            extension.title!, extension.extensionContent!);
      },
    );
    //isValueNotExist = false;
    return widget;
  }

  Widget _buildWidgetFromExtension(
      String title, List<ExtensionContent> extensionContent) {
    return Container(
      padding: const EdgeInsets.all(10),
      margin: const EdgeInsets.symmetric(vertical: 2),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Theme.of(context).primaryColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _getSectionHeader(title),
          _getSectionBody(extensionContent, title),
        ],
      ),
    );
  }

  _getSectionHeader(String title) => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
                color: Theme.of(context).primaryColor,
                fontSize: 18,
                fontWeight: FontWeight.bold),
          ),
          const SizedBox(
            height: 10,
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 5),
            child: const DottedDivider(
              color: AppColor.redColor,
            ),
          ),
          const SizedBox(
            height: 10,
          ),
        ],
      );

  _getSectionBody(List<ExtensionContent> extensionContentList, String title) {
    if (extensionMap[title] == null) {
      extensionMap[title] = [];
    }
    for (var element in extensionContentList) {}
    return ListView.builder(
      shrinkWrap: true,
      physics: const ScrollPhysics(),
      itemCount: extensionContentList.length,
      itemBuilder: (context, index) {
        ExtensionContent extensionContent = extensionContentList[index];
        if (isValueNotExist) {
          extensionMap[title].add({
            extensionContent.fieldTechName: '',
            'FIELD_MANDATORY': extensionContent.fieldMandatory,
            'FIELD_NAME': extensionContent.fieldName,
            'FIELD_TECH_NAME': extensionContent.fieldTechName,
          });
        }
        if (extensionContent.fieldControl == "TEXT") {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 5,
              ),
              _getLabelBaseOnMandatory(extensionContent),
              Container(
                margin: const EdgeInsets.only(
                  top: 5,
                ),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(color: AppColor.greyBorderColor),
                ),
                child: TextField(
                  style: const TextStyle(height: 1.2),
                  decoration: const InputDecoration(
                    contentPadding: EdgeInsets.symmetric(horizontal: 5),
                    border: InputBorder.none,
                    //hintText: extensionContent.fieldName,
                  ),
                  onChanged: (value) {
                    extensionMap[title]
                        [index] = {extensionContent.fieldTechName: value};
                    print(extensionMap.toString());
                  },
                ),
              ),
              const SizedBox(
                height: 10,
              ),
            ],
          );
        } else if (extensionContent.fieldControl == "TEXTAREA") {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 5,
              ),
              _getLabelBaseOnMandatory(extensionContent),
              Container(
                margin: const EdgeInsets.symmetric(
                  vertical: 5,
                ),
                padding: const EdgeInsets.symmetric(vertical: 5),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(color: AppColor.greyBorderColor),
                ),
                child: TextField(
                  minLines: 3,
                  maxLines: 5,
                  style: const TextStyle(height: 1.2),
                  decoration: const InputDecoration(
                    contentPadding: EdgeInsets.symmetric(horizontal: 5),
                    border: InputBorder.none,
                    //  hintText: extensionContent.fieldName,
                  ),
                  onChanged: (value) {
                    extensionMap[title]
                        [index] = {extensionContent.fieldTechName: value};
                    print(extensionMap.toString());
                  },
                ),
              ),
              const SizedBox(
                height: 10,
              ),
            ],
          );
        } else if (extensionContent.fieldControl == "NUMBER") {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 5,
              ),
              _getLabelBaseOnMandatory(extensionContent),
              Container(
                margin: const EdgeInsets.symmetric(
                  vertical: 5,
                ),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(color: AppColor.greyBorderColor),
                ),
                child: TextField(
                  style: const TextStyle(height: 1.2),
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    contentPadding: const EdgeInsets.symmetric(horizontal: 5),
                    border: InputBorder.none,
                    hintText: extensionContent.fieldName,
                  ),
                  onChanged: (value) {
                    extensionMap[title]
                        [index] = {extensionContent.fieldTechName: value};
                    print(extensionMap.toString());
                  },
                ),
              ),
            ],
          );
        } else if (extensionContent.fieldControl == "TOGGLE") {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 5,
              ),
              _getLabelBaseOnMandatory(extensionContent),
              Container(
                  margin: const EdgeInsets.symmetric(
                    vertical: 5,
                  ),
                  padding: const EdgeInsets.symmetric(
                    vertical: 10,
                  ),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(color: AppColor.greyBorderColor),
                  ),
                  child: Row(
                    children: [
                      Switch(
                        value: _getSwitchValue(
                            extensionMap, title, index, extensionContent),
                        onChanged: (bool value) {
                          extensionMap[title][index] = {
                            extensionContent.fieldTechName: value.toString()
                          };
                          isValueNotExist = false;
                          setState(() {});
                        },
                      ),
                    ],
                  )),
            ],
          );
        } else if (extensionContent.fieldControl == "CHOICE") {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 5,
              ),
              _getLabelBaseOnMandatory(extensionContent),
              dropDownOption(extensionContent, index, title)
            ],
          );
        } else {
          return Container();
        }
      },
    );
  }

  _getLabelBaseOnMandatory(ExtensionContent extensionContent) {
    return RichText(
      textAlign: TextAlign.start,
      text: TextSpan(style: const TextStyle(fontSize: 18), children: <TextSpan>[
        extensionContent.fieldMandatory == "Y"
            ? const TextSpan(
                text: "* ",
                style: TextStyle(
                    color: AppColor.redColor, fontWeight: FontWeight.bold),
              )
            : const TextSpan(text: ''),
        TextSpan(
          text: extensionContent.fieldName,
          style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColor.blackTextColor),
        ),
      ]),
    );
  }

  _getSwitchValue(Map<String, dynamic> extensionMap, String title, int index,
      ExtensionContent extensionContent) {
    if (extensionMap[title][index][extensionContent.fieldTechName] == null ||
        extensionMap[title][index][extensionContent.fieldTechName].isEmpty) {
      extensionMap[title][index][extensionContent.fieldTechName] = 'false';
    }
    return extensionMap[title][index][extensionContent.fieldTechName] == 'true';
  }

  dropDownOption(
    ExtensionContent extensionContent,
    int index,
    String title,
  ) {
    List<DropDownChoice> dropDownList = [];
    Map<String, dynamic>? data = getIt<Customization>().customizationData;
    if (data != null) {
      if (data.containsKey("CHOICE")) {
        if (data['CHOICE'].containsKey(extensionContent.fieldChoiceType)) {
          List<dynamic> dropDownOptionList =
              data['CHOICE'][extensionContent.fieldChoiceType];
          dropDownList
              .add(DropDownChoice(choiceName: 'Select', choiceValue: 'select'));
          for (var choiceMap in dropDownOptionList) {
            dropDownList.add(DropDownChoice.fromMap(choiceMap));
          }
        }
      }
    }

    return Container(
      margin: const EdgeInsets.only(top: 5),
      height: 45,
      decoration: BoxDecoration(
          color: Theme.of(context).primaryColor,
          borderRadius: BorderRadius.circular(15)),
      child: DropdownButton(
          underline: Container(),
          value: _getExtensionDropdownSelectedValue(
              dropDownList, title, index, extensionContent),
          hint: Center(
            child: Text(
              _getExtensionDropdownSelectedName(
                  dropDownList, title, index, extensionContent),
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
          icon: const Icon(
            // Add this
            Icons.arrow_drop_down,
            color: Colors.white,
            size: 40, // Add this
          ),
          isExpanded: true,
          items: dropDownList.map(
            (val) {
              return DropdownMenuItem(
                value: val.choiceValue,
                child: Text(
                  val.choiceName!,
                  style: const TextStyle(color: Colors.black),
                ),
              );
            },
          ).toList(),
          selectedItemBuilder: (BuildContext ctxt) {
            return dropDownList.map<Widget>((DropDownChoice item) {
              return DropdownMenuItem(
                  child: Container(
                    margin: const EdgeInsets.only(left: 20),
                    child: Text("${item.choiceName}",
                        style: const TextStyle(
                            fontWeight: FontWeight.bold, color: Colors.white)),
                  ),
                  value: item);
            }).toList();
          },
          onChanged: (value) {
            extensionMap[title]
                [index] = {extensionContent.fieldTechName: value};
            print(extensionMap.toString());
            isValueNotExist = false;
            setState(() {});
          }),
    );
  }

  _getExtensionDropdownSelectedValue(List<DropDownChoice> dropDownList,
      String title, int index, ExtensionContent extensionContent) {
    String selected =
        extensionMap[title][index][extensionContent.fieldTechName];
    if (selected.isNotEmpty) return selected;
    return dropDownList[0].choiceValue;
  }

  String _getExtensionDropdownSelectedName(List<DropDownChoice> dropDownList,
      String title, int index, ExtensionContent extensionContent) {
    var selected = extensionMap[title][index][extensionContent.fieldTechName];
    print(selected);

    for (DropDownChoice choice in dropDownList) {
      if (choice.choiceValue == selected) {
        print('element.choiceName' + choice.choiceName!);
        return choice.choiceName!;
      }
    }
    return dropDownList[0].choiceName!;
  }

  void _initExtentionData() {
    extensionList = getExtensionData();
  }

  getExtensionData() {
    List<Extension> extensionList = [];
    Map<String, dynamic>? data = getIt<Customization>().customizationData;
    if (data != null) {
      if (data.containsKey("EXTENSIONS")) {
        if (data['EXTENSIONS'].containsKey('REPAIR')) {
          Map<String, dynamic> sectionMap = data['EXTENSIONS']['REPAIR'];
          for (var key in sectionMap.keys) {
            Extension extension = Extension();
            extension.title = key;
            List<dynamic> sectionDetailList = sectionMap[key];
            List<ExtensionContent> extensionContentList = [];
            for (var mapData in sectionDetailList) {
              extensionContentList.add(ExtensionContent.fromMap(mapData));
            }
            extension.extensionContent = extensionContentList;
            extensionList.add(extension);
          }
        }
      }
      return extensionList;
    }
  }

  dynamic validateExtensionMap(Map<String?, dynamic> extensionMap) {
    List<String> errorList = [];
    List<Map<String?, dynamic>> returnedMap = [];
    extensionMap.forEach((key, value) {
      List<dynamic> innerMap = extensionMap[key];
      for (var element in innerMap) {
        Map<String?, dynamic> map = element;
        if (map['FIELD_MANDATORY'] == 'Y' &&
            map[map['FIELD_TECH_NAME']].isEmpty) {
          errorList.add(map['FIELD_NAME']);
        } else {
          returnedMap.add(element);
        }
      }
    });
    if (errorList.isNotEmpty) {
      ApplicationUtil.showSnackBar(
          context: context, message: '${errorList[0]} is required');
      return true;
    }
    return returnedMap;
  }

  _buildProgressButton() {
    return Center(
      child: Container(
        width: 30,
        height: 30,
        margin: const EdgeInsets.only(left: 40),
        child: const CircularProgressIndicator(),
      ),
    );
  }

  _buildCompleteButton() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 5),
      child: ElevatedButton(
        style: ButtonStyle(
          backgroundColor: MaterialStateProperty.all(Colors.green),
          shape: MaterialStateProperty.all(
            const RoundedRectangleBorder(
              borderRadius: BorderRadius.all(
                Radius.circular(14),
              ),
            ),
          ),
        ),
        onPressed: () async {
          bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
          LocationPermission permission = await Geolocator.checkPermission();
          locationData ??= await ApplicationUtil.getLocation();
          if (!serviceEnabled ||
              permission == LocationPermission.denied ||
              permission == LocationPermission.deniedForever) {
            ApplicationUtil.showWarningAlertDialog(
              context,
              title: AppLocalizations.of(context)!.locationPermissionRequired,
              desc: kIsWeb
                  ? AppLocalizations.of(context)!
                      .appRequiresLocationPermissionforweb
                  : AppLocalizations.of(context)!.appRequiresLocationPermission,
              negativeLabel: AppLocalizations.of(context)!.okay,
            );
          } else {
            LocationAccuracyStatus status =
                await Geolocator.getLocationAccuracy();
            if (status == LocationAccuracyStatus.reduced) {
              setState(() {
                fetchingLocation = false;
              });
              ApplicationUtil.showWarningAlertDialog(
                context,
                title: AppLocalizations.of(context)!.locationPermissionRequired,
                desc:
                    "To provide accurate results based on your current position, we need access to your precise location. Please enable Precise Location in your device settings",
                negativeLabel: AppLocalizations.of(context)!.okay,
              );
              return;
            }
            if (locationData == null) {
              if (status == LocationAccuracyStatus.precise) {
                setState(() {
                  fetchingLocation = true;
                });
                locationData = await ApplicationUtil.getLocation();
              }
              /*   ApplicationUtil.showWarningAlertDialog(
                context,
                title: "Please Wait fetching location Data",
                negativeLabel: AppLocalizations.of(context)!.okay,
              );*/
            } else {
              dynamic response = validateExtensionMap(extensionMap);
              if (response is bool) {
                return;
              } else {
                //IMAGE LIST
                List<Map<String, dynamic>> base64MapList = [];

                for (var element in imageList) {
                  var imageMap = {
                    "DOCUMENT_TYPE": "image/jpeg",
                    "DOCUMENT_BLOB": element
                  };
                  base64MapList.add(imageMap);
                }
                //PART LIST
                List<PartItemResponse?> partList =
                    BlocProvider.of<SelectedPartItemBloc>(context)
                        .selectedPartList
                        .where((element) => element!.count > 0)
                        .toList();
                partList.forEach((element) {
                  if (element!.bomPartId != null && element.bomPartId != "") {
                    element.partId = element.bomPartId!;
                  }
                });
                var jsonParts = partList
                    .map((part) => PartItemResponse(
                            partId: part!.partId,
                            //partId: part!.partId,
                            customerId: part.customerId,
                            name: part.name,
                            description: part.description,
                            manufacturer: part.manufacturer,
                            manufacturerPartNo: part.manufacturerPartNo,
                            partType: part.partType,
                            count: part.count)
                        .toMap())
                    .toList();
                if (base64MapList.isEmpty) {
                  ApplicationUtil.showMissingPhotosWarningDialog(
                    context,
                    message: AppLocalizations.of(context)!
                        .areYouSureYouWantToCompleteRepair,
                    onSubmit: () {
                      _saveInDatabase(jsonParts, base64MapList);
                    },
                    onCancel: () {},
                  );
                } else {
                  _saveInDatabase(jsonParts, base64MapList);
                }
              }
            }
          }
        },
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 14),
          child: fetchingLocation
              ? const Center(
                  child: CircularProgressIndicator(),
                )
              : Text(
                  AppLocalizations.of(context)!.complete,
                  style: const TextStyle(color: Colors.white, fontSize: 18),
                ),
        ),
      ),
    );
  }

  void sendDataToServer() {
    repairBloc.add(SendPendingRepairToServer());
    //
    print('sendDataToServer');
  }

  void _clearBlocList() {
    BlocProvider.of<SelectedPartItemBloc>(context)
        .add(UpdatePartItem(isReset: true));
  }

  void _createNewServiceRequest() {
    getIt<ServiceType>().type = ServiceRequestType.ADD;
    getIt<BarcodeResponse>().barcodeResponseInstance = BarcodeResponse(
      equipment: Equipment(
          equipmentId:
              widget.repairServiceRequest!.repairServiceRequest!.equipmentId,
          locationId: widget
              .repairServiceRequest!.repairServiceRequest!.currentLocationId,
          name:
              widget.repairServiceRequest!.repairServiceRequest!.equipmentName,
          location: widget.repairServiceRequest!.repairServiceRequest!.location,
          categoryName: widget.repairServiceRequest!.repairServiceRequest!
              .equipmentCategoryName),
    );
    Navigator.pushNamed(context, AddServiceRequestPage.routeName,
        arguments: ServiceType(type: ServiceRequestType.ADD));
  }

  void _saveInDatabase(List<Map<String, dynamic>> jsonParts,
      List<Map<String, dynamic>> base64mapList) {
    /*print(startRepairBtnCubit.selectedRequestDetail!.location!['LOCATION_ID'] ==
        selectedLocationId);*/
    int? userId = getIt<SharedPreferences>().getInt('userId');
    repairBloc.add(
      SaveRepairRequest(
        repairCompanion: RepairCompanion(
            createdAt: moor.Value(DateTime.now()),
            createdBy: moor.Value(userId),
            currentLocationId: moor.Value(
                startRepairBtnCubit.selectedRequestDetail!.equipmentLocationId),
            newLocationId: moor.Value(startRepairBtnCubit
                        .selectedRequestDetail!.equipmentLocationId ==
                    selectedLocationId
                ? null
                : selectedLocationId),
            parts: moor.Value(json.encode(jsonParts)),
            extn: moor.Value(json.encode(extensionMap)),
            repairDocument: moor.Value(json.encode(base64mapList)),
            customerId: moor.Value(
                startRepairBtnCubit.selectedRequestDetail!.customerId),
            equipmentId: moor.Value(startRepairBtnCubit
                .selectedRequestDetail!.equipmentId
                .toString()),
            requestId: moor.Value(
                startRepairBtnCubit.selectedRequestDetail!.requestId!),
            latitude: moor.Value(locationProvider?.currentPosition?.latitude),
            longitude:
                moor.Value(locationProvider?.currentPosition?.longitude)),
      ),
    );
  }

  void _getDetailListOfServiceRequestForEquipment() {
    if (startRepairBtnCubit.selectedRequestDetail != null) {
      repairDetailApiCubit.getRepairDetailListForEquipment(
          startRepairBtnCubit.selectedRequestDetail!.equipmentId,
          false,
          widget.repairServiceRequest!.isTimedService!);
    } else {
      repairDetailApiCubit.getRepairDetailListForEquipment(
          widget.repairServiceRequest!.repairServiceRequest!.equipmentId,
          true,
          widget.repairServiceRequest!.isTimedService!);
    }

    BlocProvider.of<RepairButtonCubit>(context).resetRepairButton();
  }

  void _resetVariable() {
    pickedImage = null;
    imageList = [];
    showAlertDialogWhileExist = false;
    isLocationConfirmed = false;
    isLocationFreezed = true;
    changeTerminalControlDisabled = true;
    base64Img;
    // EXTENSION LIST FROM SERVER
    extensionList = [];
    // Extension submit response map
    extensionMap = {};
    isValueNotExist = true;
  }

  void _initNewTerminalAndGatesForSelectedService(
      ServiceRequestDetail? selectedRequestDetail) {
    /*  if (selectedRequestDetail!.location != null) {
      selectedTerminal = LocationChoice(
          name: selectedRequestDetail.location!['NAME'],
          locationId: selectedRequestDetail.location!['LOCATION_ID']);
      selectedLocationId = selectedLocation['LOCATION_ID']
      if (!terminalList.contains(selectedTerminal)) {
        terminalList.add(selectedTerminal);
      }
      selectedGate = LocationChoice(
          name: selectedRequestDetail.location!['NAME'],
          locationId: selectedRequestDetail.location!['LOCATION_ID']);
      if (!gatesList.contains(selectedGate)) {
        gatesList.add(selectedGate);
      }
    }*/
  }

  void _initLocation() async {
    await ApplicationUtil.getLocation().then((value) {
      locationData = value;
    });
  }
}
