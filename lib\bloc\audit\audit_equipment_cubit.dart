import 'package:alink/data/model/audit_response.dart';
import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';

part 'audit_equipment_state.dart';

class AuditEquipmentCubit extends Cubit<AuditEquipmentState> {
  List<AuditEquipment> auditEquipment = [];
  List<String> unscannedBarcodeList = [];
  static int? auditId;
  static String? auditServiceType;
  static Map<String, dynamic>? auditLocation;
  static List<Map<String, dynamic>>? location;
  AuditEquipmentCubit() : super(AuditEquipmentInitial());

  void addEquipmentList(List<AuditEquipment> auditEquipment,
      Map<String, dynamic>? location, int? id, String? serviceType) {
    this.auditEquipment = auditEquipment;
    auditLocation = location;
    auditId = id;
    auditServiceType = serviceType;
  }

  void updateEquipment(String barcodeNumber) {
    if (auditEquipment.indexWhere((element) => element.tag == barcodeNumber) >=
        0) {
      auditEquipment[auditEquipment
              .indexWhere((element) => element.tag == barcodeNumber)]
          .isScanned = true;
      emit(EquipmentListData(equipmentList: auditEquipment));
    } else {
      auditEquipment.add(AuditEquipment(tag: barcodeNumber));
      emit(EquipmentListData(equipmentList: auditEquipment));
    }
  }
}
