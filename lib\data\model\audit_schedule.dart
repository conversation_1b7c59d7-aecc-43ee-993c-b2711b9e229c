class AuditSchedules {
  String? description;
  String? serviceType;

  AuditSchedules({
    this.description,
    this.serviceType,
  });

  AuditSchedules.fromJson(Map<String, dynamic> json) {
    description = json['DESCRIPTION'];
    serviceType = json['SERVICE_TYPE'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['DESCRIPTION'] = description;
    data['SERVICE_TYPE'] = serviceType;
    return data;
  }
}
