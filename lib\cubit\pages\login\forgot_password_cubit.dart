import 'package:alink/data/repository/api_repository.dart';
import 'package:bloc/bloc.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

part 'forgot_password_state.dart';

class ForgotPasswordCubit extends Cubit<ForgotPasswordState> {
  final ApiRepository apiRepository;
  ForgotPasswordCubit({required this.apiRepository})
      : super(ForgotPasswordInitial());

  forgotPassword(String email, BuildContext context) async {
    emit(SendingResetPasswordMail());
    try {
      var response = await apiRepository.resetPassword(email);
      if (response is int && response == 204) {
        emit(SentResetPasswordMail(
            msg: AppLocalizations.of(context)!
                .anEmailContainingInstructionSent));
      } else {
        emit(ResetPasswordError(errorMessage: response));
      }
    } catch (error) {
      emit(ResetPasswordError(errorMessage: error.toString()));
    }
  }
}
