enum FilePickerPlatformType { WE<PERSON>, <PERSON><PERSON><PERSON><PERSON>, WINDOWS }

enum LoginUIState { NAME, EMAIL, PASSWORD }

enum SyncState { UNSENT, SENDING, SENT }

enum ServiceRequestType { ADD, EDIT, REPLACE_BARCODE, MOVE_EQUIPMENT, ASSIGN_EQUIPMENT, R<PERSON><PERSON><PERSON>, NOTIFICATION, DIRECT_REPAIR, AUDIT,SCHEDULED }

enum InternetConnection { CONNECTED, DISCONNECTED }

enum BarcodeSearchResult { SEARCHING, SEARCHED, INITIAL, SEARCH_FOUND }

class ServiceType {
  String? barcodeNumber;
  ServiceRequestType type;
  bool fromAudit;
  String? defaultLocationId;
  bool? isTimedService;
  ServiceType({required this.type, this.barcodeNumber, this.fromAudit = false, this.defaultLocationId,this.isTimedService=false});
}
