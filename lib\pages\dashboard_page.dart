import 'dart:io';

import 'package:alink/bloc/api/api_bloc.dart';
import 'package:alink/bloc/service/service_request_bloc.dart';
import 'package:alink/bloc/user_bloc.dart';
import 'package:alink/cubit/internet/internet_cubit.dart';
import 'package:alink/data/model/barcode_response.dart';
import 'package:alink/data/repository/api_service.dart';
import 'package:alink/database/database.dart';
import 'package:alink/pages/airline/aircraft_selection_page.dart';
import 'package:alink/pages/airport/audit/audit_page.dart';
import 'package:alink/pages/airport/repair/assign_equipment/assign_equipment_page.dart';
import 'package:alink/pages/airport/repair/repair_list_page.dart';
import 'package:alink/pages/airport/repair/replace_bar_code/confirm_barcode_replace_page.dart';
import 'package:alink/pages/airport/service_request/add_service_request_page.dart';
import 'package:alink/pages/airport/service_request/service_result_page.dart';
import 'package:alink/pages/airport/timed_service_requests/timed_service_requests.dart';
import 'package:alink/pages/settings_page.dart';
import 'package:alink/pages/tasks/task_page.dart';
import 'package:alink/provider/locationProvider.dart';
import 'package:alink/provider/task_type_provider.dart';
import 'package:alink/scanner/barcode/ai_scanner_page.dart';
import 'package:alink/service_locator.dart';
import 'package:alink/util/app_color.dart';
import 'package:alink/util/app_constant.dart';
import 'package:alink/util/application_util.dart';
import 'package:alink/util/enums/app_enum.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:intl/intl.dart';
import 'package:location/location.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';

import '../bloc/task/task_bloc.dart';
import '../cubit/pages/repair_detail/repair_detail_api_cubit.dart';
import 'auth/login_page.dart';

class DashboardPage extends StatefulWidget {
  static const routeName = 'dashboard';

  const DashboardPage({Key? key}) : super(key: key);

  @override
  _DashboardPageState createState() => _DashboardPageState();
}

class _DashboardPageState extends State<DashboardPage> {
  static const String className = '_DashboardPageState';
  var serviceRequestNotificationHeight = 0.0;
  var adminMessageNotificationHeight = 0.0;
  var networkMessageNotificationHeight = 0.0;
  int count = 0;
  SyncState syncState = SyncState.UNSENT;
  String returnedCode = "Unknown";
  double screenWidth = 0.0;
  TaskTypeProvider? taskTypeProvider;
  LocationData? locationData;
  LocationProvider? locationProvider;

  @override
  void initState() {
    ApplicationUtil.initLocationPermission();
    userBloc.add(GetLoggedInUser());
    apiBloc.add(GetCustomizationData());
    apiBloc.add(GetServiceRequestCount());
    taskTypeProvider = Provider.of<TaskTypeProvider>(context, listen: false);
    locationProvider = Provider.of<LocationProvider>(context, listen: false);

    serviceRequestBloc.add(SendPendingServiceRequestToServer());
    _getAppUpdateVersionDialog();
    taskTypeProvider!.getTaskTypes();
    super.initState();
  }

  ServiceRequestBloc get serviceRequestBloc =>
      BlocProvider.of<ServiceRequestBloc>(context, listen: false);

  UserBloc get userBloc => BlocProvider.of<UserBloc>(context);
  RepairDetailApiCubit get repairDetailApiCubit =>
      BlocProvider.of<RepairDetailApiCubit>(context);

  ApiBloc get apiBloc => BlocProvider.of<ApiBloc>(context);
  TaskBloc get taskBloc => BlocProvider.of<TaskBloc>(context);

  @override
  Widget build(BuildContext context) {
    locationProvider = Provider.of<LocationProvider>(context);

    //Logger.i("Class Name: " + className);
    screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth > 500) {
      screenWidth = 500;
    }
    //Logger.i('${ApplicationUtil.getFormattedCurrentDateAndTime()} dashboard build function called');
    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: Scaffold(
        body: Center(
          child: Container(
            color: Colors.white,
            constraints: const BoxConstraints(maxWidth: 500),
            child: Stack(
              children: [
                Column(
                  children: [
                    ApplicationUtil.displayNotificationWidgetIfExist(
                        context, DashboardPage.routeName),
                    /* ElevatedButton(
                      onPressed: () {
                        serviceRequestBloc
                            .add(ServiceRequestSendNotification(count: 3));
                      },
                      child: Text('Test'),
                    ),*/
                    Expanded(
                      child: SingleChildScrollView(
                        physics: const BouncingScrollPhysics(
                          parent: AlwaysScrollableScrollPhysics(),
                        ),
                        child: Container(
                          margin: EdgeInsets.only(
                              top: MediaQuery.of(context).padding.top),
                          padding: const EdgeInsets.symmetric(horizontal: 15),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              _getCustomerName(),
                              const SizedBox(
                                height: 5,
                              ),
                              Row(
                                children: [
                                  _getGreetingMessage(context),
                                  _getSyncState()
                                ],
                              ),
                              const SizedBox(
                                height: 20,
                              ),
                              BlocConsumer<ApiBloc, ApiState>(
                                listener: (context, state) {
                                  if (state
                                      is FetchedServiceRequestCountError) {
                                    if (state.errorMessage ==
                                        ApiResponse.INVALID_AUTH) {
                                      Navigator.pushNamedAndRemoveUntil(context,
                                          LoginPage.routeName, (route) => false,
                                          arguments: true);
                                    }
                                  }
                                },
                                builder: (context, state) {
                                  return Row(
                                    children: [
                                      _getServiceRequestInMonthWidget(state),
                                      const SizedBox(
                                        width: 15,
                                      ),
                                      _getServiceRequestInTodayWidget(state),
                                      /*SizedBox(
                                            width: 20,
                                          ),*/
                                      //_getServiceRequestByMeWidget(),
                                    ],
                                  );
                                },
                              ),
                              const SizedBox(
                                height: 10,
                              ),
                              _getRolesCategoryWidget(),
                              //_getFlightFromAssets(),
                              //_getScanToReportButton(context),
                              const SizedBox(
                                height: 10,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                _getSettingIcon(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  _getSyncState() {
    return BlocConsumer<ServiceRequestBloc, ServiceRequestState>(
      listener: (context, state) {
        print('===========Sync STATE Listener=========');
        print(state);
        if (state is SentPendingServiceRequestToServer) {
          //count = state.count;
          serviceRequestBloc.add(ServiceRequestSendNotification(
              count: state.count!, message: getBannerMessage(state)));
          Future.delayed(const Duration(seconds: 2), () {
            _clearServiceNotification();
          });
        } else if (state is NetworkErrorInPendingServiceRequest) {
          if (state.errorMessage == ApiResponse.INVALID_AUTH) {
            Navigator.pushNamedAndRemoveUntil(
                context, LoginPage.routeName, (route) => false,
                arguments: true);
          } else if (state is NetworkErrorInPendingServiceRequest) {
            serviceRequestBloc.add(ServiceRequestSendErrorNotification(
                message: state.errorMessage));
          }
        }
      },
      builder: (context, state) {
        print('===========Sync STATE Builder=========');
        print(state);

        //If data is sending to server
        if (state is SendingPendingServiceRequestToServer) {
          return _syncingServiceRequestDataUI();
        } else if (state is NoPendingServiceRequestInDatabase) {
          return _allDataSyncedUI();
        } else if (state is NetworkErrorInPendingServiceRequest) {
          return _getPendingCountUI(state.pendingRequestCount);
        }
        //If notification is send to top then set sync state to synced
        if (state is ServiceRequestNotificationSent) {
          return _allDataSyncedUI();
        }

        return _allDataSyncedUI();
      },
    );
  }

  _getGreetingMessage(BuildContext context) => Expanded(
        child: BlocBuilder<UserBloc, UserState>(
          builder: (context, state) {
            if (state is LoginUserFetched) {
              return Text(
                ApplicationUtil.getGreetingMessageBaseOnTime(context) +
                    state.userData.FIRST_NAME! +
                    '!',
                style: TextStyle(
                    fontSize: 38,
                    color: Theme.of(context).primaryColor,
                    fontWeight: FontWeight.bold),
              );
            } else {
              return Text(
                ApplicationUtil.getGreetingMessageBaseOnTime(context),
                style: TextStyle(
                    fontSize: 38,
                    color: Theme.of(context).primaryColor,
                    fontWeight: FontWeight.bold),
              );
            }
          },
        ),
      );

  _getServiceRequestInMonthWidget(ApiState state) {
    ///tooltip added. to check the exact count if count is more than 1000 press and hold count will show exact count in a tooltip
    return Tooltip(
      message: ApiBloc.serviceCompletedCount != null
          ? '${ApiBloc.serviceCount}'
          : '_ _',
      child: Container(
        padding: const EdgeInsets.all(5),
        decoration: BoxDecoration(
            color: AppColor.greenServiceRequestColor,
            borderRadius: BorderRadius.circular(10)),
        width: screenWidth / 2 - 22.5,
        height: 120,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            Text(
              ApiBloc.serviceCount != null
                  ? NumberFormat.compact().format(ApiBloc.serviceCount)
                  : '_ _',
              style: const TextStyle(
                  fontSize: 32, color: AppColor.greenServiceRequestTextColor),
            ),
            Text(
              AppLocalizations.of(context)!.totalNumberServiceRequestCreated,
              textAlign: TextAlign.center,
              style: const TextStyle(
                  fontSize: 12, color: AppColor.greenServiceRequestTextColor),
            )
          ],
        ),
      ),
    );
  }

  _getServiceRequestInTodayWidget(ApiState state) {
    ///tooltip added. to check the exact count if count is more than 1000 press and hold count will show exact count in a tooltip
    return Tooltip(
      message: ApiBloc.serviceCompletedCount != null
          ? '${ApiBloc.serviceCompletedCount}'
          : '_ _',
      child: Container(
        padding: const EdgeInsets.all(5),
        decoration: BoxDecoration(
            color: AppColor.purpleServiceRequestColor,
            borderRadius: BorderRadius.circular(10)),
        width: screenWidth / 2 - 22.5,
        height: 120,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            Text(
              ApiBloc.serviceCompletedCount != null
                  ? NumberFormat.compact().format(ApiBloc.serviceCompletedCount)
                  : '_ _',
              style: const TextStyle(
                  fontSize: 32, color: AppColor.purpleServiceRequestTextColor),
            ),
            Text(
              AppLocalizations.of(context)!.totalNumberServiceRequestClosed,
              textAlign: TextAlign.center,
              style: const TextStyle(
                  fontSize: 12, color: AppColor.purpleServiceRequestTextColor),
            )
          ],
        ),
      ),
    );
  }

  _getBroadcastFormWidget() => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(15),
              border: Border.all(color: Colors.grey),
            ),
            height: 120,
            child: TextFormField(
              autofocus: false,
              keyboardType: TextInputType.multiline,
              style: const TextStyle(fontSize: 20),
              decoration: InputDecoration(
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.all(10),
                  hintStyle: const TextStyle(
                      color: Colors.grey,
                      fontSize: 20,
                      fontWeight: FontWeight.bold),
                  hintText: AppLocalizations.of(context)!
                      .fireEmergencyAtTerminalHint),
              maxLines: 6,
            ),
          ),
          const SizedBox(
            height: 10,
          ),
          ElevatedButton.icon(
            onPressed: () {},
            icon: const FaIcon(FontAwesomeIcons.bullhorn),
            label: Text(
              AppLocalizations.of(context)!.broadcast,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          )
        ],
      );

  _getRolesCategoryWidget() {
    return Column(
      children: [
        BlocBuilder<UserBloc, UserState>(
          builder: (context, state) {
            if (state is LoginUserFetched &&
                state.userData.MODULE!.contains("MESSAGE")) {
              return _getBroadcastFormWidget();
            } else {
              return Container();
            }
          },
        ),
        const SizedBox(
          height: 20,
        ),
        _getIdentifyConcernWidget(),
        const SizedBox(
          height: 10,
        ),
        _getRepairCategoryWidget(),
        const SizedBox(
          height: 10,
        ),
        _getTasksWidget(),
        const SizedBox(
          height: 10,
        ),
        _getAuditCategoryWidget(),
        const SizedBox(
          height: 10,
        ),
        _getTimedServicesWidget(),
      ],
    );
  }

  _getIdentifyConcernWidget() => BlocBuilder<UserBloc, UserState>(
        builder: (context, state) {
          if (state is LoginUserFetched) {
            UserData user = state.userData;
            bool hasCreateRole = user.MODULE!.contains(Module.SERVICE_REQUEST);
            //check if user has access to mobile app
            bool hasNoMobile = user.MODULE!.contains("NO_MOBILE");
            if (hasNoMobile) {
              hasCreateRole = !hasCreateRole;
            }
            return BlocListener<ApiBloc, ApiState>(
              listener: (context, state) {
                if (state is BarcodeApiCalling) {
                  SchedulerBinding.instance.addPostFrameCallback((_) {
                    ApplicationUtil.showLoaderDialog(
                        context, '${AppLocalizations.of(context)!.fetching}..');
                  });
                } else if (state is BarcodeApiCalled) {
                  apiBloc.add(ResetBarCodeApi());
                  Navigator.pop(context);
                  if (getIt<ServiceType>().type == ServiceRequestType.ADD) {
                    getIt<BarcodeResponse>().barcodeResponseInstance =
                        state.barcodeResponse;
                    if (state.barcodeResponse.serviceRequestCount == null ||
                        state.barcodeResponse.serviceRequestCount == 0) {
                      /// GOTO NEW ADD SERVICE PAGE IF THERE IS NO EXISTING SERVICE
                      Navigator.pushNamed(
                          context, AddServiceRequestPage.routeName,
                          arguments: ServiceType(type: ServiceRequestType.ADD));
                    } else {
                      /// GOTO NEW RESULT PAGE PAGE IF THERE IS  EXISTING SERVICE
                      Navigator.pushNamed(context, BardCodeResultPage.routeName,
                          arguments: ServiceType(
                            type: ServiceRequestType.ADD,
                          ));
                    }
                  } else if (getIt<ServiceType>().type ==
                      ServiceRequestType.REPLACE_BARCODE) {
                    ApplicationUtil.showSnackBar(
                        context: context,
                        message:
                            AppLocalizations.of(context)!.barcodeAlreadyAssign);
                  } else if (state is BarcodeApiCalled &&
                      getIt<ServiceType>().type ==
                          ServiceRequestType.DIRECT_REPAIR) {
                    repairDetailApiCubit.getSingleRepairDetailByEquipmentId(
                        equipmentId:
                            state.barcodeResponse.equipment!.equipmentId,
                        bothRequestType: true,
                        isTimedService: false,
                        isFromMap: state.isFromMap);
                  }
                  /* else if (getIt<ServiceType>().type ==
                      ServiceRequestType.MOVE_EQUIPMENT) {
                    getIt<BarcodeResponse>().barcodeResponseInstance =
                        state.barcodeResponse;
                    print('MOVED CALLED FROM DAHBOARD');
                    Navigator.pushNamed(
                        context, AddServiceRequestPage.routeName,
                        arguments: ServiceType(
                            type: ServiceRequestType.MOVE_EQUIPMENT));
                  } */
                  else if (getIt<ServiceType>().type ==
                      ServiceRequestType.ASSIGN_EQUIPMENT) {
                    ApplicationUtil.showSnackBar(
                        context: context,
                        message:
                            AppLocalizations.of(context)!.barcodeAlreadyAssign);
                  }
                } else if (state is BarcodeApiError) {
                  if (getIt<ServiceType>().type ==
                          ServiceRequestType.ASSIGN_EQUIPMENT &&
                      state.errorMessage == ApiResponse.BAR_CODE_NOT_FOUND) {
                    print('ASSIGNING============DASHBOARD>' +
                        state.barcodeNumber);
                    Navigator.pop(context);

                    Navigator.pushNamed(context, AssignEquipmentPage.routeName,
                        arguments: ServiceType(
                            type: ServiceRequestType.ASSIGN_EQUIPMENT,
                            barcodeNumber: state.barcodeNumber));
                  } else if (getIt<ServiceType>().type ==
                          ServiceRequestType.REPLACE_BARCODE &&
                      state.errorMessage == ApiResponse.BAR_CODE_NOT_FOUND) {
                    print('REPLACING============DASHBOARD>' +
                        state.barcodeNumber);
                    Navigator.pop(context);
                    Navigator.pushNamed(
                        context, ConfirmBarcodeReplacementPage.routeName,
                        arguments: ServiceType(
                            type: ServiceRequestType.REPLACE_BARCODE,
                            barcodeNumber: state.barcodeNumber));
                  } else if (getIt<ServiceType>().type ==
                          ServiceRequestType.ADD &&
                      state.errorMessage == ApiResponse.BAR_CODE_NOT_FOUND) {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, AssignEquipmentPage.routeName,
                        arguments: ServiceType(
                            type: ServiceRequestType.ASSIGN_EQUIPMENT,
                            barcodeNumber: state.barcodeNumber));
                  } else if (state.errorMessage == ApiResponse.INVALID_AUTH) {
                    Navigator.pushNamedAndRemoveUntil(
                        context, LoginPage.routeName, (route) => false,
                        arguments: true);
                  } else {
                    ApplicationUtil.showSnackBar(
                        context: context, message: state.errorMessage);

                    Navigator.pop(context);
                  }
                }
              },
              child: BlocBuilder<InternetCubit, InternetState>(
                builder: (context, state) {
                  return Material(
                    child: InkWell(
                      customBorder: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(15),
                      ),
                      onTap: hasCreateRole
                          ? () async {
                              getIt<ServiceType>().type =
                                  ServiceRequestType.ADD;
                              if (state is InternetConnected) {
                                String? customerType =
                                    getIt<SharedPreferences>()
                                        .getString('customerType');
                                if (customerType != null &&
                                    customerType == 'AIRLINE') {
                                  Navigator.pushNamed(context,
                                          AirCraftSelectionPage.routeName)
                                      .then((value) {
                                    serviceRequestBloc.add(
                                        SendPendingServiceRequestToServer());
                                    apiBloc.add(GetServiceRequestCount());
                                  });
                                } else {
                                  var result = await Navigator.pushNamed(
                                      context, AIBarCodeScannerPage.routeName);
                                  if (result != null) {
                                    Map<String, dynamic> resultData =
                                        result as Map<String, dynamic>;
                                    String returnedCode = resultData['code'];
                                    bool isScanned = resultData['isScanned'];
                                    if (isScanned) {
                                      if (locationProvider?.currentPosition !=
                                          null) {
                                        apiBloc.add(CallBarCodeApi(
                                            barcodeNumber: returnedCode,
                                            isTimedService: false,
                                            lattitude: locationProvider
                                                ?.currentPosition?.latitude,
                                            longitude: locationProvider
                                                ?.currentPosition?.longitude));
                                      } else {
                                        apiBloc.add(CallBarCodeApi(
                                            barcodeNumber: returnedCode,
                                            isTimedService: false));
                                      }
                                    } else {
                                      if (result ==
                                          'Unable to start scanning This app does not have permission to access the camera') {
                                        await openAppSettings();
                                      }
                                      apiBloc.add(CallBarCodeApi(
                                          barcodeNumber: returnedCode,
                                          isTimedService: false));
                                    }
                                    //setState(() {});
                                  }
                                }
                              } else {
                                ApplicationUtil.showSnackBar(
                                    context: context,
                                    message: AppLocalizations.of(context)!
                                        .pleaseConnectToInternet);
                              }
                            }
                          : null,
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 5),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(15),
                          border: Border.all(color: Colors.grey),
                        ),
                        child: ListTile(
                          leading: FaIcon(
                            FontAwesomeIcons.exclamationTriangle,
                            size: 30,
                            color: hasCreateRole ? Colors.red : Colors.grey,
                          ),
                          title: Text(
                            AppLocalizations.of(context)!.identifyConcern,
                            style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: !hasCreateRole ? Colors.grey : null),
                          ),
                          subtitle: Container(
                            margin: const EdgeInsets.only(top: 4),
                            child: Text(
                              AppLocalizations.of(context)!
                                  .createServiceRequestIfYouNotice,
                              style: TextStyle(
                                  fontSize: 16,
                                  color: !hasCreateRole ? Colors.grey : null),
                            ),
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            );
          }
          return const CircularProgressIndicator();
        },
      );

  _getRepairCategoryWidget() => BlocBuilder<UserBloc, UserState>(
        builder: (context, state) {
          if (state is LoginUserFetched) {
            UserData user = state.userData;
            bool hasCreateRole = user.MODULE!.contains("REPAIR");
            //check if user has access to mobile app
            bool hasNoMobile = user.MODULE!.contains("NO_MOBILE");
            if (hasNoMobile) {
              hasCreateRole = !hasCreateRole;
            }
            return BlocBuilder<InternetCubit, InternetState>(
              builder: (context, state) {
                return Material(
                  child: InkWell(
                    customBorder: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(15),
                    ),
                    onTap: hasCreateRole
                        ? () {
                            if (state is InternetConnected) {
                              _clearServiceNotification();
                              String? customerType = getIt<SharedPreferences>()
                                  .getString('customerType');
                              if (customerType != null &&
                                  customerType == 'AIRLINE') {
                                getIt<ServiceType>().type =
                                    ServiceRequestType.REPAIR;
                                Navigator.pushNamed(context,
                                        AirCraftSelectionPage.routeName)
                                    .then((value) {
                                  serviceRequestBloc
                                      .add(SendPendingServiceRequestToServer());
                                  apiBloc.add(GetServiceRequestCount());
                                });
                              } else {
                                Navigator.pushNamed(
                                        context, RepairListPage.routeName)
                                    .then((value) {
                                  serviceRequestBloc
                                      .add(SendPendingServiceRequestToServer());
                                });
                              }
                            } else {
                              ApplicationUtil.showSnackBar(
                                  context: context,
                                  message: AppLocalizations.of(context)!
                                      .pleaseConnectToInternet);
                            }
                          }
                        : null,
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 5),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(15),
                        border: Border.all(color: Colors.grey),
                      ),
                      child: ListTile(
                        leading: FaIcon(
                          FontAwesomeIcons.solidWrench,
                          size: 30,
                          color: !hasCreateRole ? Colors.grey : Colors.green,
                        ),
                        title: Text(
                          AppLocalizations.of(context)!.repair,
                          style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: !hasCreateRole ? Colors.grey : null),
                        ),
                        subtitle: Container(
                          margin: const EdgeInsets.only(top: 4),
                          child: Text(
                            AppLocalizations.of(context)!
                                .completeServiceRequesByService,
                            style: const TextStyle(fontSize: 16),
                          ),
                        ),
                      ),
                    ),
                  ),
                );
              },
            );
          }
          return const CircularProgressIndicator();
        },
      );

  _getTasksWidget() => BlocBuilder<UserBloc, UserState>(
        builder: (context, state) {
          if (state is LoginUserFetched) {
            UserData user = state.userData;
            bool hasCreateRole = user.MODULE!.contains(Module.EVENT_TASKS);
            //check if user has access to mobile app
            bool hasNoMobile = user.MODULE!.contains("NO_MOBILE");
            if (hasNoMobile) {
              hasCreateRole = !hasCreateRole;
            }
            return BlocListener<TaskBloc, TaskState>(
              listener: (context, state) {
                if (state is BarcodeApiCallingForTask) {
                  SchedulerBinding.instance.addPostFrameCallback((_) {
                    ApplicationUtil.showLoaderDialog(
                        context, '${AppLocalizations.of(context)!.fetching}..');
                  });
                } else if (state is BarcodeApiCalledForTask) {
                  context.read<TaskBloc>().add(ResetBarCodeApiForTask());
                  Navigator.pop(context);
                  Map<String, dynamic> data1 = taskTypeProvider!.task_types;
                  var data = taskTypeProvider!
                      .task_types[state.barcodeResponse.equipment!.categoryId!];
                  if (data == null || data1.containsKey('error')) {
                    ApplicationUtil.showSnackBar(
                        context: context,
                        message: data1['error'] ??
                            "No Task types assigned for this barcode.");
                    return;
                  }
                  Navigator.of(context).push(MaterialPageRoute(
                    builder: (context) =>
                        TaskPage(barcodeResponse: state.barcodeResponse),
                  ));
                  //     apiBloc.add(ResetBarCodeApiForTask() as ApiEvent);
                } else if (state is BarcodeApiErrorForTask) {
                  taskBloc.add(ResetBarCodeApiForTask());
                  Navigator.pop(context);
                  showDialog(
                    context: context,
                    builder: (context) => AlertDialog(
                      title: Text("Alert"),
                      content: Text(state.errorMessage),
                      actions: [
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(),
                          child: Text("OK"),
                        ),
                      ],
                    ),
                  );
                  //  Navigator.of(context).push(MaterialPageRoute(builder: (context) => TaskPage(errorMessage: state.errorMessage,),));
                  //   Navigator.of(context).push(MaterialPageRoute(builder: (context) => TaskPage(barcodeResponse: ),));
                }
              },
              child: BlocBuilder<InternetCubit, InternetState>(
                builder: (context, state) {
                  return Material(
                    child: InkWell(
                      customBorder: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(15),
                      ),
                      onTap: hasCreateRole
                          ? () async {
                              if (state is InternetConnected) {
                                var result = await Navigator.of(context)
                                    .push(MaterialPageRoute(
                                  builder: (context) =>
                                      const AIBarCodeScannerPage(),
                                ));
                                if (result != null) {
                                  Map<String, dynamic> resultData =
                                      result as Map<String, dynamic>;
                                  String returnedCode = resultData['code'];
                                  bool isScanned = resultData['isScanned'];
                                  if (isScanned) {
                                    if (locationProvider?.currentPosition !=
                                        null) {
                                      context.read<TaskBloc>().add(
                                          CallBarCodeApiForTask(
                                              barcodeNumber: returnedCode,
                                              isTimedService: false,
                                              latitude: locationProvider
                                                  ?.currentPosition?.latitude,
                                              longitude: locationProvider
                                                  ?.currentPosition
                                                  ?.longitude));
                                    } else {
                                      context.read<TaskBloc>().add(
                                          CallBarCodeApiForTask(
                                              barcodeNumber: returnedCode,
                                              isTimedService: false));
                                    }
                                  } else {
                                    context.read<TaskBloc>().add(
                                        CallBarCodeApiForTask(
                                            barcodeNumber: returnedCode,
                                            isTimedService: false));
                                  }
                                  if (result ==
                                      'Unable to start scanning This app does not have permission to access the camera') {
                                    await openAppSettings();
                                  }
                                }
                              } else {
                                ApplicationUtil.showSnackBar(
                                    context: context,
                                    message: AppLocalizations.of(context)!
                                        .pleaseConnectToInternet);
                              }
                            }
                          : null,
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 5),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(15),
                          border: Border.all(color: Colors.grey),
                        ),
                        child: ListTile(
                          leading: FaIcon(
                            FontAwesomeIcons.clipboardListCheck,
                            size: 30,
                            color: hasCreateRole
                                ? AppColor.primarySwatchColor
                                : Colors.grey,
                          ),
                          title: Text(
                            AppLocalizations.of(context)!.tasks,
                            style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: !hasCreateRole ? Colors.grey : null),
                          ),
                          subtitle: Container(
                            margin: const EdgeInsets.only(top: 4),
                            child: Text(
                              AppLocalizations.of(context)!
                                  .scanbarcodeandcompletetask,
                              style: TextStyle(
                                  fontSize: 16,
                                  color: !hasCreateRole ? Colors.grey : null),
                            ),
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            );
          }
          return const CircularProgressIndicator();
        },
      );

  _getTimedServicesWidget() => BlocBuilder<UserBloc, UserState>(
        builder: (context, state) {
          if (state is LoginUserFetched) {
            UserData user = state.userData;
            bool hasCreateRole = user.MODULE!.contains("REPAIR");
            //check if user has access to mobile app
            bool hasNoMobile = user.MODULE!.contains("NO_MOBILE");
            if (hasNoMobile) {
              hasCreateRole = !hasCreateRole;
            }
            return BlocBuilder<InternetCubit, InternetState>(
              builder: (context, state) {
                return Material(
                  child: InkWell(
                    customBorder: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(15),
                    ),
                    onTap: hasCreateRole
                        ? () {
                            if (state is InternetConnected) {
                              _clearServiceNotification();
                              String? customerType = getIt<SharedPreferences>()
                                  .getString('customerType');
                              if (customerType != null &&
                                  customerType == 'AIRLINE') {
                                getIt<ServiceType>().type =
                                    ServiceRequestType.SCHEDULED;
                                Navigator.pushNamed(context,
                                        AirCraftSelectionPage.routeName)
                                    .then((value) {
                                  serviceRequestBloc
                                      .add(SendPendingServiceRequestToServer());
                                  apiBloc.add(GetServiceRequestCount());
                                });
                              } else {
                                Navigator.pushNamed(
                                  context,
                                  TimedServiceRequests.routeName,
                                ).then((value) {
                                  serviceRequestBloc
                                      .add(SendPendingServiceRequestToServer());
                                });
                              }
                            } else {
                              ApplicationUtil.showSnackBar(
                                  context: context,
                                  message: AppLocalizations.of(context)!
                                      .pleaseConnectToInternet);
                            }
                          }
                        : null,
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 5),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(15),
                        border: Border.all(color: Colors.grey),
                      ),
                      child: ListTile(
                        leading: FaIcon(
                          FontAwesomeIcons.solidClock,
                          size: 30,
                          color: !hasCreateRole
                              ? Colors.grey
                              : AppColor.primarySwatchColor,
                        ),
                        title: Text(
                          AppLocalizations.of(context)!.timedServicesDashboard,
                          style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: !hasCreateRole ? Colors.grey : null),
                        ),
                        subtitle: Container(
                          margin: const EdgeInsets.only(top: 4),
                          child: Text(
                            AppLocalizations.of(context)!
                                .timedServiceDescription,
                            style: const TextStyle(fontSize: 16),
                          ),
                        ),
                      ),
                    ),
                  ),
                );
              },
            );
          }
          return const CircularProgressIndicator();
        },
      );

  _getAuditCategoryWidget() => BlocBuilder<UserBloc, UserState>(
        builder: (context, state) {
          if (state is LoginUserFetched) {
            UserData user = state.userData;
            bool hasCreateRole = user.MODULE!.contains("AUDIT");
            //check if user has access to mobile app
            bool hasNoMobile = user.MODULE!.contains("NO_MOBILE");
            if (hasNoMobile) {
              hasCreateRole = !hasCreateRole;
            }
            return BlocBuilder<InternetCubit, InternetState>(
                builder: (context, state) {
              return Material(
                child: InkWell(
                  customBorder: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(15),
                  ),
                  onTap: hasCreateRole
                      ? () {
                          if (state is InternetConnected) {
                            String? customerType = getIt<SharedPreferences>()
                                .getString('customerType');
                            if (customerType != null &&
                                customerType == 'AIRLINE') {
                              getIt<ServiceType>().type =
                                  ServiceRequestType.AUDIT;
                              Navigator.pushNamed(
                                      context, AirCraftSelectionPage.routeName)
                                  .then((value) {
                                serviceRequestBloc
                                    .add(SendPendingServiceRequestToServer());
                                apiBloc.add(GetServiceRequestCount());
                              });
                            } else {
                              Navigator.pushNamed(context, AuditPage.routeName);
                            }
                          } else {
                            ApplicationUtil.showSnackBar(
                                context: context,
                                message: AppLocalizations.of(context)!
                                    .pleaseConnectToInternet);
                          }
                        }
                      : null,
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 5),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(15),
                      border: Border.all(color: Colors.grey),
                    ),
                    child: ListTile(
                      leading: FaIcon(
                        FontAwesomeIcons.userSecret,
                        size: 35,
                        color: !hasCreateRole
                            ? Colors.grey
                            : const Color(0xff3F51B5),
                      ),
                      title: Text(
                        AppLocalizations.of(context)!.scheduledServices,
                        style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: !hasCreateRole ? Colors.grey : null),
                      ),
                      subtitle: Container(
                        margin: const EdgeInsets.only(top: 4),
                        child: Text(
                          AppLocalizations.of(context)!.scheduledAuditsAndTasks,
                          style: TextStyle(
                              fontSize: 16,
                              color: !hasCreateRole ? Colors.grey : null),
                        ),
                      ),
                    ),
                  ),
                ),
              );
            });
          } else {
            return const CircularProgressIndicator();
          }
        },
      );

  _getSettingIcon() => Positioned(
        bottom: 10,
        right: 10,
        child: IconButton(
          onPressed: () => Navigator.pushNamed(context, SettingsPage.routeName),
          icon: FaIcon(
            FontAwesomeIcons.solidCog,
            size: 25,
            color: Theme.of(context).primaryColor,
          ),
        ),
      );

  _allDataSyncedUI() => Container(
        width: 95,
        height: 95,
        decoration: BoxDecoration(
          color: AppColor.greenSentColor,
          borderRadius: BorderRadius.circular(50),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            const FaIcon(
              FontAwesomeIcons.check,
              color: Colors.white,
              size: 25,
            ),
            Text(
              AppLocalizations.of(context)!.allDataSync + "!",
              textAlign: TextAlign.center,
              style: const TextStyle(color: Colors.white, fontSize: 14),
            ),
            const SizedBox(
              height: 10,
            )
          ],
        ),
      );

  _syncingServiceRequestDataUI() => SizedBox(
        width: 95,
        height: 95,
        child: Stack(
          alignment: Alignment.center,
          children: [
            Container(
                width: 95,
                height: 95,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.white38, width: 4),
                  color: Colors.orange,
                  borderRadius: BorderRadius.circular(50),
                ),
                child: const CircularProgressIndicator(
                  color: Colors.orange,
                  strokeWidth: 8,
                )),
            Container(
              width: 85,
              height: 85,
              decoration: BoxDecoration(
                  color: Colors.orange,
                  borderRadius: BorderRadius.circular(50)),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const FaIcon(
                    FontAwesomeIcons.cloudUploadAlt,
                    color: Colors.white,
                    size: 20,
                  ),
                  Text(
                    AppLocalizations.of(context)!.syncing + " ..",
                    style: const TextStyle(color: Colors.white, fontSize: 13),
                  ),
                  const SizedBox(
                    height: 5,
                  )
                ],
              ),
            ),
          ],
        ),
      );

  _getPendingCountUI(int pendingCount) {
    return GestureDetector(
        onTap: () {
          serviceRequestBloc.add(SendPendingServiceRequestToServer());
        },
        child: Container(
          width: 95,
          height: 95,
          decoration: BoxDecoration(
              color: AppColor.redUnsentColor,
              border: Border.all(color: Colors.white70),
              borderRadius: BorderRadius.circular(50)),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                pendingCount.toString(),
                style: const TextStyle(
                    color: AppColor.redUnsentTextColor, fontSize: 22),
              ),
              Text(
                AppLocalizations.of(context)!.unsent,
                style: const TextStyle(color: AppColor.redUnsentTextColor),
              ),
              const FaIcon(
                FontAwesomeIcons.redo,
                size: 20,
                color: AppColor.redUnsentTextColor,
              )
            ],
          ),
        ));
  }

  void _clearServiceNotification() {
    serviceRequestBloc.add(DismissServiceRequestNotification());
  }

  getBannerMessage(SentPendingServiceRequestToServer state) {
    apiBloc.add(GetServiceRequestCount());
    switch (state.requestType) {
      case ServiceRequestType.ADD:
        return AppLocalizations.of(context)!.serviceRequestCreated;
      case ServiceRequestType.REPAIR:
        return AppLocalizations.of(context)!.repairCompleted;
      case ServiceRequestType.REPLACE_BARCODE:
        return AppLocalizations.of(context)!.barcodeReplaced;
      case ServiceRequestType.MOVE_EQUIPMENT:
        return AppLocalizations.of(context)!.equipmentMoved;
      case ServiceRequestType.ASSIGN_EQUIPMENT:
        return AppLocalizations.of(context)!.equipmentAssigned;
      case ServiceRequestType.NOTIFICATION:
        return AppLocalizations.of(context)!.notificationCreated;
      default:
        return AppLocalizations.of(context)!.serviceRequestCreated;
    }
  }

  _getCustomerName() {
    String? customerName = getIt<SharedPreferences>().getString('customerName');
    if (customerName != null) {
      return Text(
        customerName,
        style: TextStyle(color: Theme.of(context).primaryColor, fontSize: 20),
      );
    }
    return Container();
  }

  void _getAppUpdateVersionDialog() async {
    bool isSandbox = AppConstant.BASE_URL == "https://api.sbox.alinkhub.com";
    print("isSandbox: $isSandbox\nBase url: ${AppConstant.BASE_URL}");
    AppVersion appVersion = await fetchAppVersion(isSandbox: isSandbox);

    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    int currentVersion = stringToIntWithoutDots(packageInfo.version);

    final prefs = await SharedPreferences.getInstance();
    final lastRemindTime = prefs.getInt('lastRemindTime');
    final now = DateTime.now().millisecondsSinceEpoch;

    if (lastRemindTime == null || now - lastRemindTime >= 48 * 60 * 60 * 1000) {
      if (Platform.isAndroid &&
          _isVersionLower(
              currentVersion, stringToIntWithoutDots(appVersion.iosVersion))) {
        _showUpdateDialog(appVersion.androidVersion, packageInfo.packageName);
      } else if (Platform.isIOS &&
          _isVersionLower(
              currentVersion, stringToIntWithoutDots(appVersion.iosVersion))) {
        _showUpdateDialog(appVersion.iosVersion, '1588880668');
      }
    }
  }

  bool _isVersionLower(int currentVersion, int latestVersion) {
    // Implement your version comparison logic here
    // This is a simple example, you might need more robust logic
    // for handling different version formats
    return currentVersion.compareTo(latestVersion) < 0;
  }

  int stringToIntWithoutDots(String str) {
    String newStr = str.replaceAll('.', '');
    return int.tryParse(newStr) ?? 0;
  }

  void _showUpdateDialog(String latestVersion, String packageName) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('New Update Available!'),
          content: Text(
              'A new version of the app is available (v$latestVersion). Update now to enjoy the latest features and improvements.'),
          actions: [
            TextButton(
              onPressed: () async {
                Navigator.of(context).pop();
                /*StoreRedirect.redirect(
                  androidAppId: packageName.toString(),
                  iOSAppId: "1588880668",
                );*/
                if (Platform.isIOS) {
                  String url = 'https://apps.apple.com/app/id$packageName';
                  if (await canLaunchUrl(Uri.parse(url))) {
                    await launchUrl(
                      Uri.parse(url),
                      mode: LaunchMode.externalApplication,
                    );
                  } else {
                    throw 'Could not launch $url';
                  }
                } else if (Platform.isAndroid) {
                  String url =
                      'https://play.google.com/store/apps/details?id=$packageName';
                  if (await canLaunchUrl(Uri.parse(url))) {
                    await launchUrl(Uri.parse(url),
                        mode: LaunchMode.externalApplication);
                  } else {
                    throw 'Could not launch $url';
                  }
                }
              },
              child: const Text('Download Now'),
            ),
            TextButton(
              onPressed: () async {
                Navigator.of(context).pop();
                final prefs = await SharedPreferences.getInstance();
                final now = DateTime.now().millisecondsSinceEpoch;
                await prefs.setInt('lastRemindTime', now);
              },
              child: const Text('Remind Me Later'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('Cancel'),
            ),
          ],
        );
      },
    );
  }
}
