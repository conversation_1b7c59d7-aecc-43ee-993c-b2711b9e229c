import 'dart:ui';

class AppColor {
  static const primarySwatchColor = Color(0xFF00629B);
  static const primaryColorLight = Color(0xFF6252A7);
  //static const greyTextColor = Color(0xffAAAAAA);
  //static const greyTextColor = Color(0xff727272);
  static const greyTextColor = Color(0xff474747);
  static const blackTextColor = Color(0xff101010);
  static const greyBorderColor = Color(0xffbbbbbb);
  static const redColor = Color(0xffE4002B);
  static const redUnsentColor = Color(0xffFFEBEE);
  static const greenSentColor = Color(0xff259B24);
  static const redUnsentTextColor = Color(0xffF44336);
  static const greenServiceRequestColor = Color(0xffE0F2F1);
  static const greenServiceRequestTextColor = Color(0xff009688);
  static const purpleServiceRequestColor = Color(0xffF3E5F5);
  static const purpleServiceRequestTextColor = Color(0xff9C27B0);
  static const blueServiceRequestColor = Color(0xffE8EAF6);
  static const blueServiceRequestColorDark = Color(0xffd0d2f4);
  static const blueServiceRequestTextColor = Color(0xff3F51B5);
  static const orangeColor = Color(0xffFF9800);
  static const deepOrangeColor=Color(0xffFF5400);
}
