import 'dart:convert';
import 'dart:io';

import 'package:alink/pages/image_editor/annotated_image_page.dart';
import 'package:alink/pages/image_editor/crop_image_page.dart';
import 'package:alink/util/application_util.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:http/http.dart' as http;
import 'package:image_picker/image_picker.dart';

import '../../logger/logger.dart';

class EditImageHomePage extends StatefulWidget {
  static const routeName = '/image';
  final argumentImage;

  const EditImageHomePage({Key? key, this.argumentImage}) : super(key: key);

  @override
  _EditImageHomePageState createState() => _EditImageHomePageState();
}

class _EditImageHomePageState extends State<EditImageHomePage> {
  static String className = '_EditImageHomePageState';
  bool isImageAdded = false;
  Map<String, dynamic> selectedFileMap = {};
  final ImagePicker _picker = ImagePicker();
  dynamic _pickImageError;
  late String base64Img;
  Logger logger = Logger();

  @override
  void initState() {
    base64Img = widget.argumentImage;

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    Logger.i("Class Name: $className");
    return WillPopScope(
      onWillPop: () async {
        ApplicationUtil.showUnsavedWarningAlertDialog(context);
        return false;
      },
      child: Scaffold(
        /*appBar: AppBar(
          title: Text('Image Picker'),
          actions: [
            IconButton(
              onPressed: () {
                returnedEditedImage();
              },
              icon: Icon(Icons.done),
            )
          ],
        ),*/
        bottomNavigationBar: _buildButtons(),
        body: SafeArea(
          child: SizedBox(
            width: double.infinity,
            height: double.infinity,
            child: Container(
              decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: const BorderRadius.all(Radius.circular(5))),
              height: 400,
              width: 350,
              margin: const EdgeInsets.all(10),
              child: _getImage(),
            ),
          ),
        ),
        floatingActionButton: ApplicationUtil.getBackButton(
          context,
          onBackPressed: () {
            ApplicationUtil.showUnsavedWarningAlertDialog(context);
            //Navigator.pop(context);
          },
        ),
      ),
    );
  }

  Widget _getImage() {
    if (base64Img != null) {
      return Image.memory(base64Decode(base64Img));
    } else if (_pickImageError != null) {
      return Text(
        '${AppLocalizations.of(context)!.pickImageError}: $_pickImageError',
        textAlign: TextAlign.center,
      );
    } else {
      return Center(
        child: Text(
          AppLocalizations.of(context)!.youHaveNotYetPickedImage,
          textAlign: TextAlign.center,
        ),
      );
    }
  }

  void _onImageButtonPressed(ImageSource source,
      {required BuildContext context, bool isMultiImage = false}) async {
    try {
      Logger.e('${ApplicationUtil.getFormattedCurrentDateAndTime()} ' +
          "Image Button Pressed");
      final pickedFile = await _picker.pickImage(
        source: source,
        imageQuality: 70,
        maxHeight: 480,
        maxWidth: 640,
      );
      if (kIsWeb) {
        final http.Response responseData =
            await http.get(Uri.parse(pickedFile!.path));
        var uint8list = responseData.bodyBytes;
        base64Img = base64Encode(uint8list);
      } else {
        var uint8list = File(pickedFile!.path).readAsBytesSync();
        base64Img = base64Encode(uint8list);
      }

      setState(() {});
    } catch (e) {
      Logger.e('${ApplicationUtil.getFormattedCurrentDateAndTime()} ' +
          e.toString());
      setState(() {
        _pickImageError = e;
      });
    }
  }

  _buildButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        IconButton(
          icon: const FaIcon(FontAwesomeIcons.images),
          onPressed: () {
            _onImageButtonPressed(ImageSource.gallery, context: context);
          },
        ),
        IconButton(
          icon: const FaIcon(FontAwesomeIcons.camera),
          onPressed: () {
            _onImageButtonPressed(ImageSource.camera, context: context);
          },
        ),
        IconButton(
          icon: const FaIcon(FontAwesomeIcons.cropAlt),
          onPressed: () async {
            cropImage();
          },
        ),
        IconButton(
          icon: const FaIcon(FontAwesomeIcons.edit),
          onPressed: () {
            editImage(context);
          },
        ),
        IconButton(
          onPressed: () {
            returnedEditedImage();
          },
          icon: const FaIcon(FontAwesomeIcons.check),
        ),
      ],
    );
  }

  Future<void> editImage(BuildContext context) async {
    if (base64Img != null) {
      base64Img = await Navigator.pushNamed(
              context, AnnotatedImagePage.routeName, arguments: base64Img)
          as String;
      setState(() {});
    } else {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text(
        AppLocalizations.of(context)!.noImageSelected,
      )));
    }
  }

  Future<void> cropImage() async {
    if (base64Img != null) {
      base64Img = await Navigator.pushNamed(context, CropImagePage.routeName,
          arguments: base64Img) as String;
      setState(() {});
    } else {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text(
        AppLocalizations.of(context)!.noImageSelected,
      )));
    }
  }

  void returnedEditedImage() {
    if (base64Img != null) {
      Navigator.pop(context, base64Img);
    } else {
      Navigator.pop(context);
    }
  }
}
