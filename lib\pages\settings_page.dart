import 'dart:io';

import 'package:alink/bloc/repair_db_bloc/repair_bloc.dart';
import 'package:alink/bloc/service/service_request_bloc.dart';
import 'package:alink/bloc/user_bloc.dart';
import 'package:alink/cubit/pages/settings/settings_cubit.dart';
import 'package:alink/data/repository/api_service.dart';
import 'package:alink/database/database.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../logger/logger.dart';
import 'package:alink/pages/auth/login_page.dart';
import 'package:alink/service_locator.dart';
import 'package:alink/util/app_color.dart';
import 'package:alink/util/app_constant.dart';
import 'package:alink/util/application_util.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import 'package:package_info_plus/package_info_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../data/model/audit_location.dart';

class SettingsPage extends StatefulWidget {
  static const routeName = "/settings";

  const SettingsPage({Key? key}) : super(key: key);

  @override
  _SettingsPageState createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  late SharedPreferences prefs;

  @override
  void initState() {
    settingCubit.getLogPref();
    super.initState();
  }

  SettingsCubit get settingCubit => BlocProvider.of<SettingsCubit>(context);

  UserBloc get userBlocLogout => BlocProvider.of<UserBloc>(context);

  Database get db => RepositoryProvider.of<Database>(context);

  RepairBloc get repairBloc => BlocProvider.of<RepairBloc>(context);

  ServiceRequestBloc get serviceRequestBloc =>
      BlocProvider.of<ServiceRequestBloc>(context);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Center(
          child: Container(
            constraints: const BoxConstraints(maxWidth: 500),
            child: Stack(
              alignment: Alignment.bottomCenter,
              children: [
                Column(
                  children: [
                    _getSettingsAppBar(),
                    Expanded(
                      child: SingleChildScrollView(
                        child: Container(
                          margin: const EdgeInsets.symmetric(horizontal: 10),
                          padding: const EdgeInsets.symmetric(vertical: 5),
                          alignment: Alignment.center,
                          child: Column(
                            children: [
                              _setUpLogOption(),
                              !kIsWeb ? _sendLogToServer() : Container(),
                              _resetListTile(),
                              FutureBuilder<bool>(
                                future: checkForPendingData(),
                                builder: (context, snapshot) {
                                  if (snapshot.hasData) {
                                    if (snapshot.data == true) {
                                      return _disabledLogoutListTile();
                                    } else {
                                      return _logoutListTile();
                                    }
                                  } else {
                                    return const CircularProgressIndicator();
                                  }
                                },
                              ),
                              FutureBuilder(
                                future: _checkForAppUpdate(),
                                builder: (context, snapshot) {
                                  if (snapshot.hasData) {
                                    if (snapshot.data != "") {
                                      return _showUpdateNowTile(
                                          snapshot.data.toString());
                                    } else {
                                      return const SizedBox();
                                    }
                                  } else {
                                    return const SizedBox();
                                  }
                                },
                              ),
                              // _simulateCrash(),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                FutureBuilder<PackageInfo>(
                  future: PackageInfo.fromPlatform(),
                  builder: (context, snapshot) {
                    if (snapshot.hasData) {
                      return Container(
                          margin: const EdgeInsets.all(20),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              AppConstant.BASE_URL ==
                                      "https://api.sbox.alinkhub.com"
                                  ? Text(
                                      "Sbox",
                                      style: TextStyle(
                                          color:
                                              Theme.of(context).primaryColor),
                                    )
                                  : Text(
                                      "Prod",
                                      style: TextStyle(
                                          color:
                                              Theme.of(context).primaryColor),
                                    ),
                              const SizedBox(
                                height: 5,
                              ),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    'Ver:${snapshot.data!.version} ',
                                    style: TextStyle(
                                        color: Theme.of(context).primaryColor),
                                  ),
                                  Text(
                                    '${AppLocalizations.of(context)!.buildNo} :${snapshot.data!.buildNumber}',
                                    style: TextStyle(
                                        color: Theme.of(context).primaryColor),
                                  ),
                                ],
                              ),
                            ],
                          ));
                    } else {
                      return Container();
                    }
                  },
                )
              ],
            ),
          ),
        ),
      ),
      floatingActionButton: ApplicationUtil.getBackButton(
        context,
        onBackPressed: () {
          Navigator.pop(context);
        },
      ),
    );
  }

  _getSettingsAppBar() => Container(
        margin: const EdgeInsets.symmetric(horizontal: 5),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            IconButton(
              onPressed: () {},
              icon: FaIcon(
                FontAwesomeIcons.solidCog,
                color: Theme.of(context).primaryColor,
                size: 25,
              ),
            ),
            Text(
              AppLocalizations.of(context)!.settings,
              style: const TextStyle(
                  color: AppColor.blackTextColor,
                  fontSize: AppConstant.toolbarTitleFontSize,
                  fontWeight: FontWeight.bold),
            ),
          ],
        ),
      );

  _resetListTile() => InkWell(
        onTap: () {
          showResetAlertDialog(context);
        },
        child: ListTile(
          contentPadding: const EdgeInsets.symmetric(horizontal: 15),
          title: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                FontAwesomeIcons.solidTrash,
                color: AppColor.redColor,
                size: 20,
              ),
              const SizedBox(
                width: 10,
              ),
              Expanded(
                child: Text(
                  AppLocalizations.of(context)!.resetApp,
                  style: const TextStyle(fontSize: 18),
                ),
              ),
            ],
          ),
        ),
      );

  _sendLogToServer() => InkWell(
        onTap: () async {
          ApplicationUtil.launchEmail(context);
        },
        child: ListTile(
          contentPadding: const EdgeInsets.symmetric(horizontal: 15),
          title: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                FontAwesomeIcons.solidFileExport,
                size: 20,
              ),
              const SizedBox(
                width: 10,
              ),
              Expanded(
                child: Text(
                  AppLocalizations.of(context)!.sendLog,
                  style: const TextStyle(fontSize: 18),
                ),
              ),
            ],
          ),
        ),
      );

  Future<String> _checkForAppUpdate() async {
    bool isSandbox = AppConstant.BASE_URL == "https://api.sbox.alinkhub.com";
    AppVersion appVersion = await fetchAppVersion(isSandbox: isSandbox);

    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    int currentVersion = stringToIntWithoutDots(packageInfo.version);

    if (Platform.isAndroid &&
        _isVersionLower(currentVersion,
            stringToIntWithoutDots(appVersion.androidVersion))) {
      return appVersion.androidVersion;
      // _showUpdateDialog(appVersion.androidVersion,packageInfo.packageName);
    } else if (Platform.isIOS &&
        _isVersionLower(
            currentVersion, stringToIntWithoutDots(appVersion.iosVersion))) {
      return appVersion.iosVersion;
      // _showUpdateDialog(appVersion.iosVersion,'1588880668 ');
    } else {
      return "";
    }
  }

  void _getAppUpdateVersionDialog() async {
    ApplicationUtil.showLoaderDialog(
      context,
      "Please wait checking for application updates",
    );
    bool isSandbox = AppConstant.BASE_URL == "https://api.sbox.alinkhub.com";
    AppVersion appVersion = await fetchAppVersion(isSandbox: isSandbox);

    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    int currentVersion = stringToIntWithoutDots(packageInfo.version);

    Navigator.of(context).pop();
    if (Platform.isAndroid &&
        _isVersionLower(
            currentVersion, stringToIntWithoutDots(appVersion.iosVersion))) {
      _showUpdateDialog(appVersion.androidVersion, packageInfo.packageName);
    } else if (Platform.isIOS &&
        _isVersionLower(
            currentVersion, stringToIntWithoutDots(appVersion.iosVersion))) {
      _showUpdateDialog(appVersion.iosVersion, '1588880668');
    }
  }

  void _showUpdateDialog(String latestVersion, String packageName) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('New Update Available!'),
          content: Text(
              'A new version of the app is available (v$latestVersion). Update now to enjoy the latest features and improvements.'),
          actions: [
            TextButton(
              onPressed: () async {
                Navigator.of(context).pop();
                /*StoreRedirect.redirect(
                  androidAppId: packageName.toString(),
                  iOSAppId: "1588880668",
                );*/
                if (Platform.isIOS) {
                  String url = 'https://apps.apple.com/app/id$packageName';
                  if (await canLaunchUrl(Uri.parse(url))) {
                    await launchUrl(
                      Uri.parse(url),
                      mode: LaunchMode.externalApplication,
                    );
                  } else {
                    throw 'Could not launch $url';
                  }
                } else if (Platform.isAndroid) {
                  String url =
                      'https://play.google.com/store/apps/details?id=$packageName';
                  if (await canLaunchUrl(Uri.parse(url))) {
                    await launchUrl(Uri.parse(url),
                        mode: LaunchMode.externalApplication);
                  } else {
                    throw 'Could not launch $url';
                  }
                }
              },
              child: const Text('Download Now'),
            ),
            /*TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                // TODO: Implement remind me later logic (e.g., schedule a reminder)
              },
              child: const Text('Remind Me Later'),),*/
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('Cancel'),
            ),
          ],
        );
      },
    );
  }

  bool _isVersionLower(int currentVersion, int latestVersion) {
    // Implement your version comparison logic here
    // This is a simple example, you might need more robust logic
    // for handling different version formats
    return currentVersion.compareTo(latestVersion) < 0;
  }

  int stringToIntWithoutDots(String str) {
    String newStr = str.replaceAll('.', '');
    return int.tryParse(newStr) ?? 0;
  }

  void showResetAlertDialog(BuildContext ctx) {
    AlertDialog alert = AlertDialog(
      title: Text(AppLocalizations.of(context)!.resetApp),
      content: Text(
        AppLocalizations.of(context)!.thisWillDeleteAllData,
        textAlign: TextAlign.left,
        style: const TextStyle(
            fontSize: 15, letterSpacing: 1, wordSpacing: 1, height: 1.2),
      ),
      actions: [
        ElevatedButton(
          style: ButtonStyle(
              backgroundColor: MaterialStateProperty.all(AppColor.redColor)),
          child: Text(AppLocalizations.of(context)!.reset),
          onPressed: () async {
            if (!kIsWeb) {
              try {
                Logger.i('=====DELETED IF DATA SYNC EXIST=====');
                db.deleteEverything().then((value) {
                  Navigator.pop(ctx);
                  Logger.i('DELETED DATABASE');

                  ///DELETTING PREFERENCES
                  getIt<SharedPreferences>().clear();
                  Logger.i('CLEAR SHARED PREFERENCES');

                  Logger.i('SHOW DIALOG');
                  //
                  showDialog(
                    barrierDismissible: false,
                    context: ctx,
                    builder: (context) {
                      return WillPopScope(
                        onWillPop: () async {
                          return false;
                        },
                        child: AlertDialog(
                          title: Text(
                              AppLocalizations.of(context)!.restartApplication),
                          content: Text(
                            AppLocalizations.of(context)!.deleteAllDataFromApp,
                            textAlign: TextAlign.left,
                            style:
                                const TextStyle(fontSize: 15, letterSpacing: 1),
                          ),
                        ),
                      );
                    },
                  );
                }).catchError((e) {
                  Logger.e(
                      "Error while deleting the application data. ${e.toString()}");
                });

                /* Logger.i('GET APP SUPPORT DIR');
                final appDir = await getApplicationSupportDirectory();

                ///DELETING getApplicationSupportDirectory
                if (appDir.existsSync()) {
                  appDir.deleteSync(recursive: true);
                }

                ///DELETING getTemporaryDirectory
                Logger.i('STARTING CLEAR LOG');
                final cacheDir = await getTemporaryDirectory();
                Logger.i('CACHE DIR');
                if (cacheDir.existsSync()) {
                  cacheDir.deleteSync(recursive: true);
                }*/
              } catch (e) {
                Logger.e(e.toString());
                print(e.toString());
              }
            } else {
              getIt<SharedPreferences>().clear().then((value) {
                db.deleteEverything().then((value) => {
                      Navigator.pushNamedAndRemoveUntil(
                          context, LoginPage.routeName, (route) => false)
                    });
              });
            }
          },
        ),
        ElevatedButton(
          child: Text(AppLocalizations.of(context)!.cancel),
          onPressed: () {
            Navigator.of(ctx).pop();
          },
        ),
      ],
    );
    showDialog(
      barrierDismissible: false,
      context: ctx,
      builder: (BuildContext context) {
        return alert;
      },
    );
  }

  _setUpLogOption() {
    return BlocBuilder<SettingsCubit, SettingsState>(
      builder: (context, state) {
        if (state is LogPreferenceFetched) {
          return PopupMenuButton<String>(
            offset: const Offset(kToolbarHeight, 0),
            onSelected: (String value) async {
              //_selection = value;
              settingCubit.updateLogPref(value);
            },
            child: setDefaultLogOption(state),
            itemBuilder: (context) {
              return _getPopUpMenuItem(state);
            },
          );
        }
        return Container();
      },
    );
  }

  _getPopUpMenuItem(LogPreferenceFetched state) => <PopupMenuEntry<String>>[
        PopupMenuItem<String>(
          value: AppConstant.VERBOSE,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(AppConstant.VERBOSE),
              state.selection == AppConstant.VERBOSE
                  ? FaIcon(
                      FontAwesomeIcons.check,
                      size: 20,
                      color: Theme.of(context).primaryColor,
                    )
                  : Container()
            ],
          ),
        ),
        PopupMenuItem<String>(
          value: AppConstant.DEBUG,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(AppConstant.DEBUG),
              state.selection == AppConstant.DEBUG
                  ? FaIcon(
                      FontAwesomeIcons.check,
                      size: 20,
                      color: Theme.of(context).primaryColor,
                    )
                  : Container()
            ],
          ),
        ),
        PopupMenuItem<String>(
          value: AppConstant.INFO,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(AppConstant.INFO),
              state.selection == AppConstant.INFO
                  ? FaIcon(
                      FontAwesomeIcons.check,
                      size: 20,
                      color: Theme.of(context).primaryColor,
                    )
                  : Container()
            ],
          ),
        ),
        PopupMenuItem<String>(
          value: AppConstant.WARNING,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(AppConstant.WARNING),
              state.selection == AppConstant.WARNING
                  ? FaIcon(
                      FontAwesomeIcons.check,
                      size: 20,
                      color: Theme.of(context).primaryColor,
                    )
                  : Container()
            ],
          ),
        ),
        PopupMenuItem<String>(
          value: AppConstant.ERROR,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(AppConstant.ERROR),
              state.selection == AppConstant.ERROR
                  ? FaIcon(
                      FontAwesomeIcons.check,
                      size: 20,
                      color: Theme.of(context).primaryColor,
                    )
                  : Container()
            ],
          ),
        )
      ];

  setDefaultLogOption(LogPreferenceFetched state) => ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 15),
        title: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              FontAwesomeIcons.solidHistory,
              size: 20,
            ),
            const SizedBox(
              width: 10,
            ),
            Expanded(
              child: Text(
                AppLocalizations.of(context)!.setLogLevel,
                style: const TextStyle(fontSize: 18),
              ),
            ),
            Text(
              state.selection,
              style: const TextStyle(fontSize: 16, color: Colors.grey),
            ),
            const SizedBox(
              width: 10,
            ),
            const FaIcon(
              FontAwesomeIcons.arrowsAltV,
              color: Colors.grey,
              size: 15,
            )
          ],
        ),
      );

  _logoutListTile() {
    return InkWell(
      onTap: () async {
        try {
          await getIt<SharedPreferences>().setBool('isLoggedIn', false);
          await getIt<SharedPreferences>().setBool('isAfs', false);
          InMemoryAudiData.clear();
          db.truncateTables().then((value) {
            Navigator.pushNamedAndRemoveUntil(
                context, LoginPage.routeName, (route) => false,
                arguments: true);
          });
        } catch (e) {
          Logger.e(e.toString());
          print(e.toString());
        }
      },
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 15),
        title: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              FontAwesomeIcons.solidSignOutAlt,
              size: 20,
            ),
            const SizedBox(
              width: 10,
            ),
            Expanded(
              child: Text(
                AppLocalizations.of(context)!.logout,
                style: const TextStyle(fontSize: 18),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<bool> checkForPendingData() async {
    dynamic pendingRepairRequest =
        await repairBloc.repairDao.getPendingRepairFromDb();
    dynamic pendingServiceRequest =
        await serviceRequestBloc.serviceRequestDao.getAllServiceRequest();

    if (pendingRepairRequest == null && pendingServiceRequest == null) {
      return false;
    } else {
      if (pendingRepairRequest != null && pendingRepairRequest.length > 0) {
        return true;
      }
      if (pendingServiceRequest != null && pendingServiceRequest.length > 0) {
        return true;
      }
      return false;
    }
  }

  Widget _disabledLogoutListTile() {
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(horizontal: 15),
      title: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            FontAwesomeIcons.solidSignOutAlt,
            size: 20,
            color: Colors.grey,
          ),
          const SizedBox(
            width: 10,
          ),
          Text(
            AppLocalizations.of(context)!.logout,
            style: const TextStyle(fontSize: 18, color: Colors.grey),
          ),
          const SizedBox(
            width: 50,
          ),
          Expanded(
            child: Text(
              AppLocalizations.of(context)!.allDataNeedToSync,
              style: const TextStyle(height: 1.2, color: Colors.grey),
            ),
          )
        ],
      ),
    );
  }

  Widget _showUpdateNowTile(String appVersion) {
    return InkWell(
      onTap: () async {
        _getAppUpdateVersionDialog();
      },
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 15),
        title: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              FontAwesomeIcons.download,
              size: 20,
            ),
            const SizedBox(
              width: 10,
            ),
            const Expanded(
              child: Text(
                'New version available',
                style: TextStyle(fontSize: 18),
              ),
            ),
            Text(
              '$appVersion',
              style: const TextStyle(fontSize: 18),
            )
          ],
        ),
      ),
    );
  }
}
