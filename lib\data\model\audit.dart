import 'package:drift/drift.dart';

class Audit extends Table {
  @override
  String get tableName => 'AUDIT';
  IntColumn get auditId => integer().autoIncrement().named('AUDIT_ID')();
  TextColumn get auditedBy => text().nullable().named('AUDITED_BY')();
  IntColumn get auditedAt => integer().nullable().named('AUDITED_AT')();
  TextColumn get gpsLocation => text().nullable().named('GPS_LOCATION')();
}
