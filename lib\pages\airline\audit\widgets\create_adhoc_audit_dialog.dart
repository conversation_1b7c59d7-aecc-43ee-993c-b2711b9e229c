import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../bloc/api/api_bloc.dart';
import '../../../../cubit/pages/audit/audit_list_cubit.dart';
import '../../../../data/model/audit.dart';
import '../../../../data/model/audit_equipment.dart';
import '../../../../data/model/audit_location.dart';
import '../../../../util/app_color.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import '../../../../util/application_util.dart';
import '../../../auth/login_page.dart';

class CreateAirlineAdhocAuditDialog extends StatefulWidget {
  final BuildContext context;
  late final List<AuditEquipment> auditEquipmentList;
  late final Audit? selectedAudit;
  late final List<Audit> auditList;
  final AuditListCubit auditCubit;
  final String locationId;
  final ApiBloc apiBloc;

  CreateAirlineAdhocAuditDialog(
      {Key? key,
      required this.auditEquipmentList,
      required this.selectedAudit,
      required this.auditList,
      required this.auditCubit,
      required this.locationId,
      required this.apiBloc,
      required this.context})
      : super(key: key);

  @override
  _CreateAirlineAdhocAuditDialogState createState() =>
      _CreateAirlineAdhocAuditDialogState();
}

class _CreateAirlineAdhocAuditDialogState
    extends State<CreateAirlineAdhocAuditDialog> {
  int radioValue = InMemoryAudiData.selectedSegmentValueInMemory ?? 0;
  late TextEditingController _auditCommentController;
  int? selectedSegmentValue = 0;

  @override
  void initState() {
    _auditCommentController = TextEditingController();
    if (InMemoryAudiData.selectedSegmentValueInMemory != null) {
      if (InMemoryAudiData.selectedSegmentValueInMemory! == 1 ||
          InMemoryAudiData.selectedSegmentValueInMemory == 0) {
        selectedSegmentValue == 0;
        radioValue = 0;
      } else {
        selectedSegmentValue = 1;
        radioValue = 1;
      }
    } else {
      selectedSegmentValue = 0;
      radioValue = 1;
    }
    super.initState();
  }

  @override
  void dispose() {
    _auditCommentController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    AlertDialog alert = AlertDialog(
      title: Text(AppLocalizations.of(context)!.createAdhocAudit,
          style: TextStyle(
              fontSize: 20,
              color: Theme.of(context).primaryColor,
              fontWeight: FontWeight.bold)),
      content: Container(
        width: double.maxFinite,
        constraints: const BoxConstraints(maxWidth: 500),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            /*Text(
              AppLocalizations.of(context)!.provideCommentForAudit,
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(
              height: 10,
            ),*/
            RichText(
              textAlign: TextAlign.start,
              text: TextSpan(
                  style: const TextStyle(fontSize: 18),
                  children: <TextSpan>[
                    const TextSpan(
                      text: '* ',
                      style: TextStyle(
                          color: AppColor.redColor,
                          fontWeight: FontWeight.bold),
                    ),
                    TextSpan(
                        text: "Select Service Type",
                        style: TextStyle(
                            color: Theme.of(context).primaryColor,
                            fontWeight: FontWeight.bold)),
                  ]),
            ),
            const SizedBox(
              height: 5,
            ),
            buildRadioButtons(radioValue),
            const SizedBox(
              height: 10,
            ),
            RichText(
              textAlign: TextAlign.start,
              text: TextSpan(
                  style: const TextStyle(fontSize: 18),
                  children: <TextSpan>[
                    const TextSpan(
                      text: '* ',
                      style: TextStyle(
                          color: AppColor.redColor,
                          fontWeight: FontWeight.bold),
                    ),
                    TextSpan(
                        text: AppLocalizations.of(context)!.addComment,
                        style: TextStyle(
                            color: Theme.of(context).primaryColor,
                            fontWeight: FontWeight.bold)),
                  ]),
            ),
            const SizedBox(
              height: 5,
            ),
            TextField(
              controller: _auditCommentController,
              decoration: InputDecoration(
                border: const OutlineInputBorder(
                  borderRadius: BorderRadius.all(
                    Radius.circular(5.0),
                  ),
                ),
                hintText: AppLocalizations.of(context)!.creatingAnAdhoc,
                //labelText: 'Leave comment',
              ),
              minLines: 2,
              maxLines: 3,
            ),
          ],
        ),
      ),
      actions: [
        BlocConsumer<ApiBloc, ApiState>(
          listener: (context, state) {
            if (state is NewAuditCreated) {
              Navigator.pop(context);
              if (selectedSegmentValue == 0) {
                ApplicationUtil.showSnackBar(
                    context: context,
                    message:
                        AppLocalizations.of(context)!.auditCreateSuccessfully);
              } else {
                ApplicationUtil.showSnackBar(
                    context: context,
                    message:
                        AppLocalizations.of(context)!.taskCreateSuccessfully);
              }
              widget.auditEquipmentList = [];
              widget.selectedAudit = Audit();
              widget.auditList = [];
              widget.auditCubit.getAuditListData(
                  0, widget.locationId, null, _getSelectedValue(radioValue));
            } else if (state is ErrorCreatingNewAudit) {
              if (state.errorMessage == ApiResponse.INVALID_AUTH) {
                Navigator.pushNamedAndRemoveUntil(
                    context, LoginPage.routeName, (route) => false,
                    arguments: true);
              } else {
                ApplicationUtil.showSnackBar(
                    context: context, message: state.errorMessage);
              }
            }
          },
          builder: (context, state) {
            if (state is CreatingNewAudit) {
              return Container(
                margin: const EdgeInsets.symmetric(horizontal: 20),
                width: 25,
                height: 25,
                child: const CircularProgressIndicator(),
              );
            }
            return ElevatedButton(
              style: const ButtonStyle(
                  //backgroundColor: MaterialStateProperty.all(AppColor.redColor),
                  ),
              child: Text(AppLocalizations.of(context)!.create),
              onPressed: () {
                if (_auditCommentController.text.isNotEmpty) {
                  widget.apiBloc.add(CreateNewAudit(
                      locationId: widget.locationId,
                      comment: _auditCommentController.text,
                      serviceType: selectedSegmentValue.toString()));
                } else {
                  ApplicationUtil.showSnackBar(
                      context: context,
                      message: AppLocalizations.of(context)!.commentIsRequired);
                }
              },
            );
          },
        ),
        ElevatedButton(
          child: Text(AppLocalizations.of(context)!.cancel),
          onPressed: () {
            Navigator.of(context).pop("cancel");
          },
        ),
      ],
    );
    return alert;
  }

  Widget buildRadioButtons(serviceTypeInt) {
    return Container(
      width: double.maxFinite,
      alignment: Alignment.center,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: Theme.of(context).primaryColor),
      ),
      child: CupertinoSlidingSegmentedControl<int>(
        thumbColor: Theme.of(context).primaryColor,
        groupValue: selectedSegmentValue,
        backgroundColor: Colors.white,
        children: {
          0: buildDialogSegment("Audits", 0, selectedSegmentValue!),
          1: buildDialogSegment("Tasks", 1, selectedSegmentValue!),
        },
        onValueChanged: (value) {
          setState(() {
            selectedSegmentValue = value;
            radioValue = selectedSegmentValue!;
            print(selectedSegmentValue);
          });
        },
      ),
    );
  }

  _getSelectedValue(int? selectedValue) {
    switch (selectedValue) {
      case 0:
        return "ALL";
      case 1:
        return "AUDIT";
      case 2:
        return "TASK";
      default:
        return "ALL";
    }
  }

  Widget buildDialogSegment(String text, int index, int groupValue) {
    return Container(
      width: double.maxFinite,
      alignment: Alignment.center,
      child: Text(
        text,
        style: TextStyle(
            fontSize: 18,
            color: index == groupValue
                ? Colors.white
                : Theme.of(context).primaryColor),
      ),
    );
  }
}
