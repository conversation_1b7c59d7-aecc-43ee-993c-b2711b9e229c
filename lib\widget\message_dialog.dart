import 'package:alink/cubit/message/message_cubit.dart';
import 'package:alink/data/model/message_data.dart';
import 'package:alink/util/application_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

class MessageDialog extends StatefulWidget {
  final int requestId;

  const MessageDialog({Key? key, required this.requestId}) : super(key: key);

  @override
  _MessageDialogState createState() => _MessageDialogState();
}

class _MessageDialogState extends State<MessageDialog> {
  @override
  void initState() {
    super.initState();
    messageCubit.getMessageDataList(requestId: widget.requestId);
  }

  MessageCubit get messageCubit => BlocProvider.of<MessageCubit>(context);

  @override
  Widget build(BuildContext context) {
    return Dialog(
      insetPadding: const EdgeInsets.symmetric(horizontal: 10),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      elevation: 0,
      backgroundColor: Colors.transparent,
      child: Container(
        constraints: const BoxConstraints(maxWidth: 500),
        child: Stack(
          alignment: Alignment.topRight,
          children: <Widget>[
            Container(
              //width: MediaQuery.of(context).size.width,
              padding: const EdgeInsets.only(
                  left: 20,
                  top: 20,
                  //top: avatarRadius + padding,
                  right: 20,
                  bottom: 10),
              //margin: EdgeInsets.only(top: avatarRadius),
              decoration: BoxDecoration(
                  shape: BoxShape.rectangle,
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(15),
                  boxShadow: const [
                    BoxShadow(
                        color: Colors.black,
                        offset: Offset(0, 10),
                        blurRadius: 10),
                  ]),
              child: BlocBuilder<MessageCubit, MessageState>(
                builder: (context, state) {
                  if (state is FetchMessageDataError) {
                    return Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 15),
                          child: Row(
                            children: [
                              Center(child: Text(state.errorMessage)),
                            ],
                          ),
                        ),
                      ],
                    );
                  } else if (state is FetchedMessageData) {
                    List<MessageData> messageDataList = state.messageDataList;
                    return Column(
                      mainAxisSize: MainAxisSize.min,
                      children: <Widget>[
                        const SizedBox(
                          height: 5,
                        ),
                        Column(
                          children: [
                            Text(
                              messageDataList.length > 1
                                  ? '${AppLocalizations.of(context)!.messages} (${messageDataList.length})'
                                  : '${AppLocalizations.of(context)!.messages}',
                              style: const TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.w600,
                                  height: 1.2),
                            ),
                            const Divider(
                              thickness: 1.5,
                            ),
                          ],
                        ),
                        messageDataList.isNotEmpty
                            ? Flexible(
                                child: ListView.separated(
                                  physics: const BouncingScrollPhysics(),
                                  separatorBuilder: (context, index) =>
                                      const Divider(),
                                  shrinkWrap: true,
                                  itemCount: state.messageDataList.length,
                                  itemBuilder: (context, index) {
                                    MessageData messageData =
                                        state.messageDataList[index];
                                    return ListTile(
                                      contentPadding: EdgeInsets.zero,
                                      visualDensity: const VisualDensity(
                                          horizontal: 0, vertical: -4),
                                      //leading: Icon(widget.seatItemList[index].icon),
                                      title: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            '${messageData.message}',
                                            style: TextStyle(
                                              fontSize: 18,
                                              color: Theme.of(context)
                                                  .primaryColor,
                                            ),
                                          ),
                                          const SizedBox(
                                            height: 4,
                                          ),
                                        ],
                                      ),
                                      subtitle: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            '${AppLocalizations.of(context)!.createOn} : ${ApplicationUtil.getFormattedDateFromDate(createdDate: messageData.createdOn, format: 'MM-dd-yyyy, HH:mm')}',
                                            style: const TextStyle(
                                              fontSize: 14,
                                            ),
                                          ),
                                          const SizedBox(
                                            height: 2,
                                          ),
                                          Text(
                                            '${AppLocalizations.of(context)!.createdBy}: ${messageData.createdBy}',
                                            style:
                                                const TextStyle(fontSize: 14),
                                          ),
                                        ],
                                      ),
                                    );
                                  },
                                ),
                              )
                            : Padding(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 20),
                                child: Text(
                                  AppLocalizations.of(context)!.noMessages,
                                  style: const TextStyle(
                                      color: Colors.grey, fontSize: 18),
                                ),
                              ),
                        Column(
                          children: [
                            const Divider(
                              thickness: 1.5,
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                Align(
                                  alignment: Alignment.bottomRight,
                                  child: TextButton(
                                    onPressed: () {
                                      Navigator.pop(context);
                                    },
                                    child: Text(
                                      AppLocalizations.of(context)!.okay,
                                      style: const TextStyle(fontSize: 18),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ],
                    );
                  }
                  return _loadingContent();
                },
              ),
            ),
            IconButton(
              onPressed: () {
                Navigator.pop(context);
              },
              icon: const FaIcon(FontAwesomeIcons.times),
            ),
          ],
        ),
      ),
    );
  }

  Widget _loadingContent() => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(
            height: 10,
          ),
          Text(
            AppLocalizations.of(context)!.fetchingMessage,
            style: const TextStyle(fontSize: 22, fontWeight: FontWeight.w600),
          ),
          const Divider(),
          const SizedBox(
            height: 10,
          ),
          const CircularProgressIndicator(),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Align(
                alignment: Alignment.bottomRight,
                child: TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: Text(
                    AppLocalizations.of(context)!.okay,
                    style: const TextStyle(fontSize: 18),
                  ),
                ),
              ),
            ],
          ),
        ],
      );
}
