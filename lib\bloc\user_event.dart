part of 'user_bloc.dart';

@immutable
abstract class UserEvent extends Equatable {
  const UserEvent();
}

class <PERSON>tch<PERSON>ser extends UserEvent {
  final String email;
  final String password;
  final int customerId;
  const FetchUser(this.email, this.password, this.customerId);

  @override
  List<Object> get props => [email, password, customerId];
}

class CheckUserLoggedIn extends UserEvent {
  @override
  List<Object?> get props => [];
}

class GetLoggedInUser extends UserEvent {
  @override
  List<Object?> get props => [];
}

class LogoutUser extends UserEvent {
  @override
  List<Object?> get props => [];
}
