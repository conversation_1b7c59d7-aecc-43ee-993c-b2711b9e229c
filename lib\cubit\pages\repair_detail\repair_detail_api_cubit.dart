import 'package:alink/data/model/service_request_detail.dart';
import 'package:alink/data/repository/api_repository.dart';
import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';

part 'repair_detail_state.dart';

class RepairDetailApiCubit extends Cubit<RepairDetailState> {
  final ApiRepository apiRepository;
  int? serviceRequestBadgeCount = 0;

  RepairDetailApiCubit({required this.apiRepository})
      : super(RepairDetailInitial());

  Future<void> getRepairDetail(int id, bool isTimedService) async {
    print("Called by: getRepairDetail");
    emit(FetchingSingleRepairServiceDetail());
    try {
      var response = await apiRepository.fetchServiceRequestDetailById(
          id, isTimedService, null, true);
      if (response is List<ServiceRequestDetail>) {
        serviceRequestBadgeCount = response.length;
        emit(FetchedSingleRepairServiceDetail(response));
      } else {
        emit(SingleRepairServiceDetailError(errorMessage: response));
      }
    } catch (error) {
      emit(SingleRepairServiceDetailError(errorMessage: error.toString()));
    }
  }

  Future<void> getRepairDetailByRequestId(int id) async {
    print("Called by: getRepairDetailByRequestId");
    emit(FetchingSingleRepairServiceDetail());
    try {
      var response =
          await apiRepository.fetchServiceRequestDetailById(id, true, true);
      if (response is List<ServiceRequestDetail>) {
        serviceRequestBadgeCount = response.length;
        emit(FetchedSingleRepairServiceDetail(response));
      } else {
        emit(SingleRepairServiceDetailError(errorMessage: response));
      }
    } catch (error) {
      emit(SingleRepairServiceDetailError(errorMessage: error.toString()));
    }
  }

  Future<void> getRepairDetailListForEquipment(
      int? equipmentId, bool bothRequestType, bool isTimedService) async {
    print("Called by: getRepairDetailListForEquipment");
    emit(FetchingRepairsListForEquipment());
    try {
      var response = await apiRepository.fetchRepairListByEquipmentId(
          equipmentId!, bothRequestType, isTimedService);
      if (response is List<ServiceRequestDetail>) {
        emit(FetchedRepairsListForEquipment(response));
      } else {
        emit(FetchRepairsListError(errorMessage: response));
      }
    } catch (error) {
      emit(FetchRepairsListError(errorMessage: error.toString()));
    }
  }

  Future<void> resetRepairDataToInitial() async {
    emit(RepairDetailInitial());
  }

  Future<void> resetRepairData() async {
    emit(RepairDetailInitial());
  }

  Future<void> getSingleRepairDetailByEquipmentId(
      {int? equipmentId,
      required bool bothRequestType,
      required bool isTimedService,
      bool? isFromMap}) async {
    print("Called by: getSingleRepairDetailByEquipmentId");
    isFromMap = isFromMap ?? false;
    emit(FetchingSingleRepairsDetailByEquipmentId());
    try {
      var response = await apiRepository.fetchRepairListByEquipmentId(
          equipmentId!, bothRequestType, isTimedService);
      if (response is List<ServiceRequestDetail>) {
        isFromMap ? emit(FetchedSingleRepairsDetailByEquipmentIdForMap(response)) : emit(FetchedSingleRepairsDetailByEquipmentId(response));
      } else {
        emit(FetchSingleRepairsDetailEquipmentIdError(errorMessage: response));
      }
    } catch (error) {
      emit(FetchSingleRepairsDetailEquipmentIdError(
          errorMessage: error.toString()));
    }
  }
}
