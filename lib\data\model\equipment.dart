class Equipment {
  String? tag;
  String? name;
  String? bomId;
  int? equipmentId;
  bool isBarcodeScanned;

  Equipment(
      {this.tag,
      this.name,
      this.bomId,
      this.equipmentId,
      this.isBarcodeScanned = false});

  Map<String, dynamic> toMap() {
    return {
      'TAG': tag,
      'NAME': name,
      'BOM_ID': bomId,
      'EQUIPMENT_ID': equipmentId,
    };
  }

  factory Equipment.fromMap(Map<String, dynamic> map) {
    return Equipment(
      tag: map['TAG'] as String,
      name: map['NAME'] as String,
      bomId: map['BOM_ID'] as String,
      equipmentId: map['EQUIPMENT_ID'] as int,
    );
  }
}
