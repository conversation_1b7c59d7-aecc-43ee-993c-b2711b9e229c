import 'dart:convert';
import 'dart:isolate';

import 'package:alink/database/database.dart';
import 'package:alink/database/dbconfig/shared.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;

import '../logger/logger.dart';

class HttpIsolateTask {
  Logger logger = Logger();
  sendTaskThroughIsolate() async {
    Map<String, dynamic> map = getMapData();
    Logger.i('STARTING HTTP TASK ISOLATE');
    if (!kIsWeb) {
      Isolate _isolate = await Isolate.spawn(checkTaskAndSendToServer, map);
      Logger.i('ISOLATE HTTP TASK ENDED');
      return await httpRequestRespond() as IsolateTaskResponse;
    } else {
      return checkTaskAndSendToServer(map);
    }
  }
}

Future<dynamic> checkTaskAndSendToServer(Map<String, dynamic> map) async {
  List<Task> taskList = [];

  String token = map['token'];
  String BASEURL = map['apiUrlPath'];
  Database db = await getWorkerDatabase(map);
  IsolateTaskResponse isolateTaskResponse = IsolateTaskResponse();

  taskList = await db.taskDao.getAllTasks();

  if (taskList != null && taskList is List<Task> && taskList.isNotEmpty) {
    Map<String, String> requestHeaders = {
      'Content-type': 'application/json',
      'Accept': 'application/json',
      'Authorization': token
    };

    for (Task taskData in taskList) {
      try {
        dynamic body = {
          'EQUIPMENT_ID': taskData.EQUIPMENT_ID,
          'TASK_TYPE': taskData.TASK_TYPE,
        };
        isolateTaskResponse.apiUrl = "$BASEURL/eventtask";

        final response = await http.post(Uri.parse("$BASEURL/eventtask"),
            headers: requestHeaders, body: json.encode(body));

        isolateTaskResponse.responseCode = response.statusCode;
        isolateTaskResponse.responseLength = response.contentLength ?? 0;
        isolateTaskResponse.conversationId =
            response.headers['conversation-id'].toString();
        //Logger.i('response.statusCode');
        //Logger.i(response.statusCode.toString());

        if (response.statusCode == 204 ||
            response.statusCode == 202 ||
            response.statusCode == 200) {
          isolateTaskResponse.token = response.headers['authorization'];
          //Logger.i('http 204');
          await db.taskDao.deleteTask(taskData.TASK_ID);
          isolateTaskResponse.count++;
          //Logger.i('deleted recorded');

          isolateTaskResponse.count++;
          //Logger.i('deleted recorded');
        } else if (response.statusCode == 400) {
          //  await HttpIsolateTask().deleteTask(taskData,map);
          isolateTaskResponse.error = json.decode(response.body);
          isolateTaskResponse.hasError = true;
        } else if (response.statusCode == 401) {
          isolateTaskResponse.hasError = true;
          isolateTaskResponse.pendingRequest++;
          isolateTaskResponse.error = "Invalid Authentication Credentials";
        } else {
          isolateTaskResponse.hasError = true;
          isolateTaskResponse.error = response.body;
          isolateTaskResponse.pendingRequest++;
          //Logger.i('======Respose Body======\n' + response.body);
          //Logger.i('======Status Code======\n' + response.statusCode.toString());
          //   await HttpIsolateTask().deleteTask(taskData,map);
        }
      } catch (e) {
        //Logger.e(e.toString());
        //send pending request
        isolateTaskResponse.hasError = true;
        break;
      }
    }
    //mainSendPort.send(isolateServiceResponse);
/*

    if (!kIsWeb) {
      getReturnTaskResponse(map, isolateTaskResponse);
    } else {
      return getReturnTaskResponse(map, isolateTaskResponse);

    }
  } else {
    //mainSendPort.send(isolateServiceResponse);
    if (!kIsWeb) {
      getReturnTaskResponse(map, isolateTaskResponse);

    } else {
      getReturnTaskResponse(map, isolateTaskResponse);

    }
*/
  }
}

class IsolateTaskResponse {
  String? token;
  String error = '';
  bool hasError = false;
  int count = 0;
  int pendingRequest = 0;
  String apiUrl = '';
  int responseCode = 0;
  int responseLength = 0;
  String conversationId = "";
}
