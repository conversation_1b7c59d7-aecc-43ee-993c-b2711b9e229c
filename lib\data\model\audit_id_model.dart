class AuditRefId {
  final int? _index;
  final int? _refAuditId;
  final String? _auditName;
  final String? _serviceType;
  final String? _auditStatus;

  AuditRefId(
      {required int index,
      required int refAuditId,
      required String auditName,
      required String serviceType,
      required String auditStatus})
      : _index = index,
        _refAuditId = refAuditId,
        _auditName = auditName,
        _auditStatus = auditStatus,
        _serviceType = serviceType;
  int? get refAuditId => _refAuditId;

  int? get index => _index;

  String? get auditName => _auditName;

  String? get serviceType => _serviceType;
  String? get auditStatus => _auditStatus;
}
