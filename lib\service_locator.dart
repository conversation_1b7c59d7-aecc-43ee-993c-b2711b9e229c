import 'package:alink/data/model/customization.dart';
import 'package:alink/data/model/terminal.dart';
import 'package:alink/util/enums/app_enum.dart';
import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'package:path_provider/path_provider.dart';

import 'package:shared_preferences/shared_preferences.dart';

import 'data/model/barcode_response.dart';
import 'logger/logger.dart';

GetIt getIt = GetIt.instance;
Future setupLocator() async {
  if (!kIsWeb) {
    var directory = await getApplicationDocumentsDirectory();
    Logger.initialize(directory.path);
    Logger.setLogLevel(LogLevel.debug);
  }
  getIt.registerSingletonAsync<SharedPreferences>(
      () => SharedPreferences.getInstance());
  getIt.registerSingleton<Customization>(Customization());
  getIt.registerSingleton<Terminal>(Terminal());
  getIt.registerSingleton<BarcodeResponse>(BarcodeResponse());
  //getIt.registerSingletonAsync<Logger>(() => Logger());
  getIt.registerSingleton<ServiceType>(
      ServiceType(type: ServiceRequestType.ADD));

/*  locator.registerSingletonAsync<bool>(() async {
    final localAuth = LocalAuthentication();
    await localAuth.canCheckBiometrics();
    return localAuth;
  });*/
}
