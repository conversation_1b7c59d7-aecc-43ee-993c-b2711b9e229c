import 'package:drift/drift.dart';

class PartUsed extends Table {
  @override
  String get tableName => 'PART_USED';
  IntColumn get id => integer().autoIncrement().named('ID')();
  IntColumn get requestId => integer().named('REQUEST_ID')();
  IntColumn get partId => integer().nullable().named('PART_ID')();
  TextColumn get partDescription =>
      text().nullable().named('PART_DESCRIPTION')();
  IntColumn get quantity => integer().nullable().named('QUANTITY')();
  TextColumn get uom => text().nullable().named('UOM')();
}
