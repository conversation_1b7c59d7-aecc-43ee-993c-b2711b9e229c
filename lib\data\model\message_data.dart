class MessageData {
  final int messageId;
  final String message;
  final String createdBy;
  final String createdOn;
  final int requestId;

  const MessageData({
    required this.messageId,
    required this.message,
    required this.createdBy,
    required this.createdOn,
    required this.requestId,
  });

  Map<String, dynamic> toMap() {
    return {
      'MESSAGE_ID': this.messageId,
      'MESSAGE': this.message,
      'CREATED_BY': this.createdBy,
      'CREATED_ON': this.createdOn,
      'REQUEST_ID': this.requestId,
    };
  }

  factory MessageData.fromMap(Map<String, dynamic> map) {
    return MessageData(
      messageId: map['MESSAGE_ID'] as int,
      message: map['MESSAGE'] as String,
      createdBy: map['CREATED_BY'] as String,
      createdOn: map['CREATED_ON'] as String,
      requestId: map['REQUEST_ID'] as int,
    );
  }
}
