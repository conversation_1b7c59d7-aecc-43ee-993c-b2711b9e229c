<project name="AFS_ALink" default="publish" basedir="." xmlns:ivy="antlib:org.apache.ivy.ant">
	<property name="dist.dir" value="${basedir}/dist"/>
	
	<target name="publish">
		<!-- =================================================================== -->
		<!-- Set Artifactory target depending on release build or nightly        -->
		<!-- =================================================================== -->
		<property environment="env"/>
		<property name="pub.repo" value="customapps-release"/>
		<property name="pub.repodir" value="custom-apps-release"/>

		<!-- Release string to be written -->
		<loadfile property="release.str"
			srcFile="BuildNo.txt" failonerror="true">
		</loadfile>

		<echo message="Publishing to artifactory repository: ${pub.repodir}"/>
		<ivy:resolve file="ivy-android.xml" revision="${release.str}"/>
		<ivy:publish resolver="artifactory-publish" revision="${release.str}" update="true" overwrite="true">
			<artifacts pattern="${dist.dir}/[artifact].[ext]" />
		</ivy:publish>
	</target>
</project>