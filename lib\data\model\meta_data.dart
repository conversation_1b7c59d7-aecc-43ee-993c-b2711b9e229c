import 'dart:convert';

class Metadata {
  int? metadataId;
  String? objectReference;
  int? sectionName;
  String? fieldOrder;
  String? fieldName;
  String? fieldControl;
  String? fieldChoiceType;
  Metadata(
      {this.metadataId,
      this.objectReference,
      this.sectionName,
      this.fieldOrder,
      this.fieldName,
      this.fieldControl,
      this.fieldChoiceType});

  factory Metadata.fromJson(Map<dynamic, dynamic> map) {
    return Metadata(
        metadataId: map['metadataId'],
        objectReference: map['objectReference'],
        sectionName: map['sectionName'],
        fieldOrder: map['fieldOrder'],
        fieldName: map['fieldName'],
        fieldControl: map['fieldControl'],
        fieldChoiceType: map['fieldChoiceType']);
  }
  Map<dynamic, dynamic> toJson() {
    return {
      "metadataId": metadataId,
      "objectReference": objectReference,
      "sectionName": sectionName,
      "fieldOrder": fieldOrder,
      "fieldName": fieldName,
      "fieldControl": fieldControl,
      "fieldChoiceType": fieldChoiceType
    };
  }

  @override
  String toString() {
    return 'Metadata{metadataId: $metadataId, objectReference: $objectReference, sectionName: $sectionName, fieldOrder: $fieldOrder}';
  }

  List<Metadata> metadataFromJson(String jsonData) {
    final data = json.decode(jsonData);
    return List<Metadata>.from(data.map((item) => Metadata.fromJson(item)));
  }

  String metadataToJson(Metadata data) {
    final jsonData = data.toJson();
    return json.encode(jsonData);
  }
}
