import 'package:alink/pages/airport/audit/audit_barcode/mobile.dart'
    if (dart.library.html) 'package:alink/pages/airport/audit/audit_barcode/web.dart';
import 'package:alink/util/application_util.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../../../logger/logger.dart';

String? _label;
Function(String result)? _resultCallback;
late BuildContext rootContext;

/// AppBarcodeScannerWidget
class AuditBarcodeScannerWidget extends StatefulWidget {
  AuditBarcodeScannerWidget.defaultStyle({
    Key? key,
    required Function(String result) resultCallback,
    String label = 'Single number',
  }) : super(key: key) {
    _resultCallback = resultCallback;
    _label = label;
  }

  @override
  _AppBarcodeState createState() => _AppBarcodeState();
}

class _AppBarcodeState extends State<AuditBarcodeScannerWidget> {
  static const String className = '_AppBarcodeState';

  @override
  Widget build(BuildContext context) {
    if (kDebugMode) {
      Logger.i("Class Name: " + className);
    }
    rootContext = context;
    return _BarcodePermissionWidget();
  }
}

class _BarcodePermissionWidget extends StatefulWidget {
  @override
  State<StatefulWidget> createState() {
    return _BarcodePermissionWidgetState();
  }
}

class _BarcodePermissionWidgetState extends State<_BarcodePermissionWidget> {
  bool _isGranted = false;

  @override
  void initState() {
    super.initState();
  }

  void _requestMobilePermission() async {
    if (await Permission.camera.request().isGranted) {
      setState(() {
        _isGranted = true;
      });
    } else {
      ApplicationUtil.showWarningAlertDialog(context,
          title: "Permission not granted",
          desc: AppLocalizations.of(context)!.setCameraPermission,
          positiveLabel: AppLocalizations.of(context)!.okay,
          onPositiveClickListener: () {
        Navigator.of(context).pop();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    TargetPlatform platform = Theme.of(context).platform;
    if (!kIsWeb) {
      if (platform == TargetPlatform.android ||
          platform == TargetPlatform.iOS) {
        _requestMobilePermission();
      } else {
        setState(() {
          _isGranted = true;
        });
      }
    } else {
      setState(() {
        _isGranted = true;
      });
    }

    return Column(
      children: <Widget>[
        Expanded(
          child: _isGranted
              ? _getBarcodeScannerWidget()
              : Center(
                  child: OutlinedButton(
                    onPressed: () {
                      _requestMobilePermission();
                    },
                    child:
                        Text(AppLocalizations.of(context)!.requestPermission),
                  ),
                ),
        ),
      ],
    );
  }

  _getBarcodeScannerWidget() {
    return AuditBarcodeScanner();
  }
}
