part of 'filter_cubit.dart';

@immutable
abstract class FilterState {}

class <PERSON>lterInitial extends FilterState {}

class FetchingFilterData extends FilterState {}

class FetchedFilterData extends FilterState {
  final FilterData filterList;
  FetchedFilterData(this.filterList);
}

class FetchFilterDataError extends FilterState {
  final String errorMessage;
  FetchFilterDataError({required this.errorMessage});
}
