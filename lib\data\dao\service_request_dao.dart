import 'package:alink/data/model/service_request.dart';
import 'package:alink/database/database.dart';

import 'package:drift/native.dart'
    if (dart.library.html) 'package:alink/util/moor_web_exception.dart';

import 'package:drift/drift.dart';
part 'service_request_dao.g.dart';

@DriftAccessor(
  tables: [ServiceRequest],
)
class ServiceRequestDao extends DatabaseAccessor<Database>
    with _$ServiceRequestDaoMixin {
  final Database db;

  ServiceRequestDao(this.db) : super(db);

  Future<dynamic> insertServiceRequest(
      Insertable<ServiceRequestData> serviceRequestData) async {
    try {
      int id = await into(serviceRequest).insert(serviceRequestData);

      var insertedData = (select(serviceRequest)
            ..where((t) => t.requestId.equals(id)))
          .getSingle();
      return insertedData;
    } on SqliteException catch (error) {
      //Logger.e(error.message);
      return error.message;
    }
  }

  Future updateServiceRequest(
          Insertable<ServiceRequestData> serviceRequestData) =>
      update(serviceRequest).replace(serviceRequestData);

  Future deleteServiceRequest(
          Insertable<ServiceRequestData> serviceRequestData) =>
      delete(serviceRequest).delete(serviceRequestData);

  Future<dynamic> getAllServiceRequest() async {
    try {
      return Future.delayed(
        Duration(seconds: 1),
        () {
          return select(serviceRequest).get();
        },
      );
    } on SqliteException catch (error) {
      //Logger.e(error.message);
      return error.message;
    }
  }

  getPendingRequestFromDb() {
    return select(serviceRequest).get();
  }
}
