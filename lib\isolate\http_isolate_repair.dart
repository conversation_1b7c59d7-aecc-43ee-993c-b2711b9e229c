import 'dart:convert';
import 'dart:isolate';

import 'package:alink/database/database.dart';
import 'package:alink/database/dbconfig/shared.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;

import '../logger/logger.dart';

class HttpIsolateRepairService {
  Logger logger = Logger();
  sendRepairThroughIsolate() async {
    //ReceivePort receivePort = ReceivePort();
    //String token = await _getBasicAuth();
    Map<String, dynamic> map = getMapData();
    Logger.i('STARTING HTTP REPAIR ISOLATE');
    if (!kIsWeb) {
      Isolate _isolate = await Isolate.spawn(checkRepairAndSendToServer, map);
      Logger.i('ISOLATE HTTP REPAIR ENDED');
      return await httpRequestRespond() as IsolateRepairResponse;
    } else {
      return checkRepairAndSendToServer(map);
    }
  }
}

Future<dynamic> checkRepairAndSendToServer(Map<String, dynamic> map) async {
  //Logger.initialize(map['logPath'].toString() + 'logger.txt');
  //Logger.i('checkRepairRequestAndSendToServer');
  /*SendPort mainSendPort = message['sendPort'];
  SendPort moorSendPort = message['moorSendPort'];*/
  String token = map['token'];
  String BASEURL = map['apiUrlPath'];
  Database db = await getWorkerDatabase(map);
  IsolateRepairResponse isolateRepairServiceResponse = IsolateRepairResponse();
  //
  dynamic pendingRequest = await db.repairDao.getPendingRepairFromDb();
  if (pendingRequest != null &&
      pendingRequest is List<RepairData> &&
      pendingRequest.isNotEmpty) {
    Map<String, String> requestHeaders = {
      'Content-type': 'application/json',
      'Accept': 'application/json',
      'Authorization': token
    };

    for (RepairData repairData in pendingRequest) {
      try {
        dynamic body = {
          'CURRENT_LOCATION': repairData.currentLocationId,
          'NEW_LOCATION': repairData.newLocationId,
          'EXTN':
              repairData.extn == null ? null : json.decode(repairData.extn!),
          'PARTS': json.decode(repairData.parts!),
          'DOCUMENT': json.decode(repairData.repairDocument!),
        };
        if (repairData.customerId != null) {
          body['CUSTOMER_ID'] = repairData.customerId;
        }
        if (repairData.requestId == null && repairData.refId == null) {
          body['SERVICE_REQUEST_DOCUMENT'] =
              json.decode(repairData.serviceRequestDocument!);
        } else {
          if (repairData.requestId != null) {
            body['REQUEST_ID'] = repairData.requestId;
          } else if (repairData.refId != null) {
            body['REF_ID'] = repairData.refId;
          }
        }
        if (repairData.latitude != null) {
          body['LATITUDE'] = repairData.latitude;
        }
        if (repairData.longitude != null) {
          body['LONGITUDE'] = repairData.longitude;
        }
        isolateRepairServiceResponse.apiUrl = "$BASEURL/repair";
        //Logger.i('json.encode(body)');
        //Logger.i(json.encode(body));
        final response = await http.post(Uri.parse("$BASEURL/repair"),
            headers: requestHeaders, body: json.encode(body));
        //Logger.i("${ApplicationUtil.getFormattedCurrentDateAndTime()}Uri.parse($BASEURL/repair),headers: $requestHeaders, body: ${json.encode(body)} Conversation id: ${response.headers['conversation-id'].toString()}");
        //Logger.i('http respond');
        isolateRepairServiceResponse.responseCode = response.statusCode;
        isolateRepairServiceResponse.responseLength =
            response.contentLength ?? 0;
        isolateRepairServiceResponse.conversationId =
            response.headers['conversation-id'].toString();
        //Logger.i('response.statusCode');
        //Logger.i(response.statusCode.toString());

        if (response.statusCode == 204 ||
            response.statusCode == 202 ||
            response.statusCode == 200) {
          isolateRepairServiceResponse.token =
              response.headers['authorization'];
          //Logger.i('http 204');
          await db.repairDao.deleteServiceRequest(repairData);
          isolateRepairServiceResponse.count++;
          //Logger.i('deleted recorded');
        } else if (response.statusCode == 400) {
          await db.repairDao.deleteServiceRequest(repairData);
          isolateRepairServiceResponse.error = json.decode(response.body);
          isolateRepairServiceResponse.hasError = true;
        } else if (response.statusCode == 401) {
          isolateRepairServiceResponse.hasError = true;
          isolateRepairServiceResponse.pendingRequest++;
          isolateRepairServiceResponse.error =
              "Invalid Authentication Credentials";
        } else {
          isolateRepairServiceResponse.hasError = true;
          isolateRepairServiceResponse.error = response.body;
          isolateRepairServiceResponse.pendingRequest++;
          //Logger.i('======Respose Body======\n' + response.body);
          //Logger.i('======Status Code======\n' + response.statusCode.toString());
          await db.repairDao.deleteServiceRequest(repairData);
        }
      } catch (e) {
        //Logger.e(e.toString());
        //send pending request
        isolateRepairServiceResponse.hasError = true;
        isolateRepairServiceResponse.pendingRequest = pendingRequest.length;
        break;
      }
    }
    //mainSendPort.send(isolateServiceResponse);

    if (!kIsWeb) {
      getReturnRepairResponse(map, isolateRepairServiceResponse);
    } else {
      return getReturnRepairResponse(map, isolateRepairServiceResponse);
    }
  } else {
    //mainSendPort.send(isolateServiceResponse);
    if (!kIsWeb) {
      getReturnRepairResponse(map, isolateRepairServiceResponse);
    } else {
      return getReturnRepairResponse(map, isolateRepairServiceResponse);
    }
  }
}

class IsolateRepairResponse {
  String? token;
  String error = '';
  bool hasError = false;
  int count = 0;
  int pendingRequest = 0;
  String apiUrl = '';
  int responseCode = 0;
  int responseLength = 0;
  String conversationId = "";
}
