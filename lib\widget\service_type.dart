import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import '../data/model/audit_schedule.dart';
import '../util/app_color.dart';

class AuditDescriptionDropdownWidget extends StatefulWidget {
  final List<AuditSchedules> auditDescriptionList;
  String? defaultAuditDescriptionType;
  final Function()? onReset;
  final Function(String selectedServiceName) selectedAuditDescription;
  final String description;
  final bool showRequiredSymbol;
  final bool disableDropDown;

  AuditDescriptionDropdownWidget({
    Key? key,
    required this.auditDescriptionList,
    this.defaultAuditDescriptionType,
    required this.selectedAuditDescription,
    this.onReset()?,
    this.showRequiredSymbol = false,
    this.description = '',
    this.disableDropDown = false,
  }) : super(key: key);

  @override
  State<AuditDescriptionDropdownWidget> createState() =>
      _AuditDescriptionDropdownWidgetState();
}

class _AuditDescriptionDropdownWidgetState
    extends State<AuditDescriptionDropdownWidget> {
  List<String> get dropdownItems {
    List<String> menuItems = [];
    menuItems = widget.auditDescriptionList
        .map((auditSchedule) => auditSchedule.description.toString())
        .toList();
    return menuItems;
  }

  String selectedValue = "";

  @override
  void initState() {
    print("Selected Value: $selectedValue");
    dropdownItems;
    if (widget.defaultAuditDescriptionType == null ||
        widget.defaultAuditDescriptionType == "") {
      selectedValue = "";
    } else {
      selectedValue = widget.defaultAuditDescriptionType!;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 5),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            decoration: BoxDecoration(
              color: !widget.disableDropDown
                  ? Theme.of(context).primaryColor
                  : AppColor.greyBorderColor,
              borderRadius: BorderRadius.circular(10),
            ),
            child: DropdownButton(
              underline: Container(),
              isExpanded: true,
              focusColor: Colors.white,
              value: widget.defaultAuditDescriptionType,
              items: widget.auditDescriptionList
                  .map(
                    (auditSchedule) => DropdownMenuItem(
                      value: auditSchedule.description.toString(),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          auditSchedule.serviceType != "ALL"
                              ? FaIcon(
                                  auditSchedule.serviceType == 'TASK'
                                      ? FontAwesomeIcons.clipboardCheck
                                      : FontAwesomeIcons.userSecret,
                                  color: auditSchedule.serviceType == 'TASK'
                                      ? AppColor.primarySwatchColor
                                      : AppColor.redColor,
                                  size: 24,
                                )
                              : const SizedBox(
                                  width: 20,
                                ),
                          const SizedBox(width: 10),
                          Expanded(
                            child: Text(
                              auditSchedule.description.toString(),
                              overflow: TextOverflow.ellipsis,
                              style: TextStyle(
                                  color: Theme.of(context).primaryColor),
                            ),
                          ),
                        ],
                      ),
                    ),
                  )
                  .toList(),
              icon: const Icon(
                Icons.arrow_drop_down,
                color: Colors.white,
                size: 35, // Add this
              ),
              selectedItemBuilder: (BuildContext context) {
                return widget.auditDescriptionList
                    .map(
                      (auditSchedule) => DropdownMenuItem(
                        value: auditSchedule.description.toString(),
                        child: Container(
                          margin: const EdgeInsets.only(left: 20),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              auditSchedule.serviceType != "ALL"
                                  ? FaIcon(
                                      auditSchedule.serviceType == 'TASK'
                                          ? FontAwesomeIcons.clipboardCheck
                                          : FontAwesomeIcons.userSecret,
                                      color: Colors.white,
                                      size: 24,
                                    )
                                  : const SizedBox(),
                              const SizedBox(width: 10),
                              Expanded(
                                child: Text(
                                  auditSchedule.description.toString(),
                                  overflow: TextOverflow.ellipsis,
                                  style: const TextStyle(color: Colors.white),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    )
                    .toList();
              },
              onChanged: (String? newValue) {
                setState(() {
                  selectedValue = newValue!;
                  widget.selectedAuditDescription(newValue);
                });
              },
            ),
          ),
        ],
      ),
    );
  }
}
