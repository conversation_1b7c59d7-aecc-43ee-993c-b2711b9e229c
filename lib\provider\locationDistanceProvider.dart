import 'dart:math';
import 'package:flutter/foundation.dart';

class DistanceCalculator with ChangeNotifier {
  double? _distance;

  double? get distance => _distance;

  calculateDistance(
      double srcLat, double srcLon, double destLat, double destLon) {
    _distance = _computeDistance(srcLat, srcLon, destLat, destLon);
    notifyListeners();
  }

  double _computeDistance(double lat1, double lon1, double lat2, double lon2) {
    const double earthRadius = 6371000;

    double dLat = _degreesToRadians(lat2 - lat1);
    double dLon = _degreesToRadians(lon2 - lon1);

    double a = sin(dLat / 2) * sin(dLat / 2) +
        cos(_degreesToRadians(lat1)) *
            cos(_degreesToRadians(lat2)) *
            sin(dLon / 2) *
            sin(dLon / 2);
    double c = 2 * atan2(sqrt(a), sqrt(1 - a));
    return earthRadius * c;
  }

  double _degreesToRadians(double degrees) {
    return degrees * pi / 180;
  }

  setDistance(double distance) {
    _distance = distance * 3.280;
  }
}
