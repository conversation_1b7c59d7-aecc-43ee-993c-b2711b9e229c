import 'package:alink/data/model/message_data.dart';
import 'package:alink/data/repository/api_repository.dart';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';

part 'message_state.dart';

class MessageCubit extends Cubit<MessageState> {
  final ApiRepository apiRepository;
  MessageCubit({required this.apiRepository}) : super(MessageInitial());

  getMessageDataList({required int requestId}) async {
    emit(FetchingMessageData());
    try {
      var response = await apiRepository.getMessageDataByRequestId(requestId);
      if (response is List<MessageData>) {
        emit(FetchedMessageData(messageDataList: response));
      } else {
        emit(FetchMessageDataError(errorMessage: response));
      }
    } catch (error) {
      emit(FetchMessageDataError(errorMessage: error.toString()));
    }
  }
}
