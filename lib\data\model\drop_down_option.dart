class DropdownOptionData {
  List<DropdownOptionData> dropDownData = [];
  String? typeName;
  DropDownChoice? dropdownChoice;

  DropdownOptionData({
    this.typeName,
    this.dropdownChoice,
  });
}

class DropDownChoice {
  String? choiceName;
  String? choiceValue;

  DropDownChoice({
    this.choiceName,
    this.choiceValue,
  });

  Map<String, dynamic> toMap() {
    return {
      'CHOICE_NAME': choiceName,
      'CHOICE_VALUE': choiceValue,
    };
  }

  factory DropDownChoice.fromMap(Map<String, dynamic> map) {
    return DropDownChoice(
      choiceName: map['CHOICE_NAME'] as String,
      choiceValue: map['CHOICE_VALUE'] as String,
    );
  }
}
