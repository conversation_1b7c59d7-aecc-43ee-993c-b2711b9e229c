class Customer {
  String customerName;
  int customerId;

  Map<String, dynamic> toMap() {
    return {
      'CUSTOMER_NAME': customerName,
      'CUSTOMER_ID': customerId,
    };
  }

  factory Customer.fromJson(Map<String, dynamic> map) {
    return Customer(
      customerName: map['CUSTOMER_NAME'] as String,
      customerId: map['CUSTOMER_ID'] as int,
    );
  }

  Customer({
    required this.customerName,
    required this.customerId,
  });
}
