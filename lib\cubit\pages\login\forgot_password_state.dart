part of 'forgot_password_cubit.dart';

@immutable
abstract class ForgotPasswordState {}

class ForgotPasswordInitial extends ForgotPasswordState {}

class SendingResetPasswordMail extends ForgotPasswordState {}

class SentResetPasswordMail extends ForgotPasswordState {
  final String msg;
  SentResetPasswordMail({required this.msg});
}

class ResetPasswordError extends ForgotPasswordState {
  final String errorMessage;
  ResetPasswordError({required this.errorMessage});
}
