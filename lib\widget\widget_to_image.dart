import 'package:flutter/material.dart';

class WidgetToImage extends StatefulWidget {
  static const routeName = "widget-to-image";

  final Function(GlobalKey key) builder;

  const WidgetToImage({Key? key, required this.builder}) : super(key: key);

  @override
  _WidgetToImageState createState() => _WidgetToImageState();
}

class _WidgetToImageState extends State<WidgetToImage> {
  final globalKey = GlobalKey();
  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      key: globalKey,
      child: widget.builder(globalKey),
    );
  }
}
