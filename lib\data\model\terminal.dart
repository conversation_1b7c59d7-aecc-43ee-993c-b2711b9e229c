class Terminal {
  String? name;
  String? locationId;
  List<Gate>? gate;
  List<Terminal>? terminalLists = [];
  Terminal({this.name, this.locationId, this.gate});

  Terminal.fromJson(Map<String, dynamic> json) {
    name = json['NAME'];
    locationId = json['LOCATION_ID'];
    if (json['GATE'] != null) {
      gate = [];
      json['GATE'].forEach((v) {
        gate!.add(Gate.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['NAME'] = name;
    data['LOCATION_ID'] = locationId;
    if (gate != null) {
      data['GATE'] = gate!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Gate {
  String? name;
  String? locationId;

  Gate({this.name, this.locationId});

  Gate.fromJson(Map<String, dynamic> json) {
    name = json['NAME'];
    locationId = json['LOCATION_ID'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['NAME'] = name;
    data['LOCATION_ID'] = locationId;
    return data;
  }
}
