import 'package:alink/bloc/api/api_bloc.dart';
import 'package:alink/bloc/audit/audit_equipment_cubit.dart';
import 'package:alink/bloc/repair_list_api/repair_list_api_bloc.dart';
import 'package:alink/bloc/service/service_request_bloc.dart';
import 'package:alink/cubit/lopa/selected_equipment_cubit.dart';
import 'package:alink/cubit/pages/audit/audit_list_cubit.dart';
import 'package:alink/data/model/audit_response.dart';
import 'package:alink/data/model/customization.dart';
import 'package:alink/data/model/repair_service_request.dart';
import 'package:alink/pages/airline/audit/lopa_audit_dialog.dart';
import 'package:alink/pages/airline/audit/widgets/create_adhoc_audit_dialog.dart';
import 'package:alink/pages/airline/model/lopa_audit_option.dart';
import 'package:alink/pages/airline/model/lopa_model.dart';
import 'package:alink/service_locator.dart';
import 'package:alink/util/app_color.dart';
import 'package:alink/util/application_util.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:location/location.dart' as gps;
import 'package:shared_preferences/shared_preferences.dart';

import '../../../data/model/audit_location.dart';
import '../../../logger/logger.dart';

class LopaAuditPage extends StatefulWidget {
  static const routeName = 'digital-lopa-audit';

  const LopaAuditPage({Key? key}) : super(key: key);

  @override
  _LopaAuditPageState createState() => _LopaAuditPageState();
}

class _LopaAuditPageState extends State<LopaAuditPage> {
  bool isMethodCalled = false;
  gps.LocationData? locationData;
  String seatTag = ApplicationUtil.getEquipmentType();

  List<Audit> auditList = [];
  Audit? selectedAudit;
  List<AuditEquipment> auditEquipmentList = [];
  List<AuditEquipment> auditEquipmentListFromDb = [];
  late TextEditingController _auditCommentController;
  List<AuditScoreOption> auditScoreOptionList = [];
  int? selectedSegmentValue = 0;
  int? radioValue = InMemoryAudiData.selectedSegmentValueInMemory;
  bool refresh = false;

  ///EQUIPMENT HAVING SR
  List<RepairServiceRequest> equipmentHavingSRList = [];

  @override
  void initState() {
    _initLocation();
    _initAuditOption();
    _auditCommentController = TextEditingController();
    if (InMemoryAudiData.selectedSegmentValueInMemory != null) {
      selectedSegmentValue = InMemoryAudiData.selectedSegmentValueInMemory!;
    } else {
      selectedSegmentValue = 0;
    }
    auditCubit.getAuditListData(
        0, _getLocationId(), null, _getSelectedValue(radioValue));
    repairListBloc.add(FetchLopaRepairServiceRequestList(
        query:
            '&location=${_getLocationId()}&requestType=NOTIFICATION,SERVICE_REQUEST'));
    super.initState();
  }

  AuditListCubit get auditCubit => BlocProvider.of<AuditListCubit>(context);

  RepairListApiBloc get repairListBloc =>
      BlocProvider.of<RepairListApiBloc>(context);

  ApiBloc get apiBloc => BlocProvider.of<ApiBloc>(context);

  ServiceRequestBloc get serviceRequestBloc =>
      BlocProvider.of<ServiceRequestBloc>(context);
  int? customerId = getIt<SharedPreferences>().getInt('customerId');

  @override
  void dispose() {
    _auditCommentController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    Logger.i(
        "Class Name: ${this.runtimeType.toString()} time: ${DateTime.now()}");
    return Scaffold(
      body: SafeArea(
        child: Center(
          child: Container(
            constraints: const BoxConstraints(maxWidth: 500),
            child: _getBody(),
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.pop(context);
        },
        child: Container(
            margin: const EdgeInsets.only(right: 5),
            child: const FaIcon(FontAwesomeIcons.chevronLeft)),
      ),
    );
  }

  _getBody() {
    if (SelectedFleetCubit.seatCategoryList.isNotEmpty) {
      return Stack(
        alignment: Alignment.topCenter,
        children: [
          ConstrainedBox(
            constraints:
                BoxConstraints(maxHeight: MediaQuery.of(context).size.height),
            child: SafeArea(
              child: Scrollbar(
                child: SingleChildScrollView(
                  physics: const BouncingScrollPhysics(),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(
                        height: MediaQuery.of(context).padding.top + 10,
                      ),
                      _getFleetAndSubFleetName(),
                      BlocConsumer<AuditListCubit, AuditListState>(
                        listener: (context, state) {
                          if (state is FetchedAuditData) {
                            auditList = state.auditResponse.auditList;
                            if (auditList.isNotEmpty) {
                              if (auditList.length == 1) {
                                selectedAudit = auditList[0];
                                _setDefaultValues(selectedAudit);
                                setState(() {});
                              } else if (auditList.length > 1) {
                                if (selectedSegmentValue == 0) {
                                  auditList.insert(
                                      0,
                                      Audit(
                                          auditEquipmentList: [],
                                          description:
                                              "Select a Audit or Task"));
                                } else if (selectedSegmentValue == 1) {
                                  auditList.insert(
                                      0,
                                      Audit(
                                          auditEquipmentList: [],
                                          description: "Select a Audit"));
                                } else if (selectedSegmentValue == 2) {
                                  auditList.insert(
                                      0,
                                      Audit(
                                          auditEquipmentList: [],
                                          description: "Select a Task"));
                                }
                                selectedAudit = auditList[0];
                              }
                            }
                          }
                        },
                        builder: (context, state) {
                          if (state is FetchingAuditData) {
                            return _getLoadingDropdown();
                          }
                          if (auditList.isEmpty) {
                            return const Column(
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: [
                                SizedBox(
                                  height: 5,
                                ),
                                Center(
                                  child: Text(
                                    "No Services found",
                                    style: TextStyle(fontSize: 18),
                                  ),
                                ),
                              ],
                            );
                          } else if (auditList.isNotEmpty) {
                            return _getAuditListDropdown();
                          }
                          return const Center(
                              child: CircularProgressIndicator());
                        },
                      ),
                      _getLopaLayoutFromJson()
                    ],
                  ),
                ),
              ),
            ),
          ),
          selectedAudit != null
              ? selectedAudit!.auditId != null
                  ? _getBottomOption(SelectedFleetCubit.seatCategoryList, false)
                  : Container()
              : Container(),
          ApplicationUtil.displayNotificationWidgetIfExist(
              context, LopaAuditPage.routeName)
        ],
      );
    } else {
      return Text(AppLocalizations.of(context)!.noRecordFound);
    }
  }

  _getLopaLayoutFromJson() {
    return Container(
      margin: const EdgeInsets.only(left: 20, right: 20, bottom: 20, top: 10),
      decoration: BoxDecoration(
          border: Border.all(color: Colors.black),
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(150),
            topRight: Radius.circular(150),
          ),
          color: Colors.white),
      child: Container(
        padding: const EdgeInsets.only(top: 5, left: 5, right: 5),
        margin: const EdgeInsets.only(top: 90),
        child: Column(
          mainAxisSize: MainAxisSize.max,
          children: [
            BlocConsumer<ServiceRequestBloc, ServiceRequestState>(
              listener: (context, state) {
                if (state is FetchedEquipmentFromAuditTable) {
                  if (state.equipmentList != null) {
                    auditEquipmentListFromDb = state.equipmentList!;
                    if (auditEquipmentListFromDb.isNotEmpty) {
                      for (var element in SelectedFleetCubit.seatCategoryList) {
                        for (var seatDetail in element.seatDetailList) {}
                      }
                      for (var auditEquipment in auditEquipmentList) {
                        AuditEquipment auditEquipmentFromDb =
                            auditEquipmentListFromDb
                                .where((audit) =>
                                    audit.name == auditEquipment.name)
                                .first;
                        auditEquipment.isScanned =
                            auditEquipmentFromDb.isScanned;
                        auditEquipment.imageMapData =
                            auditEquipmentFromDb.imageMapData;

                        for (var part in auditEquipment.partList) {
                          for (var databaseMap
                              in auditEquipmentFromDb.partsMapData) {
                            if (databaseMap['PartId'] == part.part1) {
                              part.selectedScore = auditScoreOptionList
                                  .where((element) =>
                                      element.choiceScore ==
                                      databaseMap['SelectedScore'])
                                  .first;
                              if (databaseMap
                                  .containsKey('SelectedSubChoice')) {
                                part.selectedSubScore = part
                                    .selectedScore!.subChoiceList
                                    .where((element) =>
                                        element.choiceValue ==
                                        databaseMap['SelectedSubChoice'])
                                    .first;
                              }
                              part.subScoreDescription =
                                  databaseMap['Description'];
                              print('databaseMap');
                              print(databaseMap['LocationId']);
                              if (part.selectedScore!.choiceScore != 4) {
                                part.isChecked = true;
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
              },
              builder: (context, state) {
                return _generateSeatsLayout();
              },
            ),

            //_sheetCurtain(),
            const SizedBox(
              height: 20,
            ),
          ],
        ),
      ),
    );
  }

  _generateSeatsLayout() {
    return BlocConsumer<RepairListApiBloc, RepairListApiState>(
      listener: (context, state) {
        if (state is FetchedLopaRepairServiceRequestList) {
          for (var element in state.repairServiceRequestList) {
            equipmentHavingSRList.add(element);
          }
        }
      },
      builder: (context, state) {
        return ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: SelectedFleetCubit.seatCategoryList.length,
          itemBuilder: (context, index) {
            return Container(
              margin: const EdgeInsets.symmetric(vertical: 10),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.black),
                borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(10),
                    topRight: Radius.circular(10)),
              ),
              child: Column(
                children: [
                  Container(
                    padding: const EdgeInsets.all(5),
                    decoration:
                        BoxDecoration(border: Border.all(color: Colors.black)),
                    child: Text(
                      SelectedFleetCubit.seatCategoryList[index].className,
                      style: const TextStyle(
                        fontSize: 18,
                      ),
                    ),
                  ),
                  _generateRowAndColumnForSeats(
                    SelectedFleetCubit.seatCategoryList[index].seatDetailList,
                    SelectedFleetCubit.seatCategoryList[index].rowCount,
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  _singleSeat(
    SeatDetail seatDetail,
    List<SeatDetail> seatDetailList,
    AuditEquipment? auditEquipment,
  ) {
    _getExistingServiceRequestsAndNotification(
        seatDetail, seatDetailList, auditEquipment);
    return InkWell(
      onTap: () {
        // CHECK EQUIPMENT IS NOT PART OF AUDIT
        if (selectedAudit?.serviceType != "TASK") {
          if (auditEquipment == null) {
            //EQUIPMENY
          } else {
            bool isSelectPartListEmpty = seatDetail.partList
                .where((element) => element.isChecked == true)
                .isEmpty;
            if (isSelectPartListEmpty) {
              auditEquipment.isScanned = false;
            } else {
              auditEquipment.isScanned = true;
            }
            /*auditEquipment.isScanned = !auditEquipment.isScanned;

          serviceRequestBloc.add(UpdateEquipmentDataFromAuditStatusByName(
              AuditEquipmentCubit.auditId!,
              seatDetail.name,
              auditEquipment,
              auditEquipmentList));
          setState(() {});*/

            showDialog(
                context: context,
                barrierDismissible: false,
                builder: (BuildContext context) {
                  return LopaAuditDialog(
                    title: "$seatTag: ${seatDetail.name}",
                    seatItemList:
                        _getSeatItem(seatDetail.partList, auditEquipment),
                    imageList: auditEquipment.imageMapData,
                    auditScoreOptionList: auditScoreOptionList,
                  );
                }).then((value) {
              //CONVERT TO MAP FROM OBJECT
              List<Map<String, dynamic>> partListMap = [];
              for (var part in auditEquipment.partList) {
                Map<String, dynamic> mapData = {
                  'PartId': part.part1,
                  'SelectedScore': part.selectedScore!.choiceScore,
                  'Description': part.subScoreDescription,
                  'LocationId': part.equipmentLocationId,
                };
                if (part.selectedSubScore != null) {
                  mapData['SelectedSubChoice'] =
                      part.selectedSubScore!.choiceValue;
                }
                partListMap.add(mapData);
              }
              auditEquipment.partsMapData = partListMap;
              //NOW SAVE IN DATABASE
              serviceRequestBloc.add(UpdateEquipmentDataFromAuditStatusByName(
                  AuditEquipmentCubit.auditId!,
                  seatDetail.name,
                  auditEquipment,
                  auditEquipmentList,
                  null,
                  null));
              print('continue');
              setState(() {});
            });
          }
        }
      },
      child: Card(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10.0),
        ),
        elevation: 5,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Center(
              child: Stack(
                alignment: Alignment.bottomCenter,
                children: [
                  Opacity(
                    opacity: 0.7,
                    child: Container(
                      height: 55,
                      margin: const EdgeInsets.symmetric(horizontal: 10),
                      decoration: BoxDecoration(
                          borderRadius: const BorderRadius.only(
                            topRight: Radius.circular(15),
                            topLeft: Radius.circular(15),
                          ),
                          color: _getForeGroundColorCode(
                              seatDetail, auditEquipment)),
                    ),
                  ),
                  Container(
                    height: 40,
                    margin: const EdgeInsets.symmetric(horizontal: 5),
                    decoration: BoxDecoration(
                        borderRadius: const BorderRadius.only(
                          topRight: Radius.circular(15),
                          topLeft: Radius.circular(15),
                        ),
                        color: _getBackgroundGroundColorCode(
                            seatDetail, auditEquipment)),
                    child: Center(
                      child: Text(
                        seatDetail.name,
                        style: const TextStyle(
                            fontWeight: FontWeight.bold, color: Colors.white),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  _getBottomOption(List<SeatCategory> seatCategoryList, bool isPresentInDb) {
    return Align(
      alignment: Alignment.bottomCenter,
      child: BlocConsumer<AuditListCubit, AuditListState>(
        listener: (context, state) {
          if (state is SubmittedAirlineAuditData) {
            auditEquipmentList = [];
            selectedAudit = null;
            auditList = [];
            auditCubit.getAuditListData(0, _getLocationId(), null,
                _getSelectedValue(selectedSegmentValue));
            ApplicationUtil.showSnackBar(
                context: context,
                message: (InMemoryAudiData.serviceType != null
                        ? InMemoryAudiData.serviceType!
                        : "Service") +
                    " Submitted Successfully");
            setState(() {});
          }
        },
        builder: (context, state) {
          if (state is SubmittingAirlineAuditData) {
            return const Padding(
              padding: EdgeInsets.all(8.0),
              child: CircularProgressIndicator(),
            );
          }
          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 18, horizontal: 16),
            child: ElevatedButton(
              style: ButtonStyle(
                padding: MaterialStateProperty.all(
                  const EdgeInsets.symmetric(vertical: 12, horizontal: 70),
                ),
              ),
              onPressed: () {
                submitAuditToServer(auditEquipmentList);
              },
              child: Text(
                AppLocalizations.of(context)!.submit,
                style: const TextStyle(color: Colors.white, fontSize: 16),
              ),
            ),
          );
        },
      ),
    );
  }

  _generateRowAndColumnForSeats(
    List<SeatDetail> seatList,
    int rowCount,
  ) {
    int rowSeatCount = rowCount;
    // int columnCount = firstClassSeatList.length/rowSeatCount;
    List<Widget> rowList = [];
    for (int seatPosition = 0; seatPosition < seatList.length;) {
      List<Widget> rowWidgets = [];
      List<SeatDetail> rowSeatList = [];
      for (int index = seatPosition;
          index < seatList.length &&
              index < seatPosition + seatList[index].rowCount!;
          index++) {
        SeatDetail seatDetail = seatList[index];
        rowSeatCount = seatList[index].rowCount!;

        ///GET AUDIT SEAT
        //CHECK IF SEAT IS THERE IN AUDIT EQUIPMENT
        bool isEmpty = auditEquipmentList
            .where((element) => element.name == seatDetail.name)
            .isEmpty;
        //IF IT IS THERE
        AuditEquipment? auditEquipment;
        if (!isEmpty && selectedAudit?.auditId != null) {
          auditEquipment = auditEquipmentList
              .where((element) => seatDetail.name == element.name)
              .first;
        }

        ///FINISH AUDIT SET UP
        rowWidgets.add(
          Stack(
            alignment: Alignment.topRight,
            children: [
              Container(
                margin: const EdgeInsets.all(5),
                width: 75,
                child: _singleSeat(seatDetail, seatList, auditEquipment),
              ),
              _getSelectedPartBadge(seatDetail, auditEquipment),
            ],
          ),
        );
        rowSeatList.add(seatDetail);
      }
      seatPosition = seatPosition + rowSeatCount;
      //Set all row selection to false only one time

      rowList.add(
        InkWell(
          onTap: () => null,
          child: Container(
            margin: const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
            decoration: BoxDecoration(
              border: Border.all(width: 2, color: Colors.grey),
            ),
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(5),
              child: Center(
                child: SingleChildScrollView(
                  physics: const BouncingScrollPhysics(),
                  scrollDirection: Axis.horizontal,
                  child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: rowWidgets),
                ),
              ),
            ),
          ),
        ),
      );
    }
    isMethodCalled = true;
    return Column(
      children: rowList,
    );
  }

  _getSelectedPartBadge(SeatDetail seatDetail, AuditEquipment? auditEquipment) {
    if (auditEquipment != null) {
      int count = auditEquipment.partList
          .where((element) => element.isChecked == true)
          .toList()
          .length;
      if (count > 0) {
        return Positioned(
          right: -6,
          top: -6,
          child: Container(
            margin: const EdgeInsets.all(7.0),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(15.0),
              color: Theme.of(context).primaryColor,
            ),
            constraints: const BoxConstraints(
              minWidth: 21,
              minHeight: 21,
            ),
            child: Center(
              child: Text(
                '$count',
                textAlign: TextAlign.center,
                style: const TextStyle(
                    fontSize: 13, color: Colors.white, height: 1.2),
              ),
            ),
          ),
        );
      }
    }
    return Container();
  }

  _getSubFleetName() {
    if (SelectedFleetCubit.fleetLocation.subFleetName !=
        AppLocalizations.of(context)!.select) {
      return Row(
        children: [
          Text(
            SelectedFleetCubit.fleetLocation.subFleetName,
            style: TextStyle(
                color: Theme.of(context).primaryColor,
                fontWeight: FontWeight.bold,
                fontSize: 18,
                height: 1.2),
          ),
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 5),
            child: const FaIcon(
              FontAwesomeIcons.angleRight,
              size: 20,
            ),
          ),
        ],
      );
    }
    return Container();
  }

  String _getLocationId() {
    FleetLocation fleetLocation = SelectedFleetCubit.fleetLocation;
    String locationId = fleetLocation.fleetName;
    if (fleetLocation.subFleetName != 'Select') {
      if (fleetLocation.subFleetName.contains('-')) {
        locationId += '-' + fleetLocation.subFleetName.replaceAll('-', '_');
      } else {
        locationId += '-' + fleetLocation.subFleetName;
      }
    }
    locationId += '-' + fleetLocation.tailName;
    return locationId;
  }

  _getFleetAndSubFleetName() {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 15),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Text(
                SelectedFleetCubit.fleetLocation.fleetName,
                style: TextStyle(
                    color: Theme.of(context).primaryColor,
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                    height: 1.2),
              ),
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 5),
                child: const FaIcon(
                  FontAwesomeIcons.angleRight,
                  size: 20,
                ),
              ),
              _getSubFleetName(),
              Text(
                SelectedFleetCubit.fleetLocation.tailName,
                style: TextStyle(
                    color: Theme.of(context).primaryColor,
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                    height: 1.2),
              ),
              Expanded(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    _createNewAuditButton(),
                  ],
                ),
              ),
            ],
          ),
        ),
        const SizedBox(
          height: 10,
        ),
        Container(
          width: double.maxFinite,
          margin: const EdgeInsets.only(left: 15, right: 15),
          child: Text(
            AppLocalizations.of(context)!.selectServiceType,
            style: const TextStyle(
                fontSize: 18, fontWeight: FontWeight.bold, color: Colors.black),
          ),
        ),
        const SizedBox(
          height: 5,
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
          child: Container(
            width: double.infinity,
            alignment: Alignment.center,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              border: Border.all(color: Theme.of(context).primaryColor),
            ),
            child: CupertinoSlidingSegmentedControl<int>(
              thumbColor: Theme.of(context).primaryColor,
              groupValue: selectedSegmentValue,
              backgroundColor: Colors.white,
              children: {
                0: buildSegment("All", 0, selectedSegmentValue!),
                1: buildSegment("Audits", 1, selectedSegmentValue!),
                2: buildSegment("Tasks", 2, selectedSegmentValue!),
              },
              onValueChanged: (value) {
                setState(() {
                  selectedAudit = null;
                  selectedSegmentValue = value;
                  InMemoryAudiData.selectedSegmentValueInMemory =
                      selectedSegmentValue;
                  auditCubit.getAuditListData(
                      0, _getLocationId(), null, _getSelectedValue(value));
                  print(selectedSegmentValue);
                });
              },
            ),
          ),
        ),
      ],
    );
  }

  _getAuditListDropdown() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          margin: const EdgeInsets.only(left: 15, right: 15),
          decoration: BoxDecoration(
              color: Theme.of(context).primaryColor,
              borderRadius: BorderRadius.circular(5)),
          child: DropdownButton(
            underline: Container(),
            value: selectedAudit,
            hint: Align(
              alignment: Alignment.centerLeft,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Text(
                  AppLocalizations.of(context)!.select,
                  textAlign: TextAlign.start,
                  style: const TextStyle(
                      color: Colors.white, fontWeight: FontWeight.bold),
                ),
              ),
            ),
            icon: const Icon(
              // Add this
              Icons.arrow_drop_down,
              color: Colors.white,
              size: 35, // Add this
            ),
            isExpanded: true,
            items: auditList.map(
              (val) {
                return DropdownMenuItem(
                  value: val,
                  child: _getSingleDropdownItem(val),
                );
              },
            ).toList(),
            selectedItemBuilder: (BuildContext ctx) {
              return auditList.map<Widget>((item) {
                return DropdownMenuItem<Audit>(
                    child: Container(
                      margin: const EdgeInsets.only(left: 20),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          item.serviceType != null
                              ? FaIcon(
                                  item.serviceType == 'TASK'
                                      ? FontAwesomeIcons.clipboardCheck
                                      : FontAwesomeIcons.userSecret,
                                  color: Colors.white,
                                  size: 24,
                                )
                              : const SizedBox(),
                          const SizedBox(width: 10),
                          Text(
                            item.description.toString(),
                            style: const TextStyle(color: Colors.white),
                          ),
                        ],
                      ),
                    ),
                    value: item);
              }).toList();
            },
            onChanged: (value) {
              setState(() {
                selectedAudit = value as Audit;
                _setDefaultValues(selectedAudit);
              });
            },
          ),
        ),
        const SizedBox(
          height: 5,
        ),
      ],
    );
  }

  Widget buildSegment(String text, int index, int groupValue) {
    return Container(
      width: double.maxFinite,
      alignment: Alignment.center,
      child: Text(
        text,
        style: TextStyle(
            fontSize: 18,
            color: index == groupValue
                ? Colors.white
                : Theme.of(context).primaryColor),
      ),
    );
  }

  _getSelectedValue(int? selectedValue) {
    switch (selectedValue) {
      case 0:
        return "ALL";
      case 1:
        return "AUDIT";
      case 2:
        return "TASK";
      default:
        return "ALL";
    }
  }

  _getForeGroundColorCode(
      SeatDetail seatDetail, AuditEquipment? auditEquipment) {
    Color color = Colors.blue.shade200;

    if (auditEquipment != null) {
      bool hasServiceRequest = equipmentHavingSRList
          .where((element) => element.equipmentName == auditEquipment.name)
          .isNotEmpty;
      if (hasServiceRequest) {
        color = Colors.blueGrey.shade200;
        if (!refresh) {
          SchedulerBinding.instance.scheduleFrameCallback((timeStamp) {
            setState(() {});
            refresh = true;
          });
        }
      } else {
        color = Colors.orange.shade200;
      }
    }
    return color;
  }

  _getBackgroundGroundColorCode(
      SeatDetail seatDetail, AuditEquipment? auditEquipment) {
    Color color = Colors.blue;

    if (auditEquipment != null) {
      ///CHECK IF EQUIPMENT HAS ALREADY SERVICE REQUEST
      bool hasServiceRequest = equipmentHavingSRList
          .where((element) => (element.equipmentName == auditEquipment.name &&
              element.requestType == "SERVICE_REQUEST"))
          .isNotEmpty;
      bool hasNotification = equipmentHavingSRList
          .where((element) => (element.equipmentName == auditEquipment.name &&
              element.requestType == "NOTIFICATION"))
          .isNotEmpty;
      bool hasSafetyIssue = equipmentHavingSRList
          .where((element) => (element.equipmentName == auditEquipment.name &&
              element.requestType == "SERVICE_REQUEST" &&
              element.safetyIssue == "Y"))
          .isNotEmpty;

      if (hasServiceRequest) {
        ///
        RepairServiceRequest repairServiceRequest = equipmentHavingSRList
            .where((element) => element.equipmentName == auditEquipment.name)
            .first;
        repairServiceRequest.partsMap!.forEach((key, value) {
          for (var data in List<Map<String, dynamic>>.from(value)) {
            //print(element['PartId']);
            if (auditEquipment.partList
                .where((element) => element.part1 == data['PartId'])
                .isNotEmpty) {
              auditEquipment.partList
                  .where((element) => element.part1 == data['PartId'])
                  .first
                  .hasServiceRequest = true;
              auditEquipment.partList.forEach((element) => () {
                    if (element.part1 == data['PartId'] &&
                        element.hasSafetyIssue) {
                      element.selectedScore = auditScoreOptionList[3];
                    } else if (element.part1 == data['PartId'] &&
                        !element.hasSafetyIssue) {
                      element.selectedScore = auditScoreOptionList[2];
                    }
                  });
            }
          }
        });
        color = Colors.grey;
      }
      if (hasSafetyIssue) {
        ///
        RepairServiceRequest repairServiceRequest = equipmentHavingSRList
            .where((element) => element.equipmentName == auditEquipment.name)
            .first;
        repairServiceRequest.partsMap!.forEach((key, value) {
          for (var data in List<Map<String, dynamic>>.from(value)) {
            //print(element['PartId']);
            if (auditEquipment.partList
                .where((element) => element.part1 == data['PartId'])
                .isNotEmpty) {
              auditEquipment.partList
                  .where((element) => element.part1 == data['PartId'])
                  .first
                  .hasServiceRequest = true;
              auditEquipment.partList
                  .where((element) => element.part1 == data['PartId'])
                  .first
                  .hasSafetyIssue = true;
              auditEquipment.partList
                  .where((element) => element.part1 == data['PartId'])
                  .first
                  .selectedScore = auditScoreOptionList[3];
            }
          }
        });
        color = Colors.grey;
      }
      if (hasNotification) {
        ///
        RepairServiceRequest repairServiceRequest = equipmentHavingSRList
            .where((element) => element.equipmentName == auditEquipment.name)
            .first;
        repairServiceRequest.partsMap!.forEach((key, value) {
          for (var data in List<Map<String, dynamic>>.from(value)) {
            //print(element['PartId']);
            if (auditEquipment.partList
                .where((element) => element.part1 == data['PartId'])
                .isNotEmpty) {
              auditEquipment.partList
                  .where((element) => element.part1 == data['PartId'])
                  .first
                  .hasNotification = true;
              auditEquipment.partList
                  .where((element) => element.part1 == data['PartId'])
                  .first
                  .selectedScore = auditScoreOptionList[1];
            }
          }
        });
        color = Colors.grey;
      } else {
        color = Colors.orange;
      }
    }
    return color;

    /*if (isSelected) {
      return Colors.orange;
    } else {
      return Colors.blue;
    }*/
  }

  void _setDefaultValues(Audit? selectedAudit) {
    print('default called');
    print('selectedAudit: $selectedAudit');
    auditEquipmentList = [];
    if (selectedAudit != null) {
      //DB OPERATION
      AuditEquipmentCubit.auditId = selectedAudit.auditId;
      /*AuditEquipmentCubit.auditLocation = selectedAudit.auditLocation;
      serviceRequestBloc.add(SaveEquipmentDataFromAuditStatus(
          selectedAudit.auditEquipment,
          selectedAudit.auditLocation,
          selectedAudit.auditId!));*/

      for (var element in selectedAudit.auditEquipmentList) {
        for (var seatCategory in SelectedFleetCubit.seatCategoryList) {
          for (var seatDetail in seatCategory.seatDetailList) {
            seatDetail.imageList = [];
            for (var part in seatDetail.partList) {
              part.selectedScore = auditScoreOptionList[0];
              part.isChecked = false;
              part.isComplete = false;
            }
            // NOW UPDATE DEFAULT PART SO THAT auditEquipmentList have all the parts
            if (element.name == seatDetail.name) {
              element.partList = seatDetail.partList;
            }
          }
        }
        auditEquipmentList.add(element);
      }
      serviceRequestBloc
          .add(GetEquipmentDataFromAuditStatus(selectedAudit.auditId!));
    }
  }

  _createNewAuditButton() => TextButton(
      onPressed: () => _createNewAudit(),
      child: Text(
        AppLocalizations.of(context)!.createAdhocAudit,
        textAlign: TextAlign.center,
        style: const TextStyle(color: Colors.black, fontSize: 12),
      ),
      style: TextButton.styleFrom(
          minimumSize: Size.zero,
          padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 10),
          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
          shape: RoundedRectangleBorder(
            side: const BorderSide(
                color: AppColor.greenSentColor,
                width: 1,
                style: BorderStyle.solid),
            borderRadius: BorderRadius.circular(5),
          )));

  void _createNewAudit() {
    print(selectedAudit.toString());
    _auditCommentController.text = '';
    showDialog(
      barrierDismissible: false,
      context: context,
      builder: (BuildContext context) {
        return CreateAirlineAdhocAuditDialog(
          auditList: const [],
          auditEquipmentList: const [],
          apiBloc: apiBloc,
          locationId: _getLocationId(),
          auditCubit: auditCubit,
          context: context,
          selectedAudit: null,
        );
      },
    ).then((value) => setState(() {
          print(selectedAudit.toString());
          print(value.toString());
          if (value != "cancel") {
            auditCubit.getAuditListData(0, _getLocationId(), null,
                _getSelectedValue(selectedSegmentValue));
          }
        }));
  }

  void submitAuditToServer(List<AuditEquipment> auditEquipmentList) {
    Map<String, List<Map<String, dynamic>>> mapData = {};
    Map<String, List<Map<String, dynamic>>> imageData = {};

    for (var auditEquipment in auditEquipmentList) {
      List<Map<String, dynamic>> checkPartList = [];
      // LOOP EACH PART
      for (var part in auditEquipment.partList) {
        Map<String, dynamic> partsMap = {};

        if (part.isChecked) {
          String reason = '';
          /*if (part.selectedScore != null) {
            reason += part.selectedScore!.choiceValue!;
          }*/
          if (part.selectedSubScore != null) {
            if (part.selectedSubScore!.choiceValue == 'OTHER') {
              reason += part.selectedSubScore!.choiceValue +
                  '_' +
                  part.subScoreDescription;
            } else {
              reason += part.selectedSubScore!.choiceValue +
                  '_' +
                  part.selectedSubScore!.choiceName;
            }
          }
          partsMap['Reason'] = reason;
          partsMap['PartId'] = part.part1;
          partsMap['LocationId'] = part.equipmentLocationId;
          partsMap['AuditScore'] = part.selectedScore!.choiceScore;
          checkPartList.add(partsMap);
        }
      }
      if (checkPartList.isNotEmpty) {
        mapData[auditEquipment.name!] = checkPartList;
        imageData[auditEquipment.name!] = auditEquipment.imageMapData;
      }
    }
    /*print(json.encode(mapData));
    return;*/
    if (mapData.isEmpty) {
      ApplicationUtil.showWarningAlertDialog(context,
          title: AppLocalizations.of(context)!.alert,
          desc: selectedAudit?.serviceType == "TASK"
              ? AppLocalizations.of(context)!
                  .airlineSubmitTaskConfirmationDescription
              : customerId == 15
                  ? AppLocalizations.of(context)!
                      .airlineSubmitAuditConfirmationDescriptionForStadium
                  : AppLocalizations.of(context)!
                      .airlineSubmitAuditConfirmationDescription,
          positiveLabel: AppLocalizations.of(context)!.submit,
          negativeLabel: AppLocalizations.of(context)!.cancel,
          onPositiveClickListener: () {
        auditCubit.submitAirLineAudit(
          mapData,
          imageData,
          AuditEquipmentCubit.auditId!,
          locationData,
        );
        Navigator.of(context).pop();
      });
    } else {
      auditCubit.submitAirLineAudit(
        mapData,
        imageData,
        AuditEquipmentCubit.auditId!,
        locationData,
      );
    }
  }

  List<Part> _getSeatItem(List<Part> partList, AuditEquipment auditEquipment) {
    auditEquipment.partList = List.from(partList);
    return auditEquipment.partList;
  }

  void _initAuditOption() {
    Map<String, dynamic>? data = getIt<Customization>().customizationData;

    if (data != null && data.containsKey("CHOICE")) {
      if (data['CHOICE'].containsKey('AUDIT_SCORES')) {
        List<Map<String, dynamic>> dynamicList =
            List<Map<String, dynamic>>.from(data['CHOICE']['AUDIT_SCORES']);
        for (var element in dynamicList) {
          auditScoreOptionList.add(AuditScoreOption.fromJson(element));
        }
        auditScoreOptionList
            .sort((b, a) => a.choiceScore.compareTo(b.choiceScore));
      }
    }
  }

  void _initLocation() {
    ApplicationUtil.getLocation().then((value) {
      locationData = value;
    });
  }

  _getSingleDropdownItem(Audit audit) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        audit.serviceType != null
            ? FaIcon(
                audit.serviceType == 'TASK'
                    ? FontAwesomeIcons.clipboardCheck
                    : FontAwesomeIcons.userSecret,
                color: audit.serviceType == 'TASK'
                    ? AppColor.primarySwatchColor
                    : AppColor.redColor,
                size: 24,
              )
            : const SizedBox(
                width: 20,
              ),
        const SizedBox(width: 10),
        Text(
          audit.description.toString(),
          //overflow: TextOverflow.ellipsis,
          style: TextStyle(color: Theme.of(context).primaryColor),
        ),
      ],
    );
  }

  _getLoadingDropdown() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          margin: const EdgeInsets.only(left: 15, right: 15),
          decoration: BoxDecoration(
              color: Theme.of(context).primaryColor,
              borderRadius: BorderRadius.circular(5)),
          child: DropdownButton(
            underline: Container(),
            hint: Align(
              alignment: Alignment.centerLeft,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: const Text(
                  "Loading...",
                  textAlign: TextAlign.start,
                  style: TextStyle(
                      color: Colors.white, fontWeight: FontWeight.bold),
                ),
              ),
            ),
            icon: const Icon(
              // Add this
              Icons.arrow_drop_down,
              color: Colors.white,
              size: 35, // Add this
            ),
            isExpanded: true,
            items: auditList.map(
              (val) {
                return DropdownMenuItem(
                  value: val,
                  child: _getSingleDropdownItem(val),
                );
              },
            ).toList(),
            selectedItemBuilder: (BuildContext ctx) {
              return auditList.map<Widget>((item) {
                return DropdownMenuItem<Audit>(
                    child: Container(
                      margin: const EdgeInsets.only(left: 20),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          item.serviceType != "ALL"
                              ? FaIcon(
                                  item.serviceType == 'TASK'
                                      ? FontAwesomeIcons.clipboardCheck
                                      : FontAwesomeIcons.userSecret,
                                  color: Colors.white,
                                  size: 24,
                                )
                              : const SizedBox(),
                          const SizedBox(width: 10),
                          Text(
                            item.description.toString(),
                            style: const TextStyle(color: Colors.white),
                          ),
                        ],
                      ),
                    ),
                    value: item);
              }).toList();
            },
            onChanged: (value) {
              setState(() {
                selectedAudit = value as Audit;
                _setDefaultValues(selectedAudit);
              });
            },
          ),
        ),
        const SizedBox(
          height: 5,
        ),
      ],
    );
  }

  void _getExistingServiceRequestsAndNotification(SeatDetail seatDetail,
      List<SeatDetail> seatDetailList, AuditEquipment? auditEquipment) {
    if (auditEquipment != null) {
      var partItemList = _getSeatItem(seatDetail.partList, auditEquipment);
      if (selectedAudit != null) {
        partItemList
            .where((element) => (element.hasServiceRequest == true ||
                element.hasNotification == true))
            .map((seatItem) {
          /// Increment the counter a part and set isChecked parameter to true
          if (seatItem.hasServiceRequest == true &&
              seatItem.isChecked == false) {
            seatItem.selectedScore = auditScoreOptionList[2];
            seatItem.isChecked = true;
            seatItem.checkedCount++;
          } else if (seatItem.hasNotification == true &&
              seatItem.isChecked == false) {
            //SubChoice subChoice=seatItem.selectedScore!.subChoiceList[0];
            seatItem.selectedScore = auditScoreOptionList[1];
            seatItem.isChecked = true;
            seatItem.checkedCount++;
          }
        }).toList();
      }
    }
  }
}

class SingleEquipmentAndPart {
  String equipmentName;
  List<LopaPartItem> partList;
  List<Map<String, dynamic>> imageList;
  List<Map<String, dynamic>> serverImageList;

  SingleEquipmentAndPart(
      {required this.equipmentName,
      required this.partList,
      required this.imageList,
      required this.serverImageList});

  factory SingleEquipmentAndPart.fromMap(
      Map<String, dynamic> map, RepairServiceRequest selectedServiceRequest) {
    return SingleEquipmentAndPart(
        equipmentName: map['equipmentName'] as String,
        partList: List<LopaPartItem>.from(
            map['partList'].map((e) => LopaPartItem.fromMap(e)).toList()),
        imageList: [],
        serverImageList: LopaPartItem.getImageListByEquipment(
            map['equipmentName'], selectedServiceRequest));
  }
}

class LopaPartItem {
  String partId;
  bool completed;
  bool isRepaired;
  bool isChecked;
  int checkCount;
  String description;
  List<Map<String, dynamic>> imageList = [];

  LopaPartItem(
      {required this.partId,
      this.completed = false,
      this.isRepaired = false,
      this.isChecked = false,
      this.checkCount = 0,
      this.description = '',
      required this.imageList});

  factory LopaPartItem.fromMap(Map<String, dynamic> map) {
    return LopaPartItem(
      partId: map['PartId'] as String,
      completed: map['Completed'] as bool,
      isRepaired: map['IsRepaired'] != null ? map['IsRepaired'] as bool : false,
      imageList: [],
    );
  }

  @override
  String toString() {
    return 'LopaPartItem{partId: $partId, completed: $completed, isRepaired: $isRepaired}';
  }

  static List<LopaPartItem> getPartListFromJson(List<dynamic> list) {
    List<LopaPartItem> partList = [];
    for (var map in list) {
      partList.add(LopaPartItem(
          partId: map['PartId'],
          completed: map['Completed'],
          isRepaired: map['Completed'] ?? false,
          imageList: []));
    }
    return partList;
  }

  static List<Map<String, dynamic>> getImageListByEquipment(
      equipmentName, RepairServiceRequest selectedServiceRequest) {
    if (selectedServiceRequest.imageListMap != null) {
      if (selectedServiceRequest.imageListMap![equipmentName] != null) {
        return List<Map<String, dynamic>>.from(
            selectedServiceRequest.imageListMap![equipmentName]);
      }
    }
    return [];
  }
}
