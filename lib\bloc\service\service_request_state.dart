part of 'service_request_bloc.dart';

@immutable
abstract class ServiceRequestState {}

class ServiceRequestInitial extends ServiceRequestState {
  ServiceRequestInitial();
}

class ServiceRequestLoading extends ServiceRequestState {
  ServiceRequestLoading();
}

class ServiceRequestSaving extends ServiceRequestState {
  ServiceRequestSaving();
}

class ServiceRequestUpdated extends ServiceRequestState {
  ServiceRequestUpdated();
}

class ServiceRequestUpdating extends ServiceRequestState {
  ServiceRequestUpdating();
}

class ServiceRequestDeleting extends ServiceRequestState {
  ServiceRequestDeleting();
}

class ServiceRequestDeleted extends ServiceRequestState {
  ServiceRequestDeleted();
}

class ServiceRequestNotificationSent extends ServiceRequestState {
  final int count;
  final String message;
  ServiceRequestNotificationSent({required this.count, required this.message});
}

class ServiceRequestErrorNotificationSent extends ServiceRequestState {
  final String message;
  ServiceRequestErrorNotificationSent({required this.message});
}

class ServiceRequestErrorNotificationDismissed extends ServiceRequestState {
  ServiceRequestErrorNotificationDismissed();
}

class ServiceNotificationDismissed extends ServiceRequestState {}

class ServiceRequestSaved extends ServiceRequestState {
  final value;
  ServiceRequestSaved(this.value);
}

class ServiceRequestLoaded extends ServiceRequestState {
  final List<ServiceRequestData> serviceRequestList;
  ServiceRequestLoaded(this.serviceRequestList);

  @override
  bool operator ==(Object o) {
    if (identical(this, o)) return true;

    return o is ServiceRequestLoaded &&
        o.serviceRequestList == serviceRequestList;
  }

  @override
  int get hashCode => serviceRequestList.hashCode;
}

class ServiceRequestError extends ServiceRequestState {
  final String error;
  ServiceRequestError({required this.error});

  @override
  bool operator ==(Object o) {
    if (identical(this, o)) return true;

    return o is ServiceRequestError && o.error == error;
  }

  @override
  int get hashCode => error.hashCode;
}

class ServiceRequestSendingToServer extends ServiceRequestState {}

class FetchingPendingServiceRequestFromDb extends ServiceRequestState {}

class FetchedPendingServiceRequestFromDb extends ServiceRequestState {
  final int pendingCount;
  FetchedPendingServiceRequestFromDb({required this.pendingCount});
}

class PendingServiceRequestDbError extends ServiceRequestState {
  final String error;
  PendingServiceRequestDbError({required this.error});
}

class ServiceRequestSyncStateInitial extends ServiceRequestState {}

class SendingPendingServiceRequestToServer extends ServiceRequestState {}

class SentPendingServiceRequestToServer extends ServiceRequestState {
  final int? count;
  final ServiceRequestType requestType;
  SentPendingServiceRequestToServer({this.count, required this.requestType});
}

class NoPendingServiceRequestInDatabase extends ServiceRequestState {
  final String? error;
  NoPendingServiceRequestInDatabase({this.error});
}

class NetworkErrorInPendingServiceRequest extends ServiceRequestState {
  final String errorMessage;
  final int pendingRequestCount;
  NetworkErrorInPendingServiceRequest({
    required this.errorMessage,
    this.pendingRequestCount = 0,
  });
}

//audit barcode
class BarcodeDataUpdating extends ServiceRequestState {}

class BarcodeDataUpdated extends ServiceRequestState {}

class BarcodeDataError extends ServiceRequestState {
  final String error;
  BarcodeDataError({required this.error});
}

class FetchingEquipmentFromAuditTable extends ServiceRequestState {}

class FetchedEquipmentFromAuditTable extends ServiceRequestState {
  final int auditId;
  final List<AuditEquipment>? equipmentList;
  FetchedEquipmentFromAuditTable(
      {required this.auditId, required this.equipmentList});
}

class FetchingEquipmentFromAuditTableForMap extends ServiceRequestState {}

class FetchedEquipmentFromAuditTableForMap extends ServiceRequestState {
  final int auditId;
  final List<AuditEquipment>? equipmentList;
  FetchedEquipmentFromAuditTableForMap(
      {required this.auditId, required this.equipmentList});
}

class EquipmentErrorFromAuditTable extends ServiceRequestState {
  final String error;
  EquipmentErrorFromAuditTable({required this.error});
}

class UpdatingEquipmentFromAuditTable extends ServiceRequestState {}

class UpdatedEquipmentFromAuditTable extends ServiceRequestState {}

class UpdatedErrorFromAuditTable extends ServiceRequestState {
  final String error;
  UpdatedErrorFromAuditTable({required this.error});
}

class DeletingAuditFromTable extends ServiceRequestState {}

class DeletedAuditFromTable extends ServiceRequestState {}

class DeleteErrorAuditFromTable extends ServiceRequestState {
  final String error;
  DeleteErrorAuditFromTable({required this.error});
}

class SavingEquipmentData extends ServiceRequestState {}

class SavedEquipmentData extends ServiceRequestState {}

class SaveEquipmentError extends ServiceRequestState {
  final String error;
  SaveEquipmentError({required this.error});
}

class UpdatingEquipmentFromAuditTableByName extends ServiceRequestState {}

class UpdatedEquipmentFromAuditTableByName extends ServiceRequestState {}

class UpdatedErrorFromAuditTableByName extends ServiceRequestState {
  final String error;
  UpdatedErrorFromAuditTableByName({required this.error});
}
