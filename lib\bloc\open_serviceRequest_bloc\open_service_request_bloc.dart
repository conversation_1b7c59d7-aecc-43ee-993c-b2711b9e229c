import 'package:flutter_bloc/flutter_bloc.dart';
import '../../data/repository/api_service.dart';
import 'open_service_request_event.dart';
import 'open_service_request_state.dart';

class OpenServiceRequestBloc
    extends Bloc<OpenServiceRequestEvent, OpenServiceRequestState> {
  final ApiService apiService;

  OpenServiceRequestBloc({required this.apiService})
      : super(OpenServiceRequestInitial()) {
    on<FetchOpenServiceRequestCount>((event, emit) async {
      emit(OpenServiceRequestLoading());
      try {
        print(
            "Fetching open service request count for IDs: ${event.requestIds}");
        List response = await apiService.getOpenServiceRequestCount(
          equipmentIds: event.requestIds,
          isRestricted: event.isRestricted,
        );
        emit(OpenServiceRequestLoaded(response));
      } catch (error) {
        emit(OpenServiceRequestError(error.toString()));
      }
    });
  }
}
