import 'dart:io';

import 'package:alink/util/application_util.dart';
import 'package:alink/widget/bar_code_dialog.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:geolocator/geolocator.dart';
import 'package:vibration/vibration.dart';
import 'package:qr_code_scanner/qr_code_scanner.dart';

import '../../logger/logger.dart';

class BarcodeScannerWidget extends StatefulWidget {
  final String? title;
  final Function(String result, bool isScanned)? _resultCallback;

  const BarcodeScannerWidget(this._resultCallback, {Key? key, this.title})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _AppBarcodeScannerWidgetState();
  }
}

class _AppBarcodeScannerWidgetState extends State<BarcodeScannerWidget> {
  Barcode? result;
  QRViewController? controller;
  final GlobalKey qrKey = GlobalKey(debugLabel: 'QR');

  //bool isFlashOn = false;
  bool isScanned = false;
  String className = "_AppBarcodeScannerWidgetState";

  @override
  void reassemble() {
    super.reassemble();
    /*if (Platform.isAndroid) {
      controller!.pauseCamera();
    }*/
    if (Platform.isIOS) {
      controller!.resumeCamera();
    }
    // controller!.resumeCamera();
  }

  @override
  Widget build(BuildContext context) {
    Logger.i("Class Name: $className");
    return Stack(
      alignment: Alignment.bottomCenter,
      children: [
        Stack(
          alignment: Alignment.center,
          children: [
            Column(
              children: <Widget>[
                Expanded(flex: 4, child: _buildQrView(context)),
              ],
            ),
            Center(
              child: Container(
                margin: EdgeInsets.only(
                    top: MediaQuery.of(context).size.width - 50),
                child: Text(
                  AppLocalizations.of(context)!.getTheBarcodeInSquare,
                  style: const TextStyle(color: Colors.white, fontSize: 18),
                ),
              ),
            ),
            Center(
              child: Container(
                margin: EdgeInsets.only(
                    top: MediaQuery.of(context).size.width + 20),
                child: Text(
                  widget.title ?? "",
                  style: const TextStyle(fontSize: 28, color: Colors.white),
                ),
              ),
            ),
          ],
        ),
        Column(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Container(
              padding: const EdgeInsets.symmetric(vertical: 50),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  getButtonsUI(),
                  const SizedBox(
                    width: 40,
                  ),
                  IconButton(
                    onPressed: () {
                      showGeneralDialog(
                        barrierDismissible: false,
                        context: context,
                        pageBuilder: (context, animation, secondaryAnimation) {
                          return BarCodeDialog(
                            callback: (String barcode) {
                              Navigator.pop(context);
                              widget._resultCallback!(barcode, false);
                            },
                            onClose: () {},
                          );
                        },
                      );
                      //Navigator.pop(context, AppConstant.MANUAL_BAR_CODE_READ);
                    },
                    icon: const Icon(
                      Icons.keyboard_alt,
                      size: 40,
                      color: Colors.white,
                    ),
                  )
                ],
              ),
            ),
          ],
        )
      ],
    );
  }

  Widget _buildQrView(BuildContext context) {
    // For this example we check how width or tall the device is and change the scanArea and overlay accordingly.

    double width = MediaQuery.of(context).size.width;
    double height = MediaQuery.of(context).size.height;
    var scanArea = 0.0;
    //tablet
    if (width > height) {
      scanArea = height - 50;
    } else {
      scanArea = width - 100;
    }

    /*var scanArea = (MediaQuery.of(context).size.width < 400 ||
            MediaQuery.of(context).size.height < 400)
        ? 150.0
        : 300.0;*/
    // To ensure the Scanner view is properly sizes after rotation
    // we need to listen for Flutter SizeChanged notification and update controller
    return QRView(
      key: qrKey,
      onQRViewCreated: _onQRViewCreated,
      overlay: QrScannerOverlayShape(
          borderColor: Colors.red,
          borderRadius: 10,
          borderLength: 30,
          borderWidth: 10,
          cutOutSize: scanArea),
      onPermissionSet: (ctrl, p) => _onPermissionSet(context, ctrl, p),
    );
  }

  void _onQRViewCreated(QRViewController controller) async {
    /*// setState(() {
      this.controller = controller;
      controller.resumeCamera();
    // });*/
    this.controller = controller;
    if (Platform.isAndroid) {
      await controller.resumeCamera();
    }
    bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
    LocationPermission permission = await Geolocator.checkPermission();
    if (serviceEnabled &&
        permission != LocationPermission.denied &&
        permission != LocationPermission.deniedForever) {
      LocationAccuracyStatus status = await Geolocator.getLocationAccuracy();
      if (status != LocationAccuracyStatus.precise) {
        ApplicationUtil.showWarningAlertDialog(
          context,
          title: AppLocalizations.of(context)!.locationPermissionRequired,
          desc:
              "To provide accurate results based on your current position, we need access to your precise location. Please enable Precise Location in your device settings",
          negativeLabel: AppLocalizations.of(context)!.okay,
        );
        return;
      }
    }
    controller.scannedDataStream.listen((scanData) {
      if (!isScanned) {
        isScanned = true;
        Vibration.vibrate();
        result = scanData;
        if (result != null && result!.code != null) {
          widget._resultCallback!(result!.code!, true);
        }
      }
    });
  }

  void _onPermissionSet(BuildContext context, QRViewController ctrl, bool p) {
    //log('${DateTime.now().toIso8601String()}_onPermissionSet $p');
    if (!p) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('no Permission')),
      );
    }
  }

  @override
  void dispose() {
    controller?.dispose();
    super.dispose();
  }

  getButtonsUI() {
    return Container(
      margin: const EdgeInsets.all(8),
      child: IconButton(
          onPressed: () async {
            await controller?.toggleFlash();
            setState(() {});
          },
          icon: FutureBuilder(
            future: controller?.getFlashStatus(),
            builder: (context, snapshot) {
              if (snapshot.data == true) {
                return const Icon(
                  Icons.flash_on,
                  color: Colors.white,
                  size: 35,
                );
              } else {
                return const Icon(
                  Icons.flash_off,
                  color: Colors.white,
                  size: 35,
                );
              }
            },
          )),
    );
  }
}
