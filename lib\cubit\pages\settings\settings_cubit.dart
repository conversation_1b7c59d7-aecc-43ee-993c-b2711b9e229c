import 'package:alink/service_locator.dart';
import 'package:alink/util/app_constant.dart';
import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';
import 'package:shared_preferences/shared_preferences.dart';

part 'settings_state.dart';

class SettingsCubit extends Cubit<SettingsState> {
  SharedPreferences? sharedPreference;
  SettingsCubit() : super(SettingsInitial());

  updateLogPref(String selection) {
    sharedPreference = getIt<SharedPreferences>();
    emit(LogPreferencesUpdating());
    sharedPreference!.setString('logLevelPref', selection);
    emit(LogPreferenceFetched(selection: selection));
  }

  getLogPref() {
    sharedPreference = getIt<SharedPreferences>();
    emit(LogPreferenceFetching());
    String res =
        sharedPreference!.getString('logLevelPref') ?? AppConstant.DEBUG;
    emit(LogPreferenceFetched(selection: res));
  }
}
