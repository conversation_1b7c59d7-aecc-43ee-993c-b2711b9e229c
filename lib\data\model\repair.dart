import 'package:drift/drift.dart';

class Repair extends Table {
  @override
  String get tableName => 'REPAIR';
  IntColumn get repairId => integer().autoIncrement().named('REPAIR_ID')();

  IntColumn get customerId => integer().nullable().named('CUSTOMER_ID')();
  TextColumn get equipmentId => text().nullable().named('EQUIPMENT_ID')();
  //IntColumn get requestType => integer().nullable().named('REQUEST_TYPE')();
  TextColumn get currentLocationId =>
      text().nullable().named('CURRENT_LOCATION')();
  TextColumn get newLocationId => text().nullable().named('NEW_LOCATION')();
  //TextColumn get description => text().nullable().named('DESCRIPTION')();
  IntColumn get createdBy => integer().nullable().named('CREATED_BY')();
  DateTimeColumn get createdAt => dateTime().nullable().named('CREATED_AT')();
  IntColumn get status => integer().nullable().named('STATUS')();
  TextColumn get extn => text().nullable().named('EXTN')();
  TextColumn get parts => text().nullable().named('PARTS')();
  TextColumn get repairDocument => text().nullable().named('REPAIR_DOCUMENT')();
  TextColumn get serviceRequestDocument =>
      text().nullable().named('SERVICE_REQUEST_DOCUMENT')();
  IntColumn get requestId => integer().nullable().named('REQUEST_ID')();
  RealColumn get latitude => real().nullable().named('LATITUDE')();
  RealColumn get longitude => real().nullable().named('LONGITUDE')();
  TextColumn get refId => text().nullable().named('REF_ID')();
  BoolColumn get safety => boolean().nullable().named('SAFETY')();
}
