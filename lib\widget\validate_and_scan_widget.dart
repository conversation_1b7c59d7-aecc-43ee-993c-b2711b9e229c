import 'package:alink/bloc/api/api_bloc.dart';
import 'package:alink/bloc/service/service_request_bloc.dart';
import 'package:alink/data/model/audit_id_model.dart';
import 'package:alink/data/model/audit_response.dart';
import 'package:alink/data/model/confirm_audit_model.dart';
import 'package:alink/pages/airport/audit/confirm_audit_page.dart';
import 'package:alink/util/application_util.dart';
import 'package:alink/widget/scan_barcode_button_widget.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class ValidateAndScanButtons extends StatefulWidget {
  final List<AuditEquipment> equipmentList;
  final ServiceRequestBloc serviceRequestBloc;
  final ApiBloc apiBloc;
  final int? auditId;
  final AuditRefId? auditRefId;
  final bool? isConditionAudit;
  final Function onTapScan;
  final bool isFromMap;

  const ValidateAndScanButtons(
      {super.key,
      required this.equipmentList,
      required this.serviceRequestBloc,
      required this.auditId,
      required this.auditRefId,
      required this.apiBloc,
      required this.isConditionAudit,
      required this.onTapScan,
      this.isFromMap = false});

  @override
  State<ValidateAndScanButtons> createState() => _ValidateAndSaveButtonsState();
}

class _ValidateAndSaveButtonsState extends State<ValidateAndScanButtons> {
  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        _getValidateButton(widget.equipmentList),
        ScanBarCodeButton.getScanBarcodeButton(context,
            label: AppLocalizations.of(context)!.scanEquipment, onTap: () {
          widget.onTapScan();
        }),
        Container(
          alignment: Alignment.bottomRight,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          child: ApplicationUtil.getBackButton(
            context,
            onBackPressed: () {
              Navigator.of(context).pop(true);
            },
          ),
        )
      ],
    );
  }

  _getValidateButton(List<AuditEquipment> equipmentList) {
    List<AuditEquipment> filteredList = [];
    List<AuditEquipment> unScannedList = [];

    for (var element in equipmentList) {
      if ((element.isScanned == true || element.status == "Movement") &&
          (element.isScanned == true ||
              (element.status == "Scanned" || element.status == "Inactive"))) {
        filteredList.add(element);
      } else if (element.isScanned == false) {
        unScannedList.add(element);
      }
    }
    if (kDebugMode) {
      print("filteredList");
      print(filteredList);
    }
    return Container(
      alignment: Alignment.bottomLeft,
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 12),
      child: ElevatedButton(
        style: ButtonStyle(
          backgroundColor: MaterialStateProperty.all(Colors.green),
          shape: MaterialStateProperty.all(
            const RoundedRectangleBorder(
              borderRadius: BorderRadius.all(
                Radius.circular(5),
              ),
            ),
          ),
        ),
        onPressed: () async {
          int scannedEquipment = 0;
          for (var element in equipmentList) {
            if (element.status == "Scanned") {
              scannedEquipment += 1;
            }
          }
          if (scannedEquipment == 0 && widget.isConditionAudit!) {
            ApplicationUtil.showWarningAlertDialog(context,
                title: AppLocalizations.of(context)!.warning,
                desc: AppLocalizations.of(context)!.conditionAuditMessage,
                negativeLabel: AppLocalizations.of(context)!.okay);
          } else {
            if (filteredList.isNotEmpty) {
              widget.apiBloc.add(ValidateAuditBarcodes(
                  equipmentList: filteredList, auditId: widget.auditId ?? 0));

              var res =
                  await Navigator.pushNamed(context, ConfirmAuditPage.routeName,
                      arguments: ConfirmAuditModel(
                        isEquipmentsValidated: true,
                        refAuditId: widget.auditRefId?.refAuditId ?? 0,
                        auditName: widget.auditRefId?.auditName,
                        isConditionAudit: widget.isConditionAudit,
                        scannedUnscannedCount: scannedEquipment,
                        serviceType: widget.auditRefId?.serviceType ?? "",
                        isFromMap: widget.isFromMap,
                      ));
              if (res == true) {
                setState(() {});
              }
            } else {
              widget.apiBloc.add(ValidateAuditBarcodes(
                  equipmentList: const [], auditId: widget.auditId ?? 0));

              var res = await Navigator.pushNamed(
                  context, ConfirmAuditPage.routeName,
                  arguments: ConfirmAuditModel(
                      isFromMap: widget.isFromMap,
                      isEquipmentsValidated: true,
                      refAuditId: widget.auditRefId?.refAuditId,
                      auditName: widget.auditRefId?.auditName,
                      isConditionAudit: widget.isConditionAudit,
                      scannedUnscannedCount: scannedEquipment,
                      serviceType: widget.auditRefId?.serviceType));
              if (res == true) {
                setState(() {});
              }
            }
          }
        },
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 18),
          child: Text(
            AppLocalizations.of(context)!.validate,
            style: const TextStyle(color: Colors.white),
          ),
        ),
      ),
    );
  }
}
