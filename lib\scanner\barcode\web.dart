import 'package:alink/util/barcode_html.dart';
import 'package:alink/widget/bar_code_dialog.dart';
import 'package:flutter/material.dart';
import 'package:webviewx_plus/webviewx_plus.dart';

class BarcodeScannerWidget extends StatefulWidget {
  final String? title;
  final Function(String result, bool isScanned)? _resultCallback;
  const BarcodeScannerWidget(this._resultCallback, {Key? key, this.title})
      : super(key: key);
  @override
  State<StatefulWidget> createState() {
    return _AppBarcodeScannerWidgetState();
  }
}

class _AppBarcodeScannerWidgetState extends State<BarcodeScannerWidget> {
  bool isFlashOn = false;
  WebViewXController? webviewController;
  bool showDialog = false;
  String barcodeValue = '';

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.black,
      child: Stack(
        alignment: Alignment.bottomCenter,
        children: [
          !showDialog
              ? WebViewX(
                  width: MediaQuery.of(context).size.width,
                  height: MediaQuery.of(context).size.height,
                  javascriptMode: JavascriptMode.unrestricted,
                  initialContent: barcodeHtmlString,
                  initialSourceType: SourceType.html,
                  onWebViewCreated: (controller) {
                    webviewController = controller;
                    // _loadHtmlFromAssets();
                  },
                  onPageFinished: (src) {
                    // print(pageLoaded);
                    //pageLoaded = true;
                    //loadForm();
                  },
                  dartCallBacks: {
                    DartCallback(
                      name: 'SubmitCallback',
                      callBack: (msg) {
                        String msgString = msg.toString();
                        if (barcodeValue != msgString) {
                          barcodeValue = msgString;
                          widget._resultCallback!(barcodeValue, false);
                        }
                      },
                    ),
                    DartCallback(
                      name: 'CloseCallback',
                      callBack: (msg) {
                        Navigator.pop(context);
                      },
                    )
                  },
                )
              : Container(),
          WebViewAware(
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 50),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  _getFlashButton(),
                  const SizedBox(
                    width: 40,
                  ),
                  IconButton(
                    onPressed: () {
                      showDialog = true;
                      setState(() {});
                      showGeneralDialog(
                        barrierDismissible: false,
                        context: context,
                        pageBuilder: (context, animation, secondaryAnimation) {
                          return BarCodeDialog(
                            callback: (String barcode) {
                              Navigator.pop(context);
                              widget._resultCallback!(barcode, false);
                            },
                            onClose: () {
                              showDialog = false;
                              setState(() {});
                            },
                          );
                        },
                      );
                      //Navigator.pop(context, AppConstant.MANUAL_BAR_CODE_READ);
                    },
                    icon: const Icon(
                      Icons.keyboard_alt,
                      size: 40,
                      color: Colors.white,
                    ),
                  )
                ],
              ),
            ),
          )
        ],
      ),
    );
  }

  _getFlashButton() {
    if (isFlashOn) {
      return IconButton(
        onPressed: () {
          setState(() {
            isFlashOn = false;
          });
        },
        icon: const Icon(
          Icons.flash_off,
          size: 35,
          color: Colors.white,
        ),
      );
    }
    return IconButton(
      onPressed: () {
        setState(() {
          isFlashOn = true;
        });
      },
      icon: const Icon(
        Icons.flash_on,
        size: 35,
        color: Colors.white,
      ),
    );
  }

  void _loadHtmlFromAssets() {
    webviewController!.loadContent('assets/barcode/barcode.html',
        sourceType: SourceType.html, fromAssets: true);
  }
}
