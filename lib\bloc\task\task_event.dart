part of 'task_bloc.dart';

@immutable
abstract class TaskEvent {}

class CallBarCodeApiForTask extends TaskEvent {
  final String barcodeNumber;
  final double? latitude;
  final bool? isTimedService;
  final double? longitude;
  CallBarCodeApiForTask(
      {required this.barcodeNumber,
      required this.isTimedService,
      this.latitude,
      this.longitude});
}

class ResetBarCodeApiForTask extends TaskEvent {}

class SaveTaskinDataBase extends TaskEvent {
  final Task task;
  SaveTaskinDataBase({required this.task});
}

class ResetSaveTask extends TaskEvent {}
