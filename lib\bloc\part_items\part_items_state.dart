part of 'part_items_bloc.dart';

@immutable
abstract class PartItemsState {}

class PartItemsInitial extends PartItemsState {}

class FetchingPartItemsList extends PartItemsState {
  final List<PartItemResponse> oldList;
  final bool isFirstFetch;

  FetchingPartItemsList(this.oldList, {this.isFirstFetch = false});
}

class FetchedPartItems extends PartItemsState {
  final List<PartItemResponse> partItemList;
  FetchedPartItems(this.partItemList);
}

class PartItemsError extends PartItemsState {
  final String errorMessage;
  PartItemsError({required this.errorMessage});
}

class FetchingBOMPartItemList extends PartItemsState {
  FetchingBOMPartItemList();
}

class FetchedBOMPartItemList extends PartItemsState {
  final List<PartItemResponse> partItemList;
  FetchedBOMPartItemList(this.partItemList);
}

class BOMPartItemListError extends PartItemsState {
  final String errorMessage;
  BOMPartItemListError({this.errorMessage = ''});
}

class FetchingAvailablePartList extends PartItemsState {
  FetchingAvailablePartList();
}

class FetchedAvailablePartList extends PartItemsState {
  final List<AvailablePart> partList;
  FetchedAvailablePartList(this.partList);
}

class FetchAvailablePartListError extends PartItemsState {
  final String errorMessage;
  FetchAvailablePartListError({this.errorMessage = ''});
}
