import 'dart:async';

import 'package:alink/data/dao/audit_status_dao.dart';
import 'package:alink/database/database.dart';
import 'package:alink/logger/logger.dart';
import 'package:bloc/bloc.dart';
import 'package:location/location.dart';
import 'package:meta/meta.dart';

import '../../data/model/audit_response.dart';
import '../../data/repository/api_repository.dart';
import '../../util/app_constant.dart';

part 'audit_event.dart';
part 'audit_state.dart';

class AuditBloc extends Bloc<AuditEvent, AuditState> {
  final ApiRepository apiRepository;
  final Database database;
  static int equipmentTotalCount = 0;
  static int equipmentCount = 0;
  List<AuditEquipment> auditEquipment = [];
  List<String> unscannedBarcodeList = [];
  static int? auditId;
  static Map<String, dynamic>? auditLocation;
  static String? location;

  AuditBloc({required this.apiRepository, required this.database})
      : super(AuditListDataInitial());
  int offset = 0;

  @override
  Stream<AuditState> mapEventToState(
    AuditEvent event,
  ) async* {
    if (event is FetchAuditList) {
      print('================CALLED======================');
      if (state is FetchingAuditListData) return;

      final currentState = state;

      List<Audit>? oldPosts = <Audit>[];
      if (currentState is FetchedAuditListData) {
        oldPosts = currentState.auditResponse.auditList;
        if (event.refresh == true) {
          oldPosts = [];
        }
      }
      if (event.refresh == true) {
        offset = 0;
      }

      emit(FetchingAuditListData(oldPosts, isFirstFetch: offset == 0));
      apiRepository
          .getAuditListByLocationId(
              offset,
              event.location!,
              event.auditId,
              event.serviceType!,
              event.description == null ? "" : event.description!)
          .then((response) async {
        offset += AppConstant.LIMIT;

        AuditStatusDao auditStatusDao = AuditStatusDao(database);

        for (var audit in response.auditList) {
          if (audit.results != null) {
            // Collect equipment IDs with status "Scanned" from results
            final scannedEquipmentIds = audit.results!
                .where((result) => result.status == "Scanned")
                .map((result) => result.equipmentId)
                .toSet();

            // Update the status of only matching equipment in auditEquipmentList
            for (var equipment in audit.auditEquipmentList) {
              if (scannedEquipmentIds.contains(equipment.equipmentId) &&
                  equipment.status != "SCANNED") {
                equipment.status = "Scanned";
              }
            }

            // Call your function to store this modified audit in the local database
            await auditStatusDao.saveEquipmentData(
                audit.auditId, audit.auditEquipmentList);
          }
        }

        final posts = (state as FetchingAuditListData).oldList;
        if (response is AuditResponse) {
          posts.addAll(response.auditList);
          response.auditList = posts;
          emit(FetchedAuditListData(response));
        } else {
          emit(FetchAuditListDataError(errorMessage: response.toString()));
        }
      }).catchError((onError) {
        Logger.e("Error in AuditBloc: $onError");
        emit(FetchAuditListDataError(errorMessage: onError.toString()));
      });
    }
    if (event is ResetAuditListData) {
      offset = 0;
      emit(AuditListDataInitial());
    }
    if (event is SubmitAuditList) {
      emit(SubmittingAuditListData(isSaveAudit: event.isSaveAudit!));
      try {
        var response = await apiRepository.submitAudit(
            event.equipmentList,
            event.auditId,
            event.locationData,
            event.isConditionAudit,
            event.isSaveAudit);
        if (response is String) {
          if (response == "SAVED") {
            emit(SubmittedAuditListData(
                auditId: event.auditId, isSaveAudit: event.isSaveAudit!));
          } else {
            emit(SubmitAuditListDataError(errorMessage: response));
          }
        } else {
          emit(SubmitAuditListDataError(errorMessage: response));
        }
      } catch (error) {
        emit(SubmitAuditListDataError(errorMessage: error.toString()));
      }
    }
/*    if (event is FetchLopaAuditList) {
      emit(FetchingLopaAuditList());

      apiRepository.fetchLopaAuditList(event.query).then((response) {
        if (response is List<Audit>) {
          response.insert(0, defaultAudit());
          emit(FetchedLopaAuditList(response));
        } else {
          emit(FetchedLopaAuditServiceError(errorMessage: response));
        }
      }).catchError((onError) {
        emit(FetchedLopaAuditServiceError(errorMessage: onError));
      });
    }*/
  }

  submitAudit(
      List<AuditEquipment>? equipmentList,
      int auditId,
      LocationData? locationData,
      bool isConditionAudit,
      bool? isSaveAudit) async {
    emit(SubmittingAuditListData());
    try {
      var response = await apiRepository.submitAudit(
          equipmentList, auditId, locationData, isConditionAudit, isSaveAudit);
      if (response is String) {
        if (response == "SAVED") {
          emit(SubmittedAuditListData(auditId: auditId));
        } else {
          emit(SubmitAuditListDataError(errorMessage: response));
        }
      } else {
        emit(SubmitAuditListDataError(errorMessage: response));
      }
    } catch (error) {
      emit(FetchAuditListDataError(errorMessage: error.toString()));
    }
  }
}
