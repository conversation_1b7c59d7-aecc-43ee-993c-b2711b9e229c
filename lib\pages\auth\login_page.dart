import 'dart:async';
import 'dart:convert';

import 'package:alink/bloc/user_bloc.dart';
import 'package:alink/cubit/pages/login/forgot_password_cubit.dart';
import 'package:alink/cubit/pages/login/login_cubit.dart';
import 'package:alink/data/model/customer.dart';
import 'package:alink/database/database.dart';
import 'package:alink/pages/dashboard_page.dart';
import 'package:alink/pages/onboard_page.dart';
import 'package:alink/service_locator.dart';
import 'package:alink/util/app_color.dart';
import 'package:alink/util/app_constant.dart';
import 'package:alink/util/application_util.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:local_auth/error_codes.dart' as auth_error;
import 'package:local_auth/local_auth.dart';
import 'package:path_provider/path_provider.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../logger/logger.dart';
import '../../provider/locationProvider.dart';

class LoginPage extends StatefulWidget {
  static const routeName = "login";
  final bool isRestarted;
  const LoginPage({this.isRestarted = false, Key? key}) : super(key: key);

  @override
  _LoginPageState createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  late TextEditingController _customerPasswordController;
  late TextEditingController _customerEmailController;
  String username = "";
  var localAuth = LocalAuthentication();
  Logger logger = Logger();
  double logoHeight = 70;
  double logoWidth = 300;
  late Timer _timer;

  @override
  void initState() {
    _customerPasswordController = TextEditingController();
    _customerEmailController = TextEditingController();
    if (getIt<SharedPreferences>().getString('API_URL') != null) {
      AppConstant.BASE_URL = getIt<SharedPreferences>().getString('API_URL')!;
    } else {
      if (kIsWeb) {
        _setUrlForWeb();
      } else {
        AppConstant.BASE_URL = "https://api.alinkhub.com";
      }
    }
    checkUserLoggedINOrNot();
    super.initState();
  }

  Database get db => RepositoryProvider.of<Database>(context);
  UserBloc get userBloc => BlocProvider.of<UserBloc>(context, listen: false);
  LoginCubit get loginCubic =>
      BlocProvider.of<LoginCubit>(context, listen: false);
  ForgotPasswordCubit get forgotPwdCubit =>
      BlocProvider.of<ForgotPasswordCubit>(context, listen: false);

  @override
  void dispose() {
    _customerPasswordController.dispose();
    _customerEmailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
//    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle.dark); // 1
    return BlocConsumer<UserBloc, UserState>(
      listener: (context, state) async {
        if (state is UserLoggedIn) {
          ///if user logout, dont show local auth
          bool? isLoggedIn = getIt<SharedPreferences>().getBool('isLoggedIn');
          if (isLoggedIn != null && isLoggedIn == true) {
            if (!kIsWeb && !widget.isRestarted) {
              bool canCheckBiometrics = await localAuth.canCheckBiometrics;
              if (canCheckBiometrics) {
                try {
                  bool didAuthenticate = await localAuth.authenticate(
                      localizedReason: AppLocalizations.of(context)!
                          .pleaseAuthenticateToUseApp);
                  if (didAuthenticate) {
                    Provider.of<LocationProvider>(context, listen: false);
                    Navigator.pushReplacementNamed(
                        context, DashboardPage.routeName);
                  }
                } on PlatformException catch (e) {
                  /*ApplicationUtil.showToast(
                  msg: 'Too many attempt,Please try again later');*/
                  if (e.code == auth_error.lockedOut) {
                    ApplicationUtil.closeApplication();
                  }
                } catch (e) {
                  Logger.e(e.toString());
                }
              }
            }
          }
        }
      },
      builder: (context, state) {
        return GestureDetector(
          onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
          child: Scaffold(
            body: SafeArea(
              child: Center(
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 10),
                  height: double.infinity,
                  decoration:
                      kIsWeb ? _getBoxDecorationForWeb() : BoxDecoration(),
                  constraints: const BoxConstraints(maxWidth: 500),
                  child: SingleChildScrollView(
                    child: Center(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          _buildLoginImageAndBackground(),
                          _getAppLogo(),
                          const SizedBox(
                            height: 20,
                          ),
                          _loginForm()
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  _loginForm() {
    return BlocBuilder<UserBloc, UserState>(
      builder: (context, state) {
        bool isFetching = state is UserFetching;
        if (state is UserLoggedIn) {
          _customerEmailController.text = state.userData.EMAIL ?? '';
          username =
              state.userData.FIRST_NAME ?? state.userData.LAST_NAME ?? '';
        }
        return Wrap(
          children: [
            Container(
              alignment: Alignment.centerLeft,
              constraints: const BoxConstraints(maxWidth: 500),
              margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 0),
              child: _getLoginLabel(
                  _customerEmailController.text, username, state),
            ),
            _getCustomerEmailField(
                isFetching, _customerEmailController.text, state),
            _getCustomerPasswordField(),
            _getLoginButton(),
            _forgetPasswordLink()
          ],
        );
      },
    );
  }

  _logInImage() => Center(
        child: Container(
          constraints: const BoxConstraints(maxHeight: 250),
          margin: const EdgeInsets.symmetric(horizontal: 8),
          child: Image.asset('assets/images/login.png'),
        ),
      );

  _loginImageBackground() => Center(
        child: Container(
          height: 230,
          constraints: const BoxConstraints(maxWidth: 500),
          margin: const EdgeInsets.only(top: 50),
          decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withAlpha(40),
              borderRadius: const BorderRadius.all(Radius.circular(30))),
        ),
      );

  _buildLoginImageAndBackground() => Hero(
        tag: 'icon',
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
          child: Stack(
            children: <Widget>[
              _loginImageBackground(),
              _logInImage(),
            ],
          ),
        ),
      );

  _getAppLogo() => Container(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Image.asset(
          "assets/images/alink_logo.png",
          width: logoWidth,
          height: logoHeight,
        ),
      );

  _getBoxDecorationForWeb() => BoxDecoration(
        border: Border.all(
          color: Theme.of(context).primaryColorLight,
        ),
        borderRadius: const BorderRadius.all(
          Radius.circular(10),
        ),
      );

  _getLoginLabel(String email, String username, UserState state) {
    //if user is login or userId is not null
    if (state is UserLoggedIn ||
        getIt<SharedPreferences>().getInt('userId') != null) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Center(
            child: RichText(
              textAlign: TextAlign.start,
              text: TextSpan(
                  style: const TextStyle(fontSize: 20),
                  children: <TextSpan>[
                    TextSpan(
                      text: AppLocalizations.of(context)!.welcomeBack + ' ',
                      style: const TextStyle(
                          color: AppColor.greyTextColor,
                          fontWeight: FontWeight.bold),
                    ),
                    TextSpan(
                        text: username + '!',
                        style: TextStyle(
                            color: Theme.of(context).primaryColor,
                            fontWeight: FontWeight.bold)),
                  ]),
            ),
          ),
          const SizedBox(
            height: 10,
          ),
          Text(
            AppLocalizations.of(context)!.youAreLogInAs,
            style: const TextStyle(color: AppColor.greyTextColor, fontSize: 18),
          ),
          const SizedBox(
            height: 5,
          ),
          Text(
            email,
            style: TextStyle(
              color: Theme.of(context).primaryColor,
              fontWeight: FontWeight.bold,
              fontSize: 18,
            ),
          ),
          const SizedBox(
            height: 10,
          ),
        ],
      );
    }
    return RichText(
      textAlign: TextAlign.start,
      text: TextSpan(style: const TextStyle(fontSize: 18), children: <TextSpan>[
        TextSpan(
            text: AppLocalizations.of(context)!.enter,
            style: TextStyle(color: Theme.of(context).primaryColor)),
        TextSpan(
            text: " " + AppLocalizations.of(context)!.emailAndPassword + " ",
            style: TextStyle(
                color: Theme.of(context).primaryColor,
                fontWeight: FontWeight.bold)),
        TextSpan(
          text: AppLocalizations.of(context)!.below,
          style: TextStyle(color: Theme.of(context).primaryColor),
        ),
      ]),
    );
  }

  _getCustomerPasswordField() => Column(
        children: [
          Container(
            constraints: const BoxConstraints(maxWidth: 500),
            margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
            child: Builder(
              builder: (context) {
                final logInCubic = context.watch<LoginCubit>().state;
                final userBloc = context.watch<UserBloc>().state;
                bool isShowPassword = logInCubic is ShowPassword;
                bool isFetching = userBloc is UserFetching;

                return TextFormField(
                  style: const TextStyle(height: 1.2),
                  enabled: !isFetching,
                  keyboardType: TextInputType.text,
                  textInputAction: TextInputAction.done,
                  controller: _customerPasswordController,
                  obscureText:
                      !isShowPassword, //This will obscure text dynamically
                  decoration: InputDecoration(
                    contentPadding: const EdgeInsets.symmetric(
                        vertical: 16, horizontal: 10),

                    border: OutlineInputBorder(
                      borderSide:
                          const BorderSide(color: Colors.white, width: 2.0),
                      borderRadius: BorderRadius.circular(10.0),
                    ),
                    labelText: AppLocalizations.of(context)!.password,
                    hintText: AppLocalizations.of(context)!.enterPassword,
                    // Here is key idea
                    suffixIcon: IconButton(
                      icon: Icon(
                        // Based on passwordVisible state choose the icon
                        !isShowPassword
                            ? Icons.visibility
                            : Icons.visibility_off,
                        color: Theme.of(context).primaryColorDark,
                      ),
                      onPressed: () {
                        loginCubic.updatePasswordView();
                      },
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      );

  _getCustomerEmailField(bool isFetching, String email, UserState state) {
    //show sizebox instead of textfield if user id is not null
    if (getIt<SharedPreferences>().getInt('userId') != null) {
      return const SizedBox();
    } else if (email.isEmpty || state is UserNotLogIn || state is UserError) {
      return Container(
        constraints: const BoxConstraints(maxWidth: 500),
        margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
        child: TextFormField(
          style: const TextStyle(height: 1.2),
          cursorHeight: 18,
          enabled: !isFetching,
          keyboardType: TextInputType.text,
          textInputAction: TextInputAction.next,
          controller: _customerEmailController,
          decoration: InputDecoration(
            contentPadding:
                const EdgeInsets.symmetric(vertical: 16, horizontal: 10),
            border: OutlineInputBorder(
              borderSide: const BorderSide(color: Colors.white, width: 1.0),
              borderRadius: BorderRadius.circular(10.0),
            ),
            labelText: AppLocalizations.of(context)!.email,
            hintText: AppLocalizations.of(context)!.enterCustomerEmail,
            suffixIcon: IconButton(
              icon: Icon(
                Icons.email,
                color: Theme.of(context).primaryColorDark,
              ),
              onPressed: null,
            ),
          ),
        ),
      );
    } else {
      return const SizedBox();
    }
  }

  _getLoginButton() => BlocConsumer<UserBloc, UserState>(
        listener: (context, state) {
          if (state is UserFetched) {
            Provider.of<LocationProvider>(context, listen: false);
            Logger.i(
                '${ApplicationUtil.getFormattedCurrentDateAndTime()} Login Successful!');

            if (state.userData is UserData) {
              if (kIsWeb) {
                Navigator.pushReplacementNamed(
                    context, DashboardPage.routeName);
              } else {
                if (getIt<SharedPreferences>().getBool('showOnBoard') != null) {
                  Provider.of<LocationProvider>(context, listen: false);
                  Navigator.pushReplacementNamed(
                      context, DashboardPage.routeName);
                } else {
                  getIt<SharedPreferences>().setBool('showOnBoard', false);
                  Navigator.pushReplacementNamed(
                      context, OnBoardingPage.routeName);
                }
              }
            } else if (state.userData is List<Customer>) {
              //if user has multiple customer, show dialog
              ///Check if user already login with customer selected
              bool? isLoggedIn =
                  getIt<SharedPreferences>().getBool('isLoggedIn');
              if (isLoggedIn != null && isLoggedIn == true) {
                int? customerId =
                    getIt<SharedPreferences>().getInt('customerId');
                if (customerId != null) {
                  userBloc.add(FetchUser(_customerEmailController.text,
                      _customerPasswordController.text, customerId));
                }
              } else {
                /// If user logout or login for frst time show list dialog
                showCustomerListDialog(state.userData as List<Customer>);
              }
            }
          }
          if (state is UserError) {
            if (state.error == 'MULTIPLE USER LOGIN') {
              showResetAlertDialog(context);
            } else {
              ScaffoldMessenger.of(context)
                  .showSnackBar(SnackBar(content: Text(state.error)));
            }
          }
        },
        builder: (context, state) {
          if (state is UserFetching) {
            return const Center(child: CircularProgressIndicator());
          }
          return Container(
            constraints: const BoxConstraints(maxWidth: 500),
            margin: const EdgeInsets.only(left: 20, right: 20, top: 10),
            child: GestureDetector(
              onPanCancel: () => _timer.cancel(),
              onPanDown: (details) {
                _timer =
                    Timer(const Duration(seconds: 1, milliseconds: 200), () {
                  // time duration
                  if (state is! UserLoggedIn ||
                      getIt<SharedPreferences>().getInt('userId') == null) {
                    showDialog(
                        context: context,
                        builder: (BuildContext context) {
                          if (getIt<SharedPreferences>().getString('API_URL') !=
                              null) {
                            AppConstant.baseUrlList.forEach((element) {
                              if (element.url ==
                                  getIt<SharedPreferences>()
                                      .getString('API_URL')) {
                                element.isActive = true;
                              } else {
                                element.isActive = false;
                              }
                            });
                          }

                          return Container(
                            constraints: const BoxConstraints(maxWidth: 500),
                            child: Dialog(
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(10),
                              ),
                              elevation: 0,
                              backgroundColor: Colors.transparent,
                              child: contentBox(context),
                            ),
                          );
                        });
                  }
                });
              },
              child: ElevatedButton(
                style: ButtonStyle(
                  backgroundColor:
                      MaterialStateProperty.all(Theme.of(context).primaryColor),
                  shape: MaterialStateProperty.all(
                    const RoundedRectangleBorder(
                      borderRadius: BorderRadius.all(
                        Radius.circular(10),
                      ),
                    ),
                  ),
                ),
                onPressed: () {
                  userBloc.add(FetchUser(_customerEmailController.text,
                      _customerPasswordController.text, -1));

                  /* Navigator.push(
                  context, SlightLeftRoute(page: PasswordConfirmPage()));*/
                },
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: <Widget>[
                      Text(
                        AppLocalizations.of(context)!.login,
                        style: const TextStyle(color: Colors.white),
                      ),
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          borderRadius:
                              const BorderRadius.all(Radius.circular(20)),
                          color: Theme.of(context).colorScheme.secondary,
                        ),
                        child: const FaIcon(
                          FontAwesomeIcons.chevronRight,
                          color: Colors.white,
                          size: 16,
                        ),
                      )
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      );

  Future<void> checkUserLoggedINOrNot() async {
    if (!kIsWeb) {
      var directory = await getApplicationDocumentsDirectory();
      Logger.initialize(directory.path);
      Logger.setLogLevel(LogLevel.debug);
    }
    userBloc.add(CheckUserLoggedIn());
  }

  _forgetPasswordLink() {
    return BlocBuilder<ForgotPasswordCubit, ForgotPasswordState>(
      builder: (context, state) {
        print(state);
        if (state is SentResetPasswordMail) {
          return Container(
            constraints: const BoxConstraints(maxWidth: 500),
            margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
            child: Text(
              state.msg,
              textAlign: TextAlign.center,
              style:
                  const TextStyle(fontSize: 16, color: AppColor.greenSentColor),
            ),
          );
        } else if (state is SendingResetPasswordMail) {
          return Container(
            padding: const EdgeInsets.all(10),
            child: const Center(
              child: CircularProgressIndicator(),
            ),
          );
        } else if (state is ResetPasswordError) {
          return Column(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              _forgotPasswordButton(),
              Container(
                constraints: const BoxConstraints(maxWidth: 500),
                margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 0),
                child: Center(
                  child: Text(
                    state.errorMessage,
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                        fontSize: 18,
                        letterSpacing: 1,
                        color: AppColor.redColor),
                  ),
                ),
              ),
            ],
          );
        } else if (state is ForgotPasswordInitial) {
          return _forgotPasswordButton();
        }

        return _forgotPasswordButton();
      },
    );
  }

  _forgotPasswordButton() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Container(
            constraints: const BoxConstraints(maxWidth: 500),
            margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 0),
            child: TextButton(
              onPressed: () {
                String email = _customerEmailController.text;
                if (email.trim().isNotEmpty) {
                  forgotPwdCubit.forgotPassword(email, context);
                } else {
                  ApplicationUtil.showSnackBar(
                      context: context,
                      message: AppLocalizations.of(context)!.emailRequired);
                }
              },
              child: Text(
                AppLocalizations.of(context)!.forgotPassword,
                style: TextStyle(
                    color: Theme.of(context).primaryColor, fontSize: 14),
              ),
            )),
      ],
    );
  }

  void showCustomerListDialog(List<Customer> customerList) {
    AlertDialog alert = AlertDialog(
      title: Text(AppLocalizations.of(context)!.selectCustomer),
      content: Container(
        width: double.maxFinite,
        constraints: const BoxConstraints(maxWidth: 500),
        child: ListView.builder(
          shrinkWrap: true,
          itemCount: customerList.length,
          itemBuilder: (context, index) {
            Customer customer = customerList[index];
            return ListTile(
              onTap: () async {
                Navigator.pop(context);
                await getIt<SharedPreferences>()
                    .setInt('customerId', customer.customerId);
                userBloc.add(FetchUser(_customerEmailController.text,
                    _customerPasswordController.text, customer.customerId));
              },
              leading: FaIcon(
                FontAwesomeIcons.solidBuilding,
                color: Theme.of(context).primaryColor,
              ),
              title: Text(
                customer.customerName,
                textAlign: TextAlign.left,
                style: const TextStyle(
                    fontSize: 16,
                    letterSpacing: 1,
                    wordSpacing: 1,
                    height: 1.2),
              ),
              /*subtitle: Text(
                customer.customerId.toString(),
                textAlign: TextAlign.left,
              ),*/
            );
          },
        ),
      ),
    );
    showDialog(
      barrierDismissible: false,
      context: context,
      builder: (BuildContext context) {
        return alert;
      },
    );
  }

  contentBox(context) {
    return Stack(
      alignment: Alignment.topRight,
      children: <Widget>[
        Container(
          constraints: const BoxConstraints(maxWidth: 500),
          //width: MediaQuery.of(context).size.width,
          padding: const EdgeInsets.only(
              left: 20,
              top: 20,
              //top: avatarRadius + padding,
              right: 20,
              bottom: 0),
          //margin: EdgeInsets.only(top: avatarRadius),
          decoration: BoxDecoration(
              shape: BoxShape.rectangle,
              color: Colors.white,
              borderRadius: BorderRadius.circular(15),
              boxShadow: const [
                BoxShadow(
                    color: Colors.black, offset: Offset(0, 10), blurRadius: 10),
              ]),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              const SizedBox(
                height: 10,
              ),
              Text(
                AppLocalizations.of(context)!.selectAPI,
                style:
                    const TextStyle(fontSize: 22, fontWeight: FontWeight.w600),
              ),
              const Divider(),
              ListView.builder(
                shrinkWrap: true,
                itemCount: AppConstant.baseUrlList.length,
                itemBuilder: (context, index) {
                  AppUrl appUrl = AppConstant.baseUrlList[index];
                  return InkWell(
                    onTap: () {
                      AppConstant.BASE_URL = appUrl.url;
                      getIt<SharedPreferences>()
                          .setString('API_URL', appUrl.url);
                      Navigator.pop(context);
                    },
                    child: ListTile(
                      contentPadding: EdgeInsets.zero,
                      visualDensity:
                          const VisualDensity(horizontal: 0, vertical: -4),
                      //leading: Icon(widget.seatItemList[index].icon),
                      subtitle: Text(
                        appUrl.url,
                        style: TextStyle(color: Colors.blue[800], fontSize: 16),
                      ),
                      title: Text(
                        appUrl.description,
                        style: const TextStyle(fontSize: 18),
                      ),
                      trailing: appUrl.isActive
                          ? Icon(
                              Icons.check_box,
                              color: Theme.of(context).primaryColor,
                            )
                          : Icon(
                              Icons.check_box_outline_blank,
                              color: Theme.of(context).primaryColor,
                            ),
                    ),
                  );
                },
              ),
              const SizedBox(
                height: 10,
              ),
              const Divider(),
            ],
          ),
        ),
        IconButton(
          onPressed: () {
            Navigator.pop(context);
          },
          icon: const FaIcon(FontAwesomeIcons.times),
        ),
      ],
    );
  }

  Future<void> _setUrlForWeb() async {
    print('SETTING BROWSER URL');
    try {
      final contents = await rootBundle.loadString(
        'assets/config.json',
      );
      Map<String, dynamic> map = jsonDecode(contents);
      print('MAP DATA:' + json.encode(map));
      if (map.containsKey('apiUrl')) {
        AppConstant.BASE_URL = map['apiUrl'];
        getIt<SharedPreferences>().setString('API_URL', map['apiUrl']);
      } else {
        print('API URL IS NOT PRESENT');
        AppConstant.BASE_URL = "https://api.alinkhub.com";
      }
      print('URL SET UP SUCCESS: ' + AppConstant.BASE_URL);
    } catch (e) {
      AppConstant.BASE_URL = "https://api.alinkhub.com";
      print('ERROR OCCUR WHILE SETTING CONFIG>JSON');
      print(e.toString());
      //log(e.toString());
    }
  }

  void showResetAlertDialog(BuildContext ctx) {
    AlertDialog alert = AlertDialog(
      title: Text(AppLocalizations.of(context)!.alert),
      content: Text(
        AppLocalizations.of(context)!.thisEmailIsAlreadyUsedInOtherLandscape,
        textAlign: TextAlign.left,
        style: const TextStyle(
            fontSize: 15, letterSpacing: 1, wordSpacing: 1, height: 1.2),
      ),
      actions: [
        ElevatedButton(
          style: ButtonStyle(
              backgroundColor: MaterialStateProperty.all(AppColor.redColor)),
          child: Text(AppLocalizations.of(context)!.reset),
          onPressed: () async {
            if (!kIsWeb) {
              try {
                Logger.i('=====DELETED IF DATA SYNC EXIST=====');

                ///DELETING DATABASE
                db.deleteEverything().then((value) {
                  Navigator.pop(ctx);
                  Logger.i('DELETED DATABASE');

                  ///DELETTING PREFERENCES
                  getIt<SharedPreferences>().clear();
                  Logger.i('CLEAR SHARED PREFERENCES');

                  Logger.i('SHOW DIALOG');
                  //
                  showDialog(
                    barrierDismissible: false,
                    context: ctx,
                    builder: (context) {
                      return WillPopScope(
                        onWillPop: () async {
                          return false;
                        },
                        child: AlertDialog(
                          title: Text(
                              AppLocalizations.of(context)!.restartApplication),
                          content: Text(
                            AppLocalizations.of(context)!.deleteAllDataFromApp,
                            textAlign: TextAlign.left,
                            style:
                                const TextStyle(fontSize: 15, letterSpacing: 1),
                          ),
                        ),
                      );
                    },
                  );
                });
              } catch (e) {
                Logger.e(e.toString());
                print(e.toString());
              }
            } else {
              getIt<SharedPreferences>().clear().then((value) {
                db.deleteEverything().then((value) => {
                      Navigator.pushNamedAndRemoveUntil(
                          context, LoginPage.routeName, (route) => false)
                    });
              });
            }
          },
        ),
        ElevatedButton(
          child: Text(AppLocalizations.of(context)!.cancel),
          onPressed: () {
            Navigator.of(ctx).pop();
          },
        ),
      ],
    );
    showDialog(
      barrierDismissible: false,
      context: ctx,
      builder: (BuildContext context) {
        return alert;
      },
    );
  }
}
