import 'dart:async';

import 'package:alink/data/model/available_part.dart';
import 'package:alink/data/model/part_item_response.dart';
import 'package:alink/data/repository/api_repository.dart';
import 'package:alink/util/app_constant.dart';
import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';

part 'part_items_event.dart';
part 'part_items_state.dart';

class PartItemsBloc extends Bloc<PartItemsEvent, PartItemsState> {
  final ApiRepository apiRepository;

  PartItemsBloc({required this.apiRepository}) : super(PartItemsInitial());
  int offset = 0;
  String query = "";

  @override
  Stream<PartItemsState> mapEventToState(
    PartItemsEvent event,
  ) async* {
    List<PartItemResponse> backupList = [];
    if (event is FetchPartItemList) {
      if (state is FetchingPartItemsList) return;

      final currentState = state;

      var oldPosts = <PartItemResponse>[];
      if (currentState is FetchedPartItems) {
        oldPosts = currentState.partItemList;
        if (event.refresh == true) {
          oldPosts = [];
        }
      }
      if (event.refresh == true) {
        offset = 0;
        query = event.query;
      }

      emit(FetchingPartItemsList(oldPosts, isFirstFetch: offset == 0));
      print('event.partId');
      print(event.partId);

      apiRepository
          .fetchPartItemList(
        offset,
        query,
        event.partId,
      )
          .then((response) {
        offset += AppConstant.LIMIT;

        final posts = (state as FetchingPartItemsList).oldList;
        backupList = posts;
        if (response is List<PartItemResponse>) {
          posts.addAll(response);
          emit(FetchedPartItems(posts));
        } else {
          emit(PartItemsError(errorMessage: response.toString()));
        }
      }).catchError((onError) {
        emit(PartItemsError(errorMessage: onError.toString()));
      });
    }
    if (event is FetchBOMPartList) {
      yield FetchingBOMPartItemList();
      var response = await apiRepository.fetchBOMPartItemList();
      if (response is List<PartItemResponse>) {
        yield FetchedBOMPartItemList(response);
      } else if (response is String) {
        yield BOMPartItemListError(errorMessage: response);
      }
    }
    if (event is FetchAvailablePartList) {
      yield FetchingAvailablePartList();
      var response = await apiRepository.fetchAvailablePartList(event.query);
      yield FetchedAvailablePartList(response);
      if (response is List<AvailablePart>) {
        yield FetchedAvailablePartList(response);
      } else if (response is String) {
        yield FetchAvailablePartListError(errorMessage: response);
      }
    }
  }
}
