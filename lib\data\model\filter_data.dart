import 'package:alink/data/model/location_detail.dart';
import 'package:equatable/equatable.dart';

class FilterData {
  List<UnitType> unitTypeList;
  List<LocationDetail> locationList;

  FilterData({
    this.locationList = const [],
    this.unitTypeList = const [],
  });

  Map<String, dynamic> toMap() {
    return {
      'UNIT_TYPES': unitTypeList,
      'locationList': locationList,
    };
  }

  factory FilterData.fromJson(
      Map<String, dynamic> map, List<LocationDetail> locationList) {
    List<UnitType> unitTypeList = [];
    if (map['UNIT_TYPES'] != null) {
      map['UNIT_TYPES'].forEach((v) {
        unitTypeList.add(UnitType.fromJson(v));
      });
    }
    return FilterData(
      unitTypeList: unitTypeList,
      locationList: locationList,
    );
  }
}

class UnitType extends Equatable {
  String? categoryId;
  int? customerId;
  String? name;
  String? description;

  UnitType(
      {required this.categoryId,
      required this.customerId,
      required this.name,
      this.description});

  UnitType.fromJson(Map<String, dynamic> json) {
    categoryId = json['CATEGORY_ID'];
    customerId = json['CUSTOMER_ID'];
    name = json['NAME'];
    description = json['DESCRIPTION'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['CATEGORY_ID'] = categoryId;
    data['CUSTOMER_ID'] = customerId;
    data['NAME'] = name;
    data['DESCRIPTION'] = description;
    return data;
  }

  @override
  List<Object?> get props => [categoryId, customerId, name, description];
}

class TaskType extends Equatable {
  String? choiceName;
  String? choiceValue;

  TaskType({required this.choiceName, required this.choiceValue});

  TaskType.fromJson(Map<String, dynamic> json) {
    choiceName = json['CHOICE_NAME'];
    choiceValue = json['CHOICE_VALUE'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['CHOICE_NAME'] = choiceName;
    data['CHOICE_VALUE'] = choiceValue;
    return data;
  }

  @override
  List<Object?> get props => [choiceName, choiceValue];
}

class InMemoryFilterData {
  static String? location;
  static UnitType? unitType;
  static TaskType? taskType;
  static String? barcodeNumber;
  static String? partNo;
  static bool safety = false;

  static void clear() {
    location = null;
    taskType = null;
    unitType = null;
    barcodeNumber = null;
    partNo = null;
    safety = false;
  }
}
