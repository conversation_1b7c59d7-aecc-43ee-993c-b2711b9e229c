import 'package:alink/data/model/audit_response.dart';
import 'package:alink/data/model/audit_status.dart';
import 'package:alink/database/database.dart';
import 'package:drift/drift.dart';

import '../../logger/logger.dart';

part 'audit_status_dao.g.dart';

@DriftAccessor(
  tables: [AuditStatus],
)
class AuditStatusDao extends DatabaseAccessor<Database>
    with _$AuditStatusDaoMixin {
  final Database db;
  Logger logger = Logger();

  AuditStatusDao(this.db) : super(db);

  ///save scanned barcode to database (new equipment and existing equipment)
  updateAuditData(
      int auditId,
      String barcodeNumber,
      bool? isActiveEquipment,
      String? oldLocationId,
      String? newLocationId,
      List<AuditEquipment> auditEquipmentList,
      {double? latitude,
      double? longitude}) async {
    //AuditStatusData(auditId: 12, equipmentData: '')
    try {
      Logger.i("calling columnExists method");
      //addColumnIfNotExists("SCANNED_DATETIME");
      dynamic res;
      if (auditEquipmentList.isEmpty) {
        res = await (select(auditStatus)
              ..where((tbl) => tbl.auditId.equals(auditId)))
            .getSingleOrNull();
      } else {
        res = AuditStatusData(
            auditId: auditId,
            equipmentData: auditEquipmentList.map((e) => e.toJson()).toList());
      }
      if (res != null && res is AuditStatusData) {
        List<AuditEquipment> auditEquipmentList =
            res.equipmentData!.map((e) => AuditEquipment.fromJson(e)).toList();
        if (isActiveEquipment != null || oldLocationId != null) {
          int searchedEquipmentIndex = auditEquipmentList
              .indexWhere((element) => element.tag == barcodeNumber);
          if (isActiveEquipment != null) {
            Logger.i("++isActiveEquipment++");
            Logger.i(isActiveEquipment.toString());
            auditEquipmentList[searchedEquipmentIndex].isInActiveEquipment =
                isActiveEquipment;
            auditEquipmentList[searchedEquipmentIndex].scannedDateTime =
                DateTime.now();
            if (latitude != null && longitude != null) {
              auditEquipmentList[searchedEquipmentIndex].latitude = latitude;
              auditEquipmentList[searchedEquipmentIndex].longitude = longitude;
            }
            Logger.i(
                "+++++++++++++++++${auditEquipmentList[searchedEquipmentIndex]}+++++++++++++++++");
          } else if (oldLocationId != null) {
            Logger.i("++oldLocationId++");
            Logger.i(oldLocationId);
            auditEquipmentList[searchedEquipmentIndex].locationId =
                newLocationId;
            auditEquipmentList[searchedEquipmentIndex].oldLocationId =
                oldLocationId;
            auditEquipmentList[searchedEquipmentIndex].scannedDateTime =
                DateTime.now();
            if (latitude != null && longitude != null) {
              auditEquipmentList[searchedEquipmentIndex].latitude = latitude;
              auditEquipmentList[searchedEquipmentIndex].longitude = longitude;
            }
            Logger.i(
                "---------------------${auditEquipmentList[searchedEquipmentIndex]}---------------------");
          }
        } else {
          int searchedEquipmentIndex = auditEquipmentList.indexWhere(
              (element) =>
                  element.tag == barcodeNumber && element.name != null);
          if (searchedEquipmentIndex >= 0) {
            auditEquipmentList[searchedEquipmentIndex].isScanned = true;
            auditEquipmentList[searchedEquipmentIndex].status = "Scanned";
            auditEquipmentList[searchedEquipmentIndex].isInActiveEquipment =
                false;
            auditEquipmentList[searchedEquipmentIndex].scannedDateTime =
                DateTime.now();
            if (latitude != null && longitude != null) {
              auditEquipmentList[searchedEquipmentIndex].latitude = latitude;
              auditEquipmentList[searchedEquipmentIndex].longitude = longitude;
            }
            Logger.i(
                "=========================${auditEquipmentList[searchedEquipmentIndex]}=========================");
          } else {
            /// check if new equipment already added with barcode
            int searchedEquipmentIndex = auditEquipmentList.indexWhere(
                (element) =>
                    element.tag == barcodeNumber && element.name == null);

            ///if not added <0, than add
            if (searchedEquipmentIndex < 0) {
              AuditEquipment auditEquipment = AuditEquipment(
                  isScanned: true,
                  status: 'Movement',
                  tag: barcodeNumber,
                  scannedDateTime: DateTime.now(),
                  latitude: latitude,
                  longitude: longitude);
              auditEquipmentList.add(auditEquipment);
            }
          }
        }
        Logger.i(
            "audit_status_dao :: updateAuditData :: before updating to DB " +
                auditEquipmentList.length.toString() +
                " and audit ID " +
                auditId.toString());
        AuditStatusData auditStatusData = AuditStatusData(
            auditId: auditId,
            equipmentData: auditEquipmentList.map((v) => v.toJson()).toList());
        Logger.i(
            "---------------------${auditStatusData}---------------------");
        return await update(auditStatus).replace(auditStatusData);
      }
    } catch (e) {
      // Logger.e(e.toString());
      Logger.i("Caught an Exception in updateAuditData");
      Logger.i(e.toString());
    }
  }

  getEquipmentDataFromAudit(int auditId) async {
    //AuditStatusData(auditId: 12, equipmentData: '')
    try {
      Logger.i("calling columnExists method");
      //addColumnIfNotExists("SCANNED_DATETIME");
      dynamic res = await (select(auditStatus)
            ..where((tbl) => tbl.auditId.equals(auditId)))
          .getSingleOrNull();
      return res;
    } catch (e) {
      // Logger.e(e.toString());
      Logger.i(e.toString());
    }
  }

  updateEquipment(int auditId, String barcodeNumber,
      bool? isEquipmentFromConfirmAuditPage) async {
    //AuditStatusData(auditId: 12, equipmentData: '')
    try {
      Logger.i("calling columnExists method");
      //addColumnIfNotExists("SCANNED_DATETIME");
      dynamic res = await (select(auditStatus)
            ..where((tbl) => tbl.auditId.equals(auditId)))
          .getSingleOrNull();

      if (res != null && res is AuditStatusData) {
        List<AuditEquipment> auditEquipmentList =
            res.equipmentData!.map((e) => AuditEquipment.fromJson(e)).toList();
        int searchedEquipmentIndex = auditEquipmentList
            .indexWhere((element) => element.tag == barcodeNumber);
        if (searchedEquipmentIndex >= 0 &&
            auditEquipmentList[searchedEquipmentIndex].equipmentId != null) {
          auditEquipmentList[searchedEquipmentIndex].isScanned = false;
          auditEquipmentList[searchedEquipmentIndex].status = 'Unscanned';
        } else if (searchedEquipmentIndex >= 0 &&
            (auditEquipmentList[searchedEquipmentIndex].equipmentId == null ||
                isEquipmentFromConfirmAuditPage!)) {
          auditEquipmentList.removeAt(searchedEquipmentIndex);
        }
        AuditStatusData auditStatusData = AuditStatusData(
            auditId: auditId,
            equipmentData: auditEquipmentList.map((v) => v.toJson()).toList());
        Logger.i(
            "---------------------${auditStatusData}---------------------");
        return await update(auditStatus).replace(auditStatusData);
      }
      return res;
    } catch (e) {
      // Logger.e(e.toString());
      Logger.i(e.toString());
    }
  }

  updateEquipmentByName(
      int auditId,
      String equipmentName,
      AuditEquipment auditEquipment,
      List<AuditEquipment> auditEquipmentList,
      bool? isActiveEquipment,
      String? oldLocationId) async {
    //AuditStatusData(auditId: 12, equipmentData: '')
    try {
      Logger.i("calling columnExists method");
      //addColumnIfNotExists("SCANNED_DATETIME");
      dynamic auditFromDb;
      if (auditEquipmentList.isEmpty) {
        auditFromDb = await (select(auditStatus)
              ..where((tbl) => tbl.auditId.equals(auditId)))
            .getSingleOrNull();
      } else {
        auditFromDb = AuditStatusData(
            auditId: auditId,
            equipmentData: auditEquipmentList.map((e) => e.toJson()).toList());
      }
      if (auditFromDb != null && auditFromDb is AuditStatusData) {
        List<AuditEquipment> auditEquipmentListDb = auditFromDb.equipmentData!
            .map((e) => AuditEquipment.fromJson(e))
            .toList();
        int searchedEquipmentIndex = auditEquipmentListDb
            .indexWhere((element) => element.name == equipmentName);
        if (searchedEquipmentIndex >= 0) {
          auditEquipmentListDb[searchedEquipmentIndex].isScanned =
              auditEquipment.isScanned;
          //auditEquipmentListDb[searchedEquipmentIndex].status = auditEquipment.isScanned ? 'Scanned' : 'Unscanned';
          auditEquipmentListDb[searchedEquipmentIndex].isInActiveEquipment =
              isActiveEquipment;
          auditEquipmentListDb[searchedEquipmentIndex].imageMapData =
              auditEquipment.imageMapData;
          if (oldLocationId != null) {
            auditEquipmentListDb[searchedEquipmentIndex].oldLocationId =
                oldLocationId;
          }
          // MAP PARTS TO JSON
          auditEquipmentListDb[searchedEquipmentIndex].partsMapData =
              auditEquipment.partsMapData;
          // MAP PART TO JSON
        }

        AuditStatusData auditStatusData = AuditStatusData(
            auditId: auditId,
            equipmentData:
                auditEquipmentListDb.map((v) => v.toJson()).toList());
        Logger.i(
            "---------------------${auditStatusData}---------------------");
        return await update(auditStatus).replace(auditStatusData);
      } else {
        return saveEquipmentData(auditId, auditEquipmentList);
      }
    } catch (e) {
      Logger.i(e.toString());
      Logger.i(e.toString());
    }
  }

  deleteAuditEquipmentByAuditId(int auditId) async {
    //AuditStatusData(auditId: 12, equipmentData: '')
    try {
      dynamic res = await (select(auditStatus)
            ..where((tbl) => tbl.auditId.equals(auditId)))
          .getSingleOrNull();
      if (res != null && res is AuditStatusData) {
        return await delete(auditStatus).delete(res);
      }
      return res;
    } catch (e) {
      // Logger.e(e.toString());
    }
  }

  Future<int> saveEquipmentData(
      int auditId, List<AuditEquipment> newEquipmentList,
      {bool updateRequired = true}) async {
    try {
      Logger.i("calling saveEquipmentData method");
      Logger.i("---------------------$newEquipmentList---------------------");

      // Check if an entry with the same auditId already exists
      final existingEntry = await (select(auditStatus)
            ..where((tbl) => tbl.auditId.equals(auditId)))
          .getSingleOrNull();

      if (existingEntry != null) {
        if (updateRequired) {
          // Load existing equipment data from the database
          List<AuditEquipment> existingEquipmentList = existingEntry
              .equipmentData!
              .map((equipmentJson) => AuditEquipment.fromJson(equipmentJson))
              .toList();

          // Create a map of existing equipment for easy access by equipment ID
          Map<int, AuditEquipment> existingEquipmentMap = {
            for (var equipment in existingEquipmentList)
              equipment.equipmentId!: equipment
          };

          // Update the equipment list with conditions based on status
          List<Map<String, dynamic>> updatedEquipmentData = [];

          for (var newEquipment in newEquipmentList) {
            final existingEquipment =
                existingEquipmentMap[newEquipment.equipmentId];

            if (existingEquipment != null) {
              // Existing equipment found, check statuses
              if (existingEquipment.status == "Scanned") {
                // Do not update this equipment; keep it as is
                updatedEquipmentData.add(existingEquipment.toJson());
              } else if (newEquipment.status == "Scanned") {
                // Update to SCANNED if new equipment has "SCANNED" status
                updatedEquipmentData.add(newEquipment.toJson());
              } else {
                // Keep the existing data if neither has "SCANNED"
                updatedEquipmentData.add(existingEquipment.toJson());
              }
            } else {
              // New equipment that doesn't exist in the DB yet, add it
              updatedEquipmentData.add(newEquipment.toJson());
            }
          }

          // Perform the update with the modified equipment data
          return await (update(auditStatus)
                ..where((tbl) => tbl.auditId.equals(auditId)))
              .write(AuditStatusData(
            auditId: auditId,
            equipmentData: updatedEquipmentData,
          ));
        } else {
          return 0;
        }
      } else {
        // Insert a new entry if no existing data found
        return await into(auditStatus).insert(
          AuditStatusData(
            auditId: auditId,
            equipmentData: newEquipmentList.map((v) => v.toJson()).toList(),
          ),
        );
      }
    } catch (e) {
      Logger.i(e.toString());
      return -1;
    }
  }
}
