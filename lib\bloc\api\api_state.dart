part of 'api_bloc.dart';

@immutable
abstract class ApiState {}

class ApiInitial extends ApiState {}

class BarcodeApiCalling extends ApiState {}

class BarCodeApiInitial extends ApiState {}

class BarcodeApiCalled extends ApiState {
  final BarcodeResponse barcodeResponse;
  bool? isFromMap;
  BarcodeApiCalled(this.barcodeResponse, {this.isFromMap = false});
}

class BarcodeApiError extends ApiState {
  final String errorMessage;
  final String barcodeNumber;
  BarcodeApiError({
    required this.errorMessage,
    required this.barcodeNumber,
  });
}

class CheckBarcodeApiCalling extends ApiState {}

class CheckBarcodeApiInitial extends ApiState {}

class CheckBarcodeApiCalled extends ApiState {
  final BarcodeResponse barcodeResponse;
  CheckBarcodeApiCalled(this.barcodeResponse);
}

class CheckBarcodeApiError extends ApiState {
  final String errorMessage;
  final String barcodeNumber;

  CheckBarcodeApiError({
    required this.errorMessage,
    required this.barcodeNumber,
  });
}

class GettingCustomizationData extends ApiState {}

class CustomizationDataFetched extends ApiState {
  final Customization customization;
  CustomizationDataFetched(this.customization);
}

class CustomizationDataError extends ApiState {
  final String errorMessage;
  CustomizationDataError({required this.errorMessage});
}

class TerminalDataInitial extends ApiState {}

class FetchingTerminalData extends ApiState {}

class FetchedTerminalData extends ApiState {
  final List<Terminal> terminalList;
  FetchedTerminalData(this.terminalList);
}

class ApiError extends ApiState {
  final String errorMessage;
  ApiError({required this.errorMessage});
}

class FetchTerminalError extends ApiState {
  final String errorMessage;
  FetchTerminalError({required this.errorMessage});
}

class FetchingSingleServiceDetail extends ApiState {}

class FetchedSingleServiceDetail extends ApiState {
  final List<ServiceRequestDetail> serviceRequestDetail;
  FetchedSingleServiceDetail(this.serviceRequestDetail);
}

class SingleServiceDetailError extends ApiState {
  final String errorMessage;
  SingleServiceDetailError({required this.errorMessage});
}

class FetchingEquipmentListForAudit extends ApiState {}

class FetchedEquipmentListForAudit extends ApiState {
  final List<eq.Equipment> equipmentList;
  FetchedEquipmentListForAudit(this.equipmentList);
}

class EquipmentListForAuditError extends ApiState {
  final String errorMessage;
  EquipmentListForAuditError({required this.errorMessage});
}

class CreatingNewAudit extends ApiState {}

class NewAuditCreated extends ApiState {
  final String response;
  NewAuditCreated({required this.response});
}

class ErrorCreatingNewAudit extends ApiState {
  final String errorMessage;
  ErrorCreatingNewAudit({required this.errorMessage});
}

//AUDIT
class ValidatingAuditBarcodes extends ApiState {}

class ValidatedAuditBarcodes extends ApiState {
  final List<Equipment> equipmentList;
  ValidatedAuditBarcodes({required this.equipmentList});
}

class ValidateError extends ApiState {
  final String errorMessage;
  ValidateError({required this.errorMessage});
}

class GettingServiceRequestCount extends ApiState {}

class FetchedServiceRequestCount extends ApiState {
  final Map<String, dynamic> data;
  FetchedServiceRequestCount({required this.data});
}

class FetchedServiceRequestCountError extends ApiState {
  final String errorMessage;
  FetchedServiceRequestCountError({required this.errorMessage});
}

class FetchingAirCraftTails extends ApiState {}

class FetchedAirCraftTails extends ApiState {
  final List<dynamic> data;
  FetchedAirCraftTails({required this.data});
}

class FetchAirCraftTailsApiError extends ApiState {
  final String errorMessage;
  FetchAirCraftTailsApiError({required this.errorMessage});
}

class FetchingAirCraftFleet extends ApiState {}

class FetchedAirCraftFleet extends ApiState {
  final Map<String, List<String>> data;
  FetchedAirCraftFleet({required this.data});
}

class FetchAirCraftFleetApiError extends ApiState {
  final String errorMessage;
  FetchAirCraftFleetApiError({required this.errorMessage});
}

class CancellingServiceRequest extends ApiState {
  CancellingServiceRequest();
}

class CancelledServiceRequest extends ApiState {
  CancelledServiceRequest();
}

class CancelServiceRequestError extends ApiState {
  final String errorMessage;
  CancelServiceRequestError({required this.errorMessage});
}
