import 'package:alink/data/repository/api_service.dart';
import 'package:alink/logger/logger.dart';
import 'package:flutter/cupertino.dart';

class TaskTypeProvider extends ChangeNotifier {
  Map<String, dynamic> _taskTypes = {};

  Map<String, dynamic> get task_types => _taskTypes;

  Future<void> getTaskTypes() async {
    final result = await ApiService().getTaskTypeList();
    if (result is Map<String, dynamic>) {
      if (result.containsKey('error')) {
        Logger.i("Error fetching task types: ${result['error']}");
        _taskTypes = {"error": result['error']};
      } else {
        _taskTypes = result;
      }
    } else if (result is String) {
      Logger.i("Error fetching task types: $result");
      _taskTypes = {"error": result};
    } else {
      Logger.i("Failed to fetch task types. Result came as - $result");
      _taskTypes = {"error": "Failed to fetch task types"};
    }
    notifyListeners();
  }
}
