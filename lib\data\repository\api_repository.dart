import 'package:alink/data/model/audit_response.dart';
import 'package:alink/data/repository/api_service.dart';
import 'package:location_platform_interface/location_platform_interface.dart';

class ApiRepository {
  final ApiService apiService;

  ApiRepository({required this.apiService}) : assert(apiService != null);

  fetchDatafromBarcodeNumber(String barcodeNumber, bool? status,
      {double? latitude, double? longitude}) async {
    return await apiService.getEquipmentDetailByBarcodeNumber(
        barcodeNumber, status,
        latitude: latitude, longitude: longitude);
  }

  fetchCustomizationData() async {
    return await apiService.getCustomizationData();
  }

  fetchTerminalData(String locationId) async {
    return await apiService.getTerminalChangeData(locationId);
  }

  fetchServiceRequestDetailById(int locationId, bool isTimedService,
      [bool? isFromAudit, bool? isRestricted]) async {
    return await apiService.getServiceRequestDetailById(
        locationId, isTimedService, isFromAudit, isRestricted);
  }

  fetchRepairListByEquipmentId(
      int equipmentId, bool bothRequestType, bool isTimedService) async {
    return await apiService.getServiceRequestListByEquipmentId(
        equipmentId, bothRequestType, isTimedService);
  }

  Future<dynamic> fetchRepairServiceRequestList(
      int offset, String query) async {
    return await apiService.fetchRepairServiceRequestList(offset, query);
  }

  Future<dynamic> fetchLopaRepairServiceRequestList(String query) async {
    return await apiService.fetchLopaRepairServiceRequestList(query);
  }

  Future<dynamic> fetchPartItemList(
      int offset, String query, String? partId) async {
    return await apiService.fetchPartItemList(offset, query, partId);
  }

  Future<dynamic> getFilterData() async {
    return await apiService.getFilterData();
  }

  Future<dynamic> getLocationData() async {
    return await apiService.getLocationData();
  }

  Future<dynamic> getMessageDataByRequestId(int requestId) async {
    return await apiService.getMessageDataByRequestId(requestId);
  }

  Future<dynamic> fetchBOMPartItemList() async {
    return await apiService.fetchBOMPartItemList();
  }

  Future<dynamic> fetchAvailablePartList(String query) async {
    return await apiService.fetchAvailablePartList(query);
  }

  Future<dynamic> getAuditListByLocationId(int offset, String locationId,
      int? refAuditId, String serviceType, String auditSchId) async {
    return await apiService.getAuditListByLocationId(
        offset, locationId, refAuditId, serviceType, auditSchId);
  }

  createNewAudit(String locationId, String comment, String serviceType) async {
    return await apiService.createNewAudit(locationId, comment, serviceType);
  }

  submitAudit(
      List<AuditEquipment>? equipmentList,
      int auditId,
      LocationData? locationData,
      bool isConditionAudit,
      bool? isSaveAudit) async {
    return await apiService.submitValidatedAudit(
        equipmentList, auditId, locationData, isConditionAudit, isSaveAudit);
  }

  submitAirlineAudit(
      Map<String, List<Map<String, dynamic>>> mapData,
      Map<String, List<Map<String, dynamic>>> imageData,
      int auditId,
      LocationData? locationData) async {
    return await apiService.submitValidatedAirlineAudit(
        mapData, imageData, auditId, locationData);
  }

  validateAuditBarcode(List<AuditEquipment> equipmentList, int auditId) async {
    return await apiService.validateBarcodes(equipmentList, auditId);
  }

  getServiceRequestCount() async {
    return await apiService.getServiceRequestCount();
  }

  Future<dynamic> resetPassword(String email) async {
    return await apiService.resetPassword(email);
  }

  Future<dynamic> fetchAirCraftFleet() async {
    return await apiService.getFleetNameAndSubFleet();
  }

  Future<dynamic> fetchAirCraftTails(
      {required String fleetName, String? subFleetName}) async {
    return await apiService.getTailsByFleetNameAndSubFleet(
        fleetName: fleetName, subFleetName: subFleetName);
  }

  Future<dynamic> cancelServiceRequest(int requestId, String comment) async {
    return await apiService.cancelServiceRequest(
        requestId: requestId, comment: comment);
  }
}
