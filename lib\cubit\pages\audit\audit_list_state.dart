part of 'audit_list_cubit.dart';

@immutable
abstract class AuditListState {}

class AuditListInitial extends AuditListState {}

class FetchingAuditData extends AuditListState {}

class FetchedAuditData extends AuditListState {
  final AuditResponse auditResponse;
  FetchedAuditData(this.auditResponse);
}

class FetchAuditDataError extends AuditListState {
  final String errorMessage;
  FetchAuditDataError({required this.errorMessage});
}

class SubmittingAuditData extends AuditListState {}

class SubmittedAuditData extends AuditListState {
  final int auditId;
  SubmittedAuditData({required this.auditId});
}

class SubmitAuditDataError extends AuditListState {
  final String errorMessage;
  SubmitAuditDataError({required this.errorMessage});
}

class SubmittingAirlineAuditData extends AuditListState {}

class SubmittedAirlineAuditData extends AuditListState {
  final int auditId;
  SubmittedAirlineAuditData({required this.auditId});
}

class SubmitAirlineAuditDataError extends AuditListState {
  final String errorMessage;
  SubmitAirlineAuditDataError({required this.errorMessage});
}
