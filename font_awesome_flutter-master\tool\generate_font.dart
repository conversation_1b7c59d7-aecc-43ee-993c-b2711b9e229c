import 'dart:convert';
import 'dart:io';

import 'package:recase/recase.dart';
import 'package:version/version.dart';

const Map<String, String> nameAdjustments = {
  "500px": "fiveHundredPx",
  "360-degrees": "threeHundredSixtyDegrees",
  "1": "one",
  "2": "two",
  "3": "three",
  "4": "four",
  "5": "five",
  "6": "six",
  "7": "seven",
  "8": "eight",
  "9": "nine",
  "0": "zero",
};

void main(List<String> arguments) {
  var file = new File(arguments.first);

  if (!file.existsSync()) {
    print('Cannot find the file "${arguments.first}".');
  }

  var content = file.readAsStringSync();
  Map<String, dynamic> icons = json.decode(content);

  Map<String, String> iconDefinitions = {};

  bool hasDuotone = false;

  // figure out current version
  Version highestVersion = Version.parse("0.0.0");

  for (String iconName in icons.keys) {
    var icon = icons[iconName];

    // At least one icon does not have a glyph in the font files. This property
    // is marked with "private": true in icons.json
    if ((icon as Map<String, dynamic>).containsKey('private') &&
        icon['private']) continue;

    // compute highest version
    var changes = (icon["changes"] as List);

    List<Version> versions = changes.map((v) {
      // since font awesome does not adhere to semver standards
      var partsCount = v.split(".").length;
      if (partsCount == 1) {
        return v + ".0.0";
      } else if (partsCount == 2) {
        return v + ".0";
      } else if (partsCount == 3) {
        return v;
      } else {
        return "0.0.0";
      }
    }).map((v) {
      try {
        return Version.parse(v);
      } on FormatException {
        return Version.parse("0.0.0");
      }
    }).toList(growable: true)
      ..add(highestVersion)
      ..sort();

    highestVersion = versions.last;

    var unicode = icon['unicode'];
    List<String> styles = (icon['styles'] as List).cast<String>();

    if (styles.length > 1) {
      if (styles.contains('regular')) {
        styles.remove('regular');
        iconDefinitions[iconName] = generateIconDefinition(
          iconName,
          'regular',
          unicode,
          icon["search"]["terms"],
          icon["label"],
        );
      }

      if (styles.contains('duotone')) {
        hasDuotone = true;
      }

      for (String style in styles) {
        String name = '${style}_$iconName';
        iconDefinitions[name] = generateIconDefinition(
          name,
          style,
          unicode,
          icon["search"]["terms"],
          icon["label"],
        );
      }
    } else {
      iconDefinitions[iconName] = generateIconDefinition(
        iconName,
        styles.first,
        unicode,
        icon["search"]["terms"],
        icon["label"],
      );
    }
  }

  List<String> generatedOutput = [
    'library font_awesome_flutter;',
    '',
    "import 'package:flutter/widgets.dart';",
    "import 'package:font_awesome_flutter/src/icon_data.dart';",
    "export 'package:font_awesome_flutter/src/fa_icon.dart';",
    "export 'package:font_awesome_flutter/src/icon_data.dart';",
  ];

  if (hasDuotone) {
    generatedOutput
        .add("export 'package:font_awesome_flutter/src/fa_duotone_icon.dart';");
  }

  generatedOutput.addAll([
    '',
    '// THIS FILE IS AUTOMATICALLY GENERATED!',
    '',
    '/// Icons based on font awesome $highestVersion',
    'class FontAwesomeIcons {',
  ]);

  generatedOutput.addAll(iconDefinitions.values);

  generatedOutput.add('}');

  File output = new File('lib/font_awesome_flutter.dart');
  output.writeAsStringSync(generatedOutput.join('\n'));
}

String generateIconDocumentation(
    String iconName, String style, List searchTerms, String iconLabel) {
  searchTerms = searchTerms;
  var searchTermsString = searchTerms.toString();
  searchTermsString =
      searchTermsString.substring(1, searchTermsString.length - 1);

  iconName = iconName.replaceFirst("solid_", "");

  var doc = '/// ${style.sentenceCase} $iconLabel icon\n'
      '///\n'
      '/// https://fontawesome.com/icons/$iconName?style=$style';

  if (searchTermsString.length != 0) {
    doc += '\n/// $searchTermsString';
  }

  return doc;
}

String generateIconDefinition(String iconName, String style, String unicode,
    List searchTerms, String iconLabel) {
  if (style == 'duotone') {
    return generateDuotoneIconDefinition(
        iconName, unicode, searchTerms, iconLabel);
  }

  String doc =
      generateIconDocumentation(iconName, style, searchTerms, iconLabel);

  iconName = normalizeIconName(iconName);
  String iconDataSource = styleToDataSource(style);

  return '$doc\nstatic const IconData $iconName = const $iconDataSource(0x$unicode);';
}

String generateDuotoneIconDefinition(String iconName, String primaryUnicode,
    List searchTerms, String iconLabel) {
  String doc =
      generateIconDocumentation(iconName, "duotone", searchTerms, iconLabel);

  iconName = normalizeIconName(iconName);
  String secondaryUnicode = (int.parse(primaryUnicode, radix: 16) + 0x100000)
      .toRadixString(16)
      .toString();

  return '$doc\nstatic const IconDataDuotone $iconName = const IconDataDuotone(0x$primaryUnicode, secondary: const IconDataDuotone(0x$secondaryUnicode),);';
}

String normalizeIconName(String iconName) {
  iconName = nameAdjustments[iconName] ?? iconName;

  return new ReCase(iconName).camelCase;
}

String styleToDataSource(String style) {
  style = '${style[0].toUpperCase()}${style.substring(1)}';

  return 'IconData$style';
}
