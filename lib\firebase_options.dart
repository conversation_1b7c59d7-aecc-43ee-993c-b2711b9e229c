// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyACzdMun-EJU4HRFmt4IvWYLQf8obvbQ-o',
    appId: '1:655096066337:web:7953c9841471c0564a8e60',
    messagingSenderId: '655096066337',
    projectId: 'afs-alink',
    authDomain: 'afs-alink.firebaseapp.com',
    storageBucket: 'afs-alink.appspot.com',
    measurementId: 'G-LMQHVRRQM9',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDGp2S3ALU8ls0vyO101Hqf6mOxubUnY_k',
    appId: '1:655096066337:android:974e59fa6645895f4a8e60',
    messagingSenderId: '655096066337',
    projectId: 'afs-alink',
    storageBucket: 'afs-alink.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyAToum7KQQJphayNe358u3gkVmFCnMKGlg',
    appId: '1:655096066337:ios:acef1b92c7aeacb54a8e60',
    messagingSenderId: '655096066337',
    projectId: 'afs-alink',
    storageBucket: 'afs-alink.appspot.com',
    iosClientId:
        '655096066337-9aibkeveutsm7kibs600lb9dqnqdoih5.apps.googleusercontent.com',
    iosBundleId: 'com.afs.alink',
  );
}
