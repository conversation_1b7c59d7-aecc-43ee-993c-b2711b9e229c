part of 'user_bloc.dart';

@immutable
abstract class UserState {}

class UserInitial extends UserState {}

class UserFetching extends UserState {}

class UserFetched extends UserState {
  final dynamic userData;

  UserFetched({required this.userData}) : assert(userData != null);

  List<Object> get props => [userData];
}

class UserError extends UserState {
  final String error;
  UserError({required this.error});
}

//check log in or not
class CheckedUserLoggedIn extends UserState {}

class UserNotLogIn extends UserState {}

class UserLoggedIn extends UserState {
  final UserData userData;
  UserLoggedIn({required this.userData});
}

class LoginUserFetched extends UserState {
  final UserData userData;
  LoginUserFetched({required this.userData});
}

class UserLoggingOut extends UserState {}

class UserLoggedOut extends UserState {}
