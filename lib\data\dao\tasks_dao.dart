import 'package:alink/database/database.dart';
import 'package:drift/drift.dart';
import '../model/task.dart';

part 'tasks_dao.g.dart';

@DriftAccessor(tables: [Tasks])
class TaskDao extends DatabaseAccessor<Database> with _$TaskDaoMixin {
  final Database db;

  TaskDao(this.db) : super(db);

  // Insert a new task
  Future<int> insertTask(Task task) => into(tasks).insert(task);

  // Get all tasks
  Future<List<Task>> getAllTasks() => select(tasks).get();

  // Get a specific task by ID
  Future<Task?> getTaskById(int taskId) {
    return (select(tasks)..where((tbl) => tbl.TASK_ID.equals(taskId)))
        .getSingleOrNull();
  }

  // Update a task
  Future<bool> updateTask(Task task) => update(tasks).replace(task);

  // Delete a task by ID
  Future<int> deleteTask(int taskId) {
    return (delete(tasks)..where((tbl) => tbl.TASK_ID.equals(taskId))).go();
  }
}
