import 'package:equatable/equatable.dart';

class PartItemResponse extends Equatable {
  String partId;
  int customerId;
  String name;
  String description;
  String manufacturer;
  String? bomPartId;
  String manufacturerPartNo;
  String partType;
  int count;
  String? uom;

  List<dynamic> equipmentCategoryIds;

  PartItemResponse({
    required this.partId,
    required this.customerId,
    required this.name,
    required this.description,
    required this.manufacturer,
    required this.manufacturerPartNo,
    required this.partType,
    this.bomPartId,
    this.count = 1,
    this.uom,
    this.equipmentCategoryIds = const [],
  });

  Map<String, dynamic> toMap() {
    return {
      'PART_ID': this.partId,
      'CUSTOMER_ID': this.customerId,
      'BOM_PART_ID': this.bomPartId,
      'NAME': this.name,
      'DESCRIPTION': this.description,
      'MANUFACTURER': this.manufacturer,
      'MFG_PART_NUMBER': this.manufacturerPartNo,
      'PART_TYPE': this.partType,
      'COUNT': this.count,
      'UOM': this.uom,
      'EQUIPMENT_CATEGORY_IDS': this.equipmentCategoryIds,
    };
  }

  factory PartItemResponse.fromMap(Map<String, dynamic> map) {
    return PartItemResponse(
      partId: map['PART_ID'] != null ? map['PART_ID'] as String : '',
      customerId: map['CUSTOMER_ID'] != null ? map['CUSTOMER_ID'] as int : 0,
      bomPartId: map['BOM_PART_ID'] != null ? map['BOM_PART_ID'] as String : '',
      name: map['NAME'] != null ? map['NAME'] as String : '',
      description:
          map['DESCRIPTION'] != null ? map['DESCRIPTION'] as String : '',
      manufacturer:
          map['MANUFACTURER'] != null ? map['MANUFACTURER'] as String : '',
      manufacturerPartNo: map['MFG_PART_NUMBER'] != null
          ? map['MFG_PART_NUMBER'].toString()
          : '',
      partType: map['PART_TYPE'] != null ? map['PART_TYPE'] as String : '',
      count: map['COUNT'] != null ? map['COUNT'] as int : 0,
      uom: map['UOM'] != null ? map['UOM'] as String : 'pc',
      equipmentCategoryIds: map['EQUIPMENT_CATEGORY_IDS'] != null
          ? map['EQUIPMENT_CATEGORY_IDS'] as List<dynamic>
          : [],
    );
  }

  @override
  // TODO: implement props
  List<Object> get props => [
        partId,
        customerId,
        //bomPartId!,
        name,
        description,
        manufacturer,
        manufacturerPartNo,
        partType,
      ];

  @override
  String toString() {
    return 'PartItemResponse{partId: $partId, customerId: $customerId, bomPartId: $bomPartId, name: $name, description: $description, manufacturer:'
        ' $manufacturer, manufacturerPartNo: $manufacturerPartNo, partType: $partType, count: $count, equipmentCategoryIds: $equipmentCategoryIds}';
  }
}
