import 'package:alink/scanner/barcode/app_barcode_scanner_widget.dart';
import 'package:alink/util/application_util.dart';
import 'package:flutter/material.dart';

import '../../logger/logger.dart';

/// FullScreenScannerPage
class AIBarCodeScannerPage extends StatefulWidget {
  static const routeName = '/ai-barcode-scanner';
  final String? title;

  const AIBarCodeScannerPage({Key? key, this.title}) : super(key: key);

  @override
  _AIBarCodeScannerPageState createState() => _AIBarCodeScannerPageState();
}

class _AIBarCodeScannerPageState extends State<AIBarCodeScannerPage> {
  String className = "_AIBarCodeScannerPageState";
  @override
  Widget build(BuildContext context) {
    print(widget.title);
    Logger.i("Class Name: $className");
    return Scaffold(
      body: Column(
        children: [
          Expanded(
            child: AppBarcodeScannerWidget.defaultStyle(
              resultCallback: (String code, bool isScanned) {
                Navigator.pop(context, {"code": code, "isScanned": isScanned});
              },
              title: widget.title,
            ),
          ),
        ],
      ),
      floatingActionButton: ApplicationUtil.getBackButton(
        context,
        onBackPressed: () {
          Navigator.pop(context);
        },
      ),
    );
  }
}
