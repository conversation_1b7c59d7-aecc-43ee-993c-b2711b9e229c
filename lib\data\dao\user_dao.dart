import 'package:alink/data/model/user.dart';
import 'package:alink/database/database.dart';
import 'package:drift/drift.dart';
part 'user_dao.g.dart';

@DriftAccessor(
  tables: [User],
)
class UserDao extends DatabaseAccessor<Database> with _$UserDaoMixin {
  final Database db;
  UserDao(this.db) : super(db);
  Future insertUser(Insertable<UserData> userData) =>
      into(user).insert(userData);
  Future updateUser(Insertable<UserData> userData) =>
      update(user).replace(userData);
  Future deleteUser(Insertable<UserData> userData) =>
      delete(user).delete(userData);
  Future<UserData?> getUser() => select(user).getSingleOrNull();
  Future getUserByIdAndEmail(int id, String email) => (select(user)
        ..where((t) => t.USER_ID.equals(id))
        ..where((tbl) => tbl.EMAIL.equals(email)))
      .getSingleOrNull();
}
