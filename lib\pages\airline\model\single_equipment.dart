import 'package:alink/data/model/repair_service_request.dart';

class SingleEquipmentAndPart {
  int serviceRequestId;
  DateTime createdDateTimeSR;
  int? remainingDateTime;
  String? status;
  bool isSRPresentInDb;
  String equipmentName;
  String requestType;
  String? safetyIssue;

  List<LopaRepairPartItem> partList;
  List<Map<String, dynamic>> imageList;
  List<Map<String, dynamic>> serverImageList;

  SingleEquipmentAndPart(
      {required this.serviceRequestId,
      required this.createdDateTimeSR,
      this.remainingDateTime,
      this.status,
      required this.requestType,
      this.isSRPresentInDb = false,
      required this.equipmentName,
      required this.partList,
      required this.imageList,
      required this.serverImageList,
      this.safetyIssue});

  factory SingleEquipmentAndPart.fromMap(
      Map<String, dynamic> map, RepairServiceRequest selectedServiceRequest) {
    return SingleEquipmentAndPart(
        serviceRequestId: selectedServiceRequest.requestId,
        createdDateTimeSR: selectedServiceRequest.createdAt != null
            ? DateTime.parse(selectedServiceRequest.createdAt!).toLocal()
            : DateTime.now().toLocal(),
        remainingDateTime: selectedServiceRequest.remainingRequestTime,
        status: selectedServiceRequest.status,
        isSRPresentInDb: selectedServiceRequest.isPresentInDb,
        safetyIssue: selectedServiceRequest.safetyIssue,
        equipmentName: map['equipmentName'] as String,
        partList: map['partData'] is List
            ? List<LopaRepairPartItem>.from(map['partData']
                .map((e) => LopaRepairPartItem.fromMap(e))
                .toList())
            : [LopaRepairPartItem.fromMap(map['partData'])],
        imageList: [],
        serverImageList: LopaRepairPartItem.getImageListByEquipment(
            map['equipmentName'], selectedServiceRequest),
        requestType: selectedServiceRequest.requestType!);
  }
}

class LopaRepairPartItem {
  String partId;
  bool completed;
  bool isRepaired;
  bool isChecked;
  int checkCount;
  String description;
  String equipmentLocationId;
  List<Map<String, dynamic>> imageList = [];

  LopaRepairPartItem(
      {required this.partId,
      this.completed = false,
      this.isRepaired = false,
      this.isChecked = false,
      this.checkCount = 0,
      this.description = '',
      required this.imageList,
      this.equipmentLocationId = ''});

  factory LopaRepairPartItem.fromMap(Map<String, dynamic> map) {
    return LopaRepairPartItem(
      partId: map['PartId'] as String,
      completed: map['Completed'] as bool,
      isRepaired: map['IsRepaired'] != null ? map['IsRepaired'] as bool : false,
      imageList: [],
    );
  }

  @override
  String toString() {
    return 'LopaPartItem{partId: $partId, completed: $completed, isRepaired: $isRepaired}';
  }

  static List<Map<String, dynamic>> getImageListByEquipment(
      equipmentName, RepairServiceRequest selectedServiceRequest) {
    if (selectedServiceRequest.imageListMap != null) {
      if (selectedServiceRequest.imageListMap![equipmentName] != null) {
        return List<Map<String, dynamic>>.from(
            selectedServiceRequest.imageListMap![equipmentName]);
      }
    }
    return [];
  }
}
