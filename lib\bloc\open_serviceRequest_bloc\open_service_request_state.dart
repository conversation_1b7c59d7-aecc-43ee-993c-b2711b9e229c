import 'package:equatable/equatable.dart';

abstract class OpenServiceRequestState extends Equatable {
  const OpenServiceRequestState();

  @override
  List<Object> get props => [];
}

class OpenServiceRequestInitial extends OpenServiceRequestState {}

class OpenServiceRequestLoading extends OpenServiceRequestState {}

class OpenServiceRequestLoaded extends OpenServiceRequestState {
  final List count;

  const OpenServiceRequestLoaded(this.count);

  @override
  List<Object> get props => [count];
}

class OpenServiceRequestError extends OpenServiceRequestState {
  final String message;

  const OpenServiceRequestError(this.message);

  @override
  List<Object> get props => [message];
}
