import 'dart:async';

import 'package:alink/bloc/api/api_bloc.dart';
import 'package:alink/bloc/audit/audit_equipment_cubit.dart';
import 'package:alink/bloc/service/service_request_bloc.dart';
import 'package:alink/cubit/location/location_cubit.dart';
import 'package:alink/cubit/pages/audit/audit_list_cubit.dart';
import 'package:alink/data/model/audit_location.dart';
import 'package:alink/data/model/audit_response.dart';
import 'package:alink/data/model/location_detail.dart';
import 'package:alink/pages/airport/audit/equipment_list_page.dart';
import 'package:alink/pages/auth/login_page.dart';
import 'package:alink/util/app_color.dart';
import 'package:alink/util/application_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:intl/intl.dart';

import '../../../bloc/audit_bloc/audit_bloc.dart';
import '../../../data/model/audit_id_model.dart';
import '../../../logger/logger.dart';
import 'audit_page.dart';

class SplitAudit extends StatefulWidget {
  static const String routeName = "splitAudit";
  final int? refAuditId;
  final String auditName;

  const SplitAudit(
      {Key? key, required this.refAuditId, required this.auditName})
      : super(key: key);

  @override
  State<SplitAudit> createState() => _SplitAuditState();
}

class _SplitAuditState extends State<SplitAudit> {
  static const String className = '_SplitAuditPageState';
  String? selectedItem;
  int selected = 0;
  List<LocationDetail> locationList = [];
  late TextEditingController _auditCommentController;
  bool showCreateAuditButton = false;
  String selectedLocationId = '';

  // LocationCubit get locationCubit => BlocProvider.of<LocationCubit>(context);

  //AuditListCubit get auditCubit => BlocProvider.of<AuditListCubit>(context);

  ApiBloc get apiBLoc => BlocProvider.of<ApiBloc>(context);

  ServiceRequestBloc get serviceRequestBloc =>
      BlocProvider.of<ServiceRequestBloc>(context);

  AuditBloc get auditBloc => BlocProvider.of<AuditBloc>(context);

  final scrollController = ScrollController();

  @override
  void initState() {
    _auditCommentController = TextEditingController();
    //locationCubit.getLocationData();
    _initializeAuditList();
    super.initState();
  }

  @override
  void dispose() {
    _auditCommentController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    print('widget.refAuditId from previous screen ');
    print(widget.refAuditId.toString());
    Logger.i("Class Name: " + className);
    return WillPopScope(
      onWillPop: () async {
        print('reset');
        //auditCubit.resetAuditList();
        auditBloc.add(ResetAuditListData());
        return false;
      },
      child: Scaffold(
        body: SafeArea(
          child: Center(
            child: Stack(
              alignment: Alignment.center,
              children: [
                Container(
                  constraints: const BoxConstraints(maxWidth: 500),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      ApplicationUtil.displayNotificationWidgetIfExist(
                          context, SplitAudit.routeName),
                      Expanded(
                        child: SingleChildScrollView(
                          child: Container(
                            margin: const EdgeInsets.symmetric(vertical: 5),
                            padding: const EdgeInsets.symmetric(
                                vertical: 15, horizontal: 15),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                BlocConsumer<LocationCubit, LocationState>(
                                  listener: (context, state) {
                                    if (state is FetchLocationDataError) {
                                      if (state.errorMessage ==
                                          ApiResponse.INVALID_AUTH) {
                                        Navigator.pushNamedAndRemoveUntil(
                                            context,
                                            LoginPage.routeName,
                                            (route) => false,
                                            arguments: true);
                                      }
                                    } else if (state is FetchedLocationData) {
                                      locationList = state.locationList;
                                    }
                                  },
                                  builder: (context, state) {
                                    if (state is FetchedLocationData) {
                                      if (InMemoryAudiData.location != null) {
                                        //selectedLocationId = InMemoryAudiData.location!;
                                      }
                                      return Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Container(
                                            child: Column(children: [
                                              //getPendingAuditUI(),
                                              _auditListUI(),
                                            ]),
                                          ),
                                        ],
                                      );
                                    }
                                    return const Center(
                                      child: CircularProgressIndicator(),
                                    );
                                  },
                                ),
                                const SizedBox(
                                  height: 10,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        floatingActionButton: ApplicationUtil.getBackButton(
          context,
          onBackPressed: () {
            navigateToAuditMainPage();
          },
        ),
      ),
    );
  }

  _auditListUI() {
    return BlocConsumer<AuditBloc, AuditState>(
      listener: (context, state) {
        if (state is SubmittedAuditListData) {
          ApplicationUtil.showSnackBar(
              context: context,
              message: AppLocalizations.of(context)!.auditCompleteSuccessfully);
          //auditCubit.getAuditListData("", widget.refAuditId);
          serviceRequestBloc.add(DeleteAuditFromTable(auditId: state.auditId));
        } else if (state is SubmittingAuditData) {
          ApplicationUtil.showLoaderDialog(
              context, "Please wait submitting audit");
        } else if (state is SubmitAuditListDataError) {
          if (state.errorMessage == ApiResponse.INVALID_AUTH) {
            Navigator.pushNamedAndRemoveUntil(
                context, LoginPage.routeName, (route) => false,
                arguments: true);
          } else {
            ApplicationUtil.showSnackBar(
                context: context, message: state.errorMessage);
          }
        }
      },
      builder: (context, state) {
        List<Audit> auditList = [];
        int? auditCount = 0;
        bool isLoading = false;
        /*print(state);*/
        if (state is AuditListInitial) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _getAuditComopleteUI(),
              _getDoneButton(),
            ],
          );
        } else if (state is FetchingAuditListData && state.isFirstFetch) {
          return _loadingIndicator();
        } else if (state is FetchedAuditListData) {
          auditList = state.auditResponse.auditList;
          auditCount = state.auditResponse.auditCount;
          if (state.auditResponse.auditList.isEmpty ||
              state.auditResponse.auditList.length == 0) {
            Future.delayed(const Duration(seconds: 2));
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _getAuditComopleteUI(),
                _getDoneButton(),
              ],
            );
          }
        } else if (state is FetchAuditListDataError) {
          return Center(
            child: Text(state.errorMessage),
          );
        } else if (state is FetchingAuditListData) {
          auditList = state.oldList;
          isLoading = true;
        }
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            getPendingAuditUI(auditList),
            ListView.builder(
              padding: const EdgeInsets.all(0),
              controller: scrollController,
              shrinkWrap: true,
              //physics: const ScrollPhysics(),
              itemCount: (auditList.length + 1) + (isLoading ? 1 : 0),
              itemBuilder: (context, index) {
                /*print(
                    "audit list length--->${auditList.length}\naudit count--->$auditCount\nindex--->$index");*/
                if (index < auditList.length) {
                  Audit audit = auditList[index];
                  /* print(index.toString() +
                      " lesser  " +
                      auditList.length.toString());*/
                  return getSingleListTile(audit, index);
                } else if (auditList.length < auditCount!) {
                  /*print(index.toString() +
                      "  equal " +
                      auditList.length.toString());*/
                  return ElevatedButton(
                      onPressed: () {
                        //auditCubit.getAuditListData(0, selectedLocationId, null);
                        if (auditList.length == auditCount) {
                          auditBloc.add(FetchAuditList(
                              selectedLocationId, null, "", "",
                              refresh: false));
                        } else {
                          auditBloc.add(FetchAuditList(
                              selectedLocationId, null, "", "",
                              refresh: false));
                        }
                      },
                      child: const Text("Load More"));
                } else if (auditList.length == auditCount) {
                  /*print(index.toString() + "   " + auditList.length.toString());*/
                  return Container();
                } else if (auditList.length >= auditCount) {
                  return Container();
                }
                {
                  Timer(const Duration(milliseconds: 30), () {
                    scrollController
                        .jumpTo(scrollController.position.maxScrollExtent);
                  });
                  return _loadingIndicator();
                }
              },
            )
          ],
        );
      },
    );
  }

  getSingleListTile(Audit audit, int index) {
    List auditLocation = [];
    for (var element in audit.auditLocationList!) {
      element.forEach((key, value) {
        if (key == "NAME") {
          auditLocation.add(value);
        }
      });
    }
    return Container(
      margin: const EdgeInsets.only(bottom: 10),
      decoration: BoxDecoration(
          border: Border.all(color: AppColor.greyBorderColor),
          borderRadius: BorderRadius.circular(10)),
      child: ListTile(
        onTap: () {
          AuditEquipmentCubit.auditId = audit.auditId;
          AuditEquipmentCubit.location = audit.auditLocationList;
          AuditEquipmentCubit.auditLocation =
              ApplicationUtil.getEndLocationFromLocation(
                  locationId: audit.locationId!,
                  locationMap: audit.auditLocationList);
          serviceRequestBloc.add(SaveEquipmentDataFromAuditStatus(
              audit.auditEquipmentList, audit.auditId!,
              updateRequired: false));

          Navigator.pushNamed(context, EquipmentListPage.routeName,
                  arguments: AuditRefId(
                      index: index,
                      refAuditId: audit.refAuditId!,
                      auditName: widget.auditName,
                      auditStatus: audit.status!,
                      serviceType: "Audit"))
              .then((value) {
            if (value != null && value == 'refresh') {
              print('pop till here');
            }
          });
        },
        /* leading: const FaIcon(
          FontAwesomeIcons.userSecret,
          color: Color(0xff3F51B5),
        ),*/
        title: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              audit.schedule != null ? audit.schedule! : '',
              style: const TextStyle(
                color: Color(0xff101010),
              ),
            ),
          ],
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(audit.description.toString()),
            const SizedBox(
              height: 5,
            ),
            Text(
              auditLocation.join(" • ").toString(),
              style:
                  const TextStyle(fontSize: 16, color: AppColor.blackTextColor),
            ),
          ],
        ),
        trailing: Container(
          padding: const EdgeInsets.all(10),
          decoration: const BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.red,
          ),
          child: Text(
            audit.auditEquipmentList.length.toString(),
            style: const TextStyle(color: Colors.white),
          ),
        ),
      ),
    );
  }

  String getFormattedDate(String date) {
    var localDate = DateTime.parse(date).toLocal();

    var inputFormat = DateFormat('yyyy-MM-dd HH:mm');
    var inputDate = inputFormat.parse(localDate.toString());
    var outputFormat = DateFormat('dd-MMMM-yyyy');
    var outputDate = outputFormat.format(inputDate);

    return outputDate.toString();
  }

  getPendingAuditUI(List<Audit> auditList) {
    //String updatedDate;
    var updatedDate = auditList.isNotEmpty
        ? getFormattedDate(auditList.first.expiryDate!)
        : Container();
    return Container(
      margin: const EdgeInsets.only(bottom: 10),
      padding: const EdgeInsets.only(top: 10),
      decoration: BoxDecoration(
          border: Border.all(color: Colors.blueAccent),
          borderRadius: BorderRadius.circular(10)),
      child: ListTile(
        onTap: () {},
        title: Text(
          AppLocalizations.of(context)!.pendingAudit,
          style: const TextStyle(
              color: AppColor.blueServiceRequestTextColor,
              fontSize: 18,
              fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(
              height: 10,
            ),
            auditList.isNotEmpty
                ? Text(
                    "The " +
                        auditList[0].description.toString() +
                        " " +
                        AppLocalizations.of(context)!.pendingAuditsMessage +
                        " " +
                        updatedDate.toString(),
                    style: const TextStyle(
                      fontSize: 14,
                    ))
                : const Text(" "),
            const SizedBox(
              height: 10,
            ),
            _getSkipButton(),
            const SizedBox(
              height: 10,
            ),
          ],
        ),
      ),
    );
  }

  _getSkipButton() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 5),
      width: double.infinity,
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15.0),
          ),
        ),
        onPressed: () {
          navigateToAuditMainPage();
        },
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 14),
          child: Text(
            AppLocalizations.of(context)!.skip,
            style: const TextStyle(color: Colors.white, fontSize: 18),
          ),
        ),
      ),
    );
  }

  _getAuditComopleteUI() {
    serviceRequestBloc.add(DeleteAuditFromTable(auditId: widget.refAuditId!));
    return Container(
      margin: const EdgeInsets.only(bottom: 10),
      padding: const EdgeInsets.only(top: 10),
      decoration: BoxDecoration(
          border: Border.all(color: Colors.blueAccent),
          borderRadius: BorderRadius.circular(10)),
      child: ListTile(
        onTap: () {},
        leading: const FaIcon(
          FontAwesomeIcons.checkCircle,
          color: AppColor.greenSentColor,
          size: 30,
        ),
        title: Text(
          AppLocalizations.of(context)!.auditComplete,
          style: const TextStyle(
              color: AppColor.greenSentColor,
              fontSize: 18,
              fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(
              height: 10,
            ),
            RichText(
              text: TextSpan(
                text: "All audits which are part of ",
                style: const TextStyle(color: AppColor.blackTextColor),
                children: <TextSpan>[
                  TextSpan(
                    text: widget.auditName.toString(),
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const TextSpan(
                    text: " are completed",
                    style: TextStyle(color: AppColor.blackTextColor),
                  )
                ],
              ),
            ),
            const SizedBox(
              height: 10,
            ),
          ],
        ),
      ),
    );
  }

  void _initializeAuditList() {
    auditBloc.add(FetchAuditList("", widget.refAuditId, "", "", refresh: true));
    //auditCubit.getAuditListData(0, "", widget.refAuditId);
  }

  _getDoneButton() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 5),
      width: double.infinity,
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15.0),
          ),
        ),
        onPressed: () {
          navigateToAuditMainPage();
        },
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 14),
          child: Text(
            AppLocalizations.of(context)!.done,
            style: const TextStyle(color: Colors.white, fontSize: 18),
          ),
        ),
      ),
    );
  }

  Widget _loadingIndicator() {
    /*print("=============called loadingIndicator for load more=============");*/
    return Container(
      padding: const EdgeInsets.all(8.0),
      child: const Center(child: CircularProgressIndicator()),
    );
  }

  void navigateToAuditMainPage() {
    auditBloc.add(ResetAuditListData());
    Navigator.pushReplacement(
        context,
        MaterialPageRoute(
          builder: (context) => const AuditPage(),
        ));
  }
}
