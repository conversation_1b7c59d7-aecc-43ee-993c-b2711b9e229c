part of 'selected_part_item_bloc.dart';

@immutable
abstract class SelectedPartItemEvent {}

class UpdatePartItem extends SelectedPartItemEvent {
  final PartItemResponse? part;
  final bool isAdd;
  final bool isReset;
  UpdatePartItem({this.part, this.isAdd = false, this.isReset = false});
}

class RemovePartItem extends SelectedPartItemEvent {
  final PartItemResponse part;
  RemovePartItem(this.part);
}

class FetchSelectedPartItem extends SelectedPartItemEvent {}

class ProPopulateSuggestedPart extends SelectedPartItemEvent {
  final List<PartItemResponse>? suggestedParts;
  ProPopulateSuggestedPart({this.suggestedParts});
}
