PODS:
  - camera_avfoundation (0.0.1):
    - Flutter
  - connectivity_plus (0.0.1):
    - Flutter
    - ReachabilitySwift
  - device_info_plus (0.0.1):
    - Flutter
  - Firebase/Analytics (10.28.0):
    - Firebase/Core
  - Firebase/Core (10.28.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 10.28.0)
  - Firebase/CoreOnly (10.28.0):
    - FirebaseCore (= 10.28.0)
  - Firebase/Crashlytics (10.28.0):
    - Firebase/CoreOnly
    - FirebaseCrashlytics (~> 10.28.0)
  - firebase_analytics (11.2.0):
    - Firebase/Analytics (= 10.28.0)
    - firebase_core
    - Flutter
  - firebase_core (3.2.0):
    - Firebase/CoreOnly (= 10.28.0)
    - Flutter
  - firebase_crashlytics (4.0.3):
    - Firebase/Crashlytics (= 10.28.0)
    - firebase_core
    - Flutter
  - FirebaseAnalytics (10.28.0):
    - FirebaseAnalytics/AdIdSupport (= 10.28.0)
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebaseAnalytics/AdIdSupport (10.28.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleAppMeasurement (= 10.28.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebaseCore (10.28.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.12)
    - GoogleUtilities/Logger (~> 7.12)
  - FirebaseCoreExtension (10.29.0):
    - FirebaseCore (~> 10.0)
  - FirebaseCoreInternal (10.29.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseCrashlytics (10.28.1):
    - FirebaseCore (~> 10.5)
    - FirebaseInstallations (~> 10.0)
    - FirebaseRemoteConfigInterop (~> 10.23)
    - FirebaseSessions (~> 10.5)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/Environment (~> 7.8)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (~> 2.1)
  - FirebaseInstallations (10.29.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - FirebaseRemoteConfigInterop (10.29.0)
  - FirebaseSessions (10.29.0):
    - FirebaseCore (~> 10.5)
    - FirebaseCoreExtension (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/Environment (~> 7.13)
    - GoogleUtilities/UserDefaults (~> 7.13)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesSwift (~> 2.1)
  - Flutter (1.0.0)
  - flutter_email_sender (0.0.1):
    - Flutter
  - flutter_image_compress (1.0.0):
    - Flutter
    - Mantle
    - SDWebImage
    - SDWebImageWebPCoder
  - flutter_keyboard_visibility (0.0.1):
    - Flutter
  - fluttertoast (0.0.2):
    - Flutter
    - Toast
  - geolocator_apple (1.2.0):
    - Flutter
  - Google-Maps-iOS-Utils (5.0.0):
    - GoogleMaps (~> 8.0)
  - google_maps_flutter_ios (0.0.1):
    - Flutter
    - Google-Maps-iOS-Utils (< 7.0, >= 5.0)
    - GoogleMaps (< 10.0, >= 8.4)
  - GoogleAppMeasurement (10.28.0):
    - GoogleAppMeasurement/AdIdSupport (= 10.28.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleAppMeasurement/AdIdSupport (10.28.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 10.28.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (10.28.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleDataTransport (9.4.1):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleMaps (8.4.0):
    - GoogleMaps/Maps (= 8.4.0)
  - GoogleMaps/Base (8.4.0)
  - GoogleMaps/Maps (8.4.0):
    - GoogleMaps/Base
  - GoogleUtilities/AppDelegateSwizzler (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (7.13.3):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.13.3)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/Reachability (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - image_picker_ios (0.0.1):
    - Flutter
  - libwebp (1.5.0):
    - libwebp/demux (= 1.5.0)
    - libwebp/mux (= 1.5.0)
    - libwebp/sharpyuv (= 1.5.0)
    - libwebp/webp (= 1.5.0)
  - libwebp/demux (1.5.0):
    - libwebp/webp
  - libwebp/mux (1.5.0):
    - libwebp/demux
  - libwebp/sharpyuv (1.5.0)
  - libwebp/webp (1.5.0):
    - libwebp/sharpyuv
  - local_auth_darwin (0.0.1):
    - Flutter
  - location (0.0.1):
    - Flutter
  - Mantle (2.2.0):
    - Mantle/extobjc (= 2.2.0)
  - Mantle/extobjc (2.2.0)
  - MTBBarcodeScanner (5.0.11)
  - nanopb (2.30910.0):
    - nanopb/decode (= 2.30910.0)
    - nanopb/encode (= 2.30910.0)
  - nanopb/decode (2.30910.0)
  - nanopb/encode (2.30910.0)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - pointer_interceptor_ios (0.0.1):
    - Flutter
  - PromisesObjC (2.4.0)
  - PromisesSwift (2.4.0):
    - PromisesObjC (= 2.4.0)
  - qr_code_scanner (0.2.0):
    - Flutter
    - MTBBarcodeScanner
  - ReachabilitySwift (5.2.4)
  - SDWebImage (5.21.0):
    - SDWebImage/Core (= 5.21.0)
  - SDWebImage/Core (5.21.0)
  - SDWebImageWebPCoder (0.14.6):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.17)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - "sqlite3 (3.46.1+1)":
    - "sqlite3/common (= 3.46.1+1)"
  - "sqlite3/common (3.46.1+1)"
  - "sqlite3/dbstatvtab (3.46.1+1)":
    - sqlite3/common
  - "sqlite3/fts5 (3.46.1+1)":
    - sqlite3/common
  - "sqlite3/perf-threadsafe (3.46.1+1)":
    - sqlite3/common
  - "sqlite3/rtree (3.46.1+1)":
    - sqlite3/common
  - sqlite3_flutter_libs (0.0.1):
    - Flutter
    - "sqlite3 (~> 3.46.0+1)"
    - sqlite3/dbstatvtab
    - sqlite3/fts5
    - sqlite3/perf-threadsafe
    - sqlite3/rtree
  - store_redirect (0.0.1):
    - Flutter
  - Toast (4.1.1)
  - url_launcher_ios (0.0.1):
    - Flutter
  - vibration (3.0.0):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter

DEPENDENCIES:
  - camera_avfoundation (from `.symlinks/plugins/camera_avfoundation/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - firebase_analytics (from `.symlinks/plugins/firebase_analytics/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_crashlytics (from `.symlinks/plugins/firebase_crashlytics/ios`)
  - Flutter (from `Flutter`)
  - flutter_email_sender (from `.symlinks/plugins/flutter_email_sender/ios`)
  - flutter_image_compress (from `.symlinks/plugins/flutter_image_compress/ios`)
  - flutter_keyboard_visibility (from `.symlinks/plugins/flutter_keyboard_visibility/ios`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - geolocator_apple (from `.symlinks/plugins/geolocator_apple/ios`)
  - google_maps_flutter_ios (from `.symlinks/plugins/google_maps_flutter_ios/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - local_auth_darwin (from `.symlinks/plugins/local_auth_darwin/darwin`)
  - location (from `.symlinks/plugins/location/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - pointer_interceptor_ios (from `.symlinks/plugins/pointer_interceptor_ios/ios`)
  - qr_code_scanner (from `.symlinks/plugins/qr_code_scanner/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqlite3_flutter_libs (from `.symlinks/plugins/sqlite3_flutter_libs/ios`)
  - store_redirect (from `.symlinks/plugins/store_redirect/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - vibration (from `.symlinks/plugins/vibration/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/ios`)

SPEC REPOS:
  trunk:
    - Firebase
    - FirebaseAnalytics
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseCrashlytics
    - FirebaseInstallations
    - FirebaseRemoteConfigInterop
    - FirebaseSessions
    - Google-Maps-iOS-Utils
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleMaps
    - GoogleUtilities
    - libwebp
    - Mantle
    - MTBBarcodeScanner
    - nanopb
    - PromisesObjC
    - PromisesSwift
    - ReachabilitySwift
    - SDWebImage
    - SDWebImageWebPCoder
    - sqlite3
    - Toast

EXTERNAL SOURCES:
  camera_avfoundation:
    :path: ".symlinks/plugins/camera_avfoundation/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  firebase_analytics:
    :path: ".symlinks/plugins/firebase_analytics/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_crashlytics:
    :path: ".symlinks/plugins/firebase_crashlytics/ios"
  Flutter:
    :path: Flutter
  flutter_email_sender:
    :path: ".symlinks/plugins/flutter_email_sender/ios"
  flutter_image_compress:
    :path: ".symlinks/plugins/flutter_image_compress/ios"
  flutter_keyboard_visibility:
    :path: ".symlinks/plugins/flutter_keyboard_visibility/ios"
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  geolocator_apple:
    :path: ".symlinks/plugins/geolocator_apple/ios"
  google_maps_flutter_ios:
    :path: ".symlinks/plugins/google_maps_flutter_ios/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  local_auth_darwin:
    :path: ".symlinks/plugins/local_auth_darwin/darwin"
  location:
    :path: ".symlinks/plugins/location/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  pointer_interceptor_ios:
    :path: ".symlinks/plugins/pointer_interceptor_ios/ios"
  qr_code_scanner:
    :path: ".symlinks/plugins/qr_code_scanner/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqlite3_flutter_libs:
    :path: ".symlinks/plugins/sqlite3_flutter_libs/ios"
  store_redirect:
    :path: ".symlinks/plugins/store_redirect/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  vibration:
    :path: ".symlinks/plugins/vibration/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/ios"

SPEC CHECKSUMS:
  camera_avfoundation: 04b44aeb14070126c6529e5ab82cc7c9fca107cf
  connectivity_plus: 8443422d4c5a53dee0d50779ec5dbcda1071251e
  device_info_plus: 335f3ce08d2e174b9fdc3db3db0f4e3b1f66bd89
  Firebase: 5121c624121af81cbc81df3bda414b3c28c4f3c3
  firebase_analytics: 9fe4819ecb114d05fa6e40be785ae9534cfe18fa
  firebase_core: 64a8856f75bd3baf5d256d20627818cccfefc40b
  firebase_crashlytics: 98d45db95a08a37e5817f0ab4c44991635c02f89
  FirebaseAnalytics: 1e06fe7d246af7230b08d1d9cdca54a4624dd461
  FirebaseCore: 857dc1c6dd1255675047404d8466f7dfaac5d779
  FirebaseCoreExtension: 705ca5b14bf71d2564a0ddc677df1fc86ffa600f
  FirebaseCoreInternal: df84dd300b561c27d5571684f389bf60b0a5c934
  FirebaseCrashlytics: f51e12b93f8e1134bbed602ed22df33804d55ccf
  FirebaseInstallations: 913cf60d0400ebd5d6b63a28b290372ab44590dd
  FirebaseRemoteConfigInterop: 6efda51fb5e2f15b16585197e26eaa09574e8a4d
  FirebaseSessions: dbd14adac65ce996228652c1fc3a3f576bdf3ecc
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_email_sender: cd533cdc7ea5eda6fabb2c7f78521c71207778a4
  flutter_image_compress: 4b058288a81f76e5e80340af37c709abafff34c4
  flutter_keyboard_visibility: 4625131e43015dbbe759d9b20daaf77e0e3f6619
  fluttertoast: 76fea30fcf04176325f6864c87306927bd7d2038
  geolocator_apple: 1560c3c875af2a412242c7a923e15d0d401966ff
  Google-Maps-iOS-Utils: 66d6de12be1ce6d3742a54661e7a79cb317a9321
  google_maps_flutter_ios: 0291eb2aa252298a769b04d075e4a9d747ff7264
  GoogleAppMeasurement: 55a4a3c8000c1280d68bf4c084adbfef20c49db1
  GoogleDataTransport: 6c09b596d841063d76d4288cc2d2f42cc36e1e2a
  GoogleMaps: 8939898920281c649150e0af74aa291c60f2e77d
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  image_picker_ios: 7fe1ff8e34c1790d6fff70a32484959f563a928a
  libwebp: 02b23773aedb6ff1fd38cec7a77b81414c6842a8
  local_auth_darwin: 059c13c837fd9493856f0de8e1004322695e5510
  location: 155caecf9da4f280ab5fe4a55f94ceccfab838f8
  Mantle: c5aa8794a29a022dfbbfc9799af95f477a69b62d
  MTBBarcodeScanner: f453b33c4b7dfe545d8c6484ed744d55671788cb
  nanopb: 438bc412db1928dac798aa6fd75726007be04262
  package_info_plus: 566e1b7a2f3900e4b0020914ad3fc051dcc95596
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  permission_handler_apple: 4ed2196e43d0651e8ff7ca3483a069d469701f2d
  pointer_interceptor_ios: ec847ef8b0915778bed2b2cef636f4d177fa8eed
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  PromisesSwift: 9d77319bbe72ebf6d872900551f7eeba9bce2851
  qr_code_scanner: d77f94ecc9abf96d9b9b8fc04ef13f611e5a147a
  ReachabilitySwift: 32793e867593cfc1177f5d16491e3a197d2fccda
  SDWebImage: f84b0feeb08d2d11e6a9b843cb06d75ebf5b8868
  SDWebImageWebPCoder: e38c0a70396191361d60c092933e22c20d5b1380
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  sqlite3: 0bb0e6389d824e40296f531b858a2a0b71c0d2fb
  sqlite3_flutter_libs: 9379996d65aa23dcda7585a5b58766cebe0aa042
  store_redirect: 55fd455802ceab09803b2df6e68f19a58815266a
  Toast: 1f5ea13423a1e6674c4abdac5be53587ae481c4e
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d
  vibration: 69774ad57825b11c951ee4c46155f455d7a592ce
  webview_flutter_wkwebview: 45a041c7831641076618876de3ba75c712860c6b

PODFILE CHECKSUM: 05d5671b77e74485f874a29c81d3b5ddcc2299dd

COCOAPODS: 1.16.2
