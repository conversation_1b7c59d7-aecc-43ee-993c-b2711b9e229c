import 'dart:convert';
import 'dart:ui';

import 'package:alink/util/application_util.dart';
import 'package:alink/widget/widget_to_image.dart';
import 'package:crop_image/crop_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

class CropImagePage extends StatefulWidget {
  static const routeName = '/crop-image';
  final base64Img;
  const CropImagePage({Key? key, this.base64Img}) : super(key: key);

  @override
  _CropImagePageState createState() => _CropImagePageState();
}

class _CropImagePageState extends State<CropImagePage> {
  int rotateValue = 4;
  GlobalKey? key;
  String? base64Image;

  final controller = CropController(
    aspectRatio: 1,
    defaultCrop: const Rect.fromLTRB(0.1, 0.1, 0.9, 0.9),
  );

  @override
  void dispose() {
    //controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    base64Image = widget.base64Img;
    return Scaffold(
      /*appBar: AppBar(
        title: Text('Crop Image'),
      ),*/
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(6.0),
          child: RotatedBox(
            quarterTurns: rotateValue,
            child: CropImage(
              controller: controller,
              image: getImage(),
            ),
          ),
        ),
      ),
      floatingActionButton: ApplicationUtil.getBackButton(
        context,
        onBackPressed: () {
          Navigator.pop(context);
        },
      ),
      bottomNavigationBar: _buildButtons(),
    );
  }

  Widget _buildButtons() => Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          IconButton(
            icon: const FaIcon(FontAwesomeIcons.times),
            onPressed: () {
              controller.aspectRatio = 1.0;
              controller.crop = const Rect.fromLTRB(0.1, 0.1, 0.9, 0.9);
            },
          ),
          IconButton(
            icon: const Icon(Icons.rotate_left),
            onPressed: () {
              print(rotateValue);
              if (rotateValue <= 1) {
                rotateValue = 4;
              } else {
                rotateValue--;
              }
              setState(() {});
            },
          ),
          IconButton(
            icon: const Icon(Icons.rotate_right),
            onPressed: () {
              if (rotateValue >= 4) {
                rotateValue = 1;
              } else {
                rotateValue++;
              }
              setState(() {});
            },
          ),
          IconButton(
            icon: const Icon(Icons.aspect_ratio),
            onPressed: _aspectRatios,
          ),
          TextButton(
            onPressed: () {
              _finished(context);
            },
            child: Text(AppLocalizations.of(context)!.done),
          ),
        ],
      );

  Future<void> _aspectRatios() async {
    final value = await showDialog<double>(
      context: context,
      builder: (context) {
        return SimpleDialog(
          title: Text(AppLocalizations.of(context)!.selectAspectRatio),
          children: [
            SimpleDialogOption(
              onPressed: () => Navigator.pop(context, 1.0),
              child: Text(AppLocalizations.of(context)!.square),
            ),
            SimpleDialogOption(
              onPressed: () => Navigator.pop(context, 2.0),
              child: const Text('2:1'),
            ),
            SimpleDialogOption(
              onPressed: () => Navigator.pop(context, 4.0 / 3.0),
              child: const Text('4:3'),
            ),
            SimpleDialogOption(
              onPressed: () => Navigator.pop(context, 16.0 / 9.0),
              child: const Text('16:9'),
            ),
          ],
        );
      },
    );
    if (value != null) {
      controller.aspectRatio = value;
      controller.crop = const Rect.fromLTRB(0.1, 0.1, 0.9, 0.9);
    }
  }

  Future<void> _finished(ctx) async {
    final image = await controller.croppedImage();
    await showDialog<bool>(
      context: context,
      builder: (context) {
        return SimpleDialog(
          contentPadding: const EdgeInsets.all(6.0),
          titlePadding: const EdgeInsets.all(15.0),
          title: Text(
            AppLocalizations.of(context)!.croppedImage,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          children: [
            const SizedBox(height: 5),
            WidgetToImage(
              builder: (key) {
                this.key = key;
                return RotatedBox(quarterTurns: rotateValue, child: image);
              },
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                ElevatedButton(
                  onPressed: () => Navigator.pop(context, true),
                  child: Text(
                    AppLocalizations.of(context)!.backToEdit,
                  ),
                ),
                const SizedBox(
                  width: 10,
                ),
                ElevatedButton(
                  onPressed: () async {
                    var uintList = await captureImage(key);
                    String img64 = base64Encode(uintList);
                    Navigator.pop(context, true);
                    Navigator.pop(ctx, img64);
                  },
                  child: Text(AppLocalizations.of(context)!.done),
                ),
              ],
            ),
          ],
        );
      },
    );
  }

  Image getImage() {
    return Image.memory(base64Decode(base64Image!));
  }

  captureImage(GlobalKey<State<StatefulWidget>>? key) async {
    if (key == null) {
      return;
    }
    RenderRepaintBoundary boundary =
        key.currentContext!.findRenderObject() as RenderRepaintBoundary;
    final image = await boundary.toImage(pixelRatio: 3);
    final byteData = await image.toByteData(format: ImageByteFormat.png);
    final pngByte = byteData!.buffer.asUint8List();
    return pngByte;
  }
}
