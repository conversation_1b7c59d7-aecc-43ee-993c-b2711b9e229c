import 'dart:convert';

import 'package:alink/util/application_util.dart';
import 'package:flutter/material.dart';

class ImageViewPage extends StatefulWidget {
  static const routeName = "image-view";
  final ImageWithTag imageWithTag;
  const ImageViewPage({Key? key, required this.imageWithTag}) : super(key: key);

  @override
  _ImageViewPageState createState() => _ImageViewPageState();
}

class _ImageViewPageState extends State<ImageViewPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Center(
          child: InteractiveViewer(
            panEnabled: false, // Set it to false
            boundaryMargin: const EdgeInsets.all(100),
            minScale: 0.5,
            maxScale: 2,
            child: Hero(
              tag: widget.imageWithTag.index.toString(),
              child: Image.memory(
                base64Decode(widget.imageWithTag.base64),
                width: MediaQuery.of(context).size.width,
                height: MediaQuery.of(context).size.height,
                //fit: BoxFit.cover,
              ),
            ),
          ),
        ),
      ),
      floatingActionButton: ApplicationUtil.getBackButton(
        context,
        onBackPressed: () {
          Navigator.pop(context);
        },
      ),
    );
  }
}
