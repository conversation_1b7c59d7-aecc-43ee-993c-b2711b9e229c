<!DOCTYPE html>
<html lang="en">

<head>
    <title></title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        /* In order to place the tracking correctly */
        canvas.drawing, canvas.drawingBuffer {
            position: absolute;
            left: 0;
            top: 0;
        }
        html, body {
    height: 100%; /* IMPORTANT!!! Stretches viewport to 100% */
}
    </style>
    <script src="https://unpkg.com/html5-qrcode@2.0.13/dist/html5-qrcode.min.js"></script>


</head>

<body>
<!-- Div to show the scanner -->
<div id="reader" ></div>

<script>

const html5QrCode = new Html5Qrcode("reader");
console.log("Starting SCANNGING CODE");
const qrCodeSuccessCallback = (decodedText, decodedResult) => {
    /* handle success */
    SubmitCallback(decodedText)
};
const config = { fps: 10, qrbox:  { width: 280, height: 120 } };

// If you want to prefer back camera
html5QrCode.start({ facingMode: "environment" }, config, qrCodeSuccessCallback);
//html5QrCode.start({ facingMode: "user" }, config, qrCodeSuccessCallback);

    </script>
</body>

</html>