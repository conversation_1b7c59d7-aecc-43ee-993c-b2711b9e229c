import 'package:drift/drift.dart';

class AuditEquipment extends Table {
  @override
  String get tableName => 'AUDIT_EQUIPMENT';
  IntColumn get auditId => integer().autoIncrement().named('AUDIT_ID')();
  IntColumn get equipmentId => integer().nullable().named('EQUIPMENT_ID')();
  TextColumn get equipmentDescription =>
      text().nullable().named('EQUIPMENT_DESCRIPTION')();
  TextColumn get equipmentBarcode =>
      text().nullable().named('EQUIPMENT_BAR_CODE')();
  IntColumn get equipmentLocationId =>
      integer().nullable().named('EQUIPMENT_LOCATION_ID')();
  BoolColumn get isScanned => boolean().nullable().named('IS_SCANNED')();
  BoolColumn get isNew => boolean().nullable().named('IS_NEW')();
}
