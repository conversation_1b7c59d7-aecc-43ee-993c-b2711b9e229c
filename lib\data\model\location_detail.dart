class LocationDetail {
  final String name;
  final String id;
  final String categoryId;
  final List<LocationDetail>? childrens;

  LocationDetail({
    required this.name,
    required this.id,
    required this.categoryId,
    this.childrens,
  });

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'id': id,
      'categoryId': categoryId,
      'childrens': childrens,
    };
  }

  factory LocationDetail.fromMap(Map<String, dynamic> map) {
    List<LocationDetail> childrens = [];
    if (map['childrens'] != null) {
      map['childrens'].forEach((v) {
        childrens.add(LocationDetail.fromMap(v));
      });
      childrens.insert(
          0,
          LocationDetail(
            name: 'Select Location',
            id: "-1",
            categoryId: "-1",
          ));
    }
    return LocationDetail(
      name: map['name'] as String,
      id: map['id'] as String,
      categoryId: map['categoryId'] as String,
      childrens: childrens,
    );
  }
}

class LocationDropdownModel {
  List<LocationDetail> dropdownList;
  LocationDetail selectedValue;
  String parentNodeId;

  LocationDropdownModel({
    required this.dropdownList,
    required this.selectedValue,
    required this.parentNodeId,
  });
}

/*
class ServiceTypeModel {
  final String name;
  final String id;

  ServiceTypeModel({
    required this.name,
    required this.id,
  });

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'id': id,
    };
  }

  factory ServiceTypeModel.fromMap(Map<String, dynamic> map) {
    List<ServiceTypeModel> childrens = [];
    if (map['childrens'] != null) {
      map['childrens'].forEach((v) {
        childrens.add(ServiceTypeModel.fromMap(v));
      });
      childrens.insert(
          0,
          ServiceTypeModel(
            name: 'Select Location',
            id: "-1",
          ));
    }
    return ServiceTypeModel(
      name: map['name'] as String,
      id: map['id'] as String,
    );
  }
}
*/
