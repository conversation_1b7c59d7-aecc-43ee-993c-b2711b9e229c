part of 'audit_equipment_cubit.dart';

@immutable
abstract class AuditEquipmentState {}

class AuditEquipmentInitial extends AuditEquipmentState {}

class EquipmentListData extends AuditEquipmentState {
  final List<AuditEquipment> equipmentList;
  EquipmentListData({required this.equipmentList});
}

class NewEquipmentListData extends AuditEquipmentState {
  final List<String> equipmentList;
  NewEquipmentListData({required this.equipmentList});
}
