import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:alink/bloc/part_items/part_items_bloc.dart';
import 'package:alink/bloc/service/service_request_bloc.dart';
import 'package:alink/cubit/filter/filter_cubit.dart';
import 'package:alink/data/model/barcode_response.dart' as BR;
import 'package:alink/data/model/customization.dart';
import 'package:alink/data/model/drop_down_option.dart';
import 'package:alink/data/model/extension.dart';
import 'package:alink/data/model/filter_data.dart';
import 'package:alink/data/model/location_detail.dart';
import 'package:alink/data/model/part_item_response.dart';
import 'package:alink/database/database.dart';
import 'package:alink/pages/auth/login_page.dart';
import 'package:alink/pages/dashboard_page.dart';
import 'package:alink/pages/full_image_view.dart';
import 'package:alink/pages/image_editor/edit_image_home_page.dart';
import 'package:alink/provider/locationProvider.dart';
import 'package:alink/service_locator.dart';
import 'package:alink/util/app_color.dart';
import 'package:alink/util/app_constant.dart';
import 'package:alink/util/application_util.dart';
import 'package:alink/util/enums/app_enum.dart';
import 'package:alink/widget/location_widget.dart';
import 'package:alink/widget/seperator.dart';
import 'package:drift/drift.dart' as moor;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:http/http.dart' as http;
import 'package:image_picker/image_picker.dart';
import 'package:provider/provider.dart';

import 'package:shared_preferences/shared_preferences.dart';

import '../../../../logger/logger.dart';

class AssignEquipmentPage extends StatefulWidget {
  static const routeName = "assign-equipment";
  final ServiceType type;

  const AssignEquipmentPage({Key? key, required this.type}) : super(key: key);

  @override
  _AssignEquipmentPageState createState() => _AssignEquipmentPageState();
}

class _AssignEquipmentPageState extends State<AssignEquipmentPage> {
  static const String className = '_AssignEquipmentPageState';

  List<UnitType>? unitTypeList;
  var imageList = [];
  UnitType? selectedUnitType;
  String selectedLocationId = '';
  List<LocationDetail> locationList = [];
  late TextEditingController equipmentNameController;

  String selectedCategoryId = "";
  late TextEditingController equipmentDescriptionController;
  BR.BarcodeResponse? barcodeResponse;
  PartItemResponse? selectedPartItem;
  List<PartItemResponse>? partItemList;
  List<Extension> extensionList = [];
  Map<String, dynamic> extensionMap = {};
  bool isValueNotExist = true;
  String? base64Img;
  String? pickedImage;
  final ImagePicker _picker = ImagePicker();
  late String barcodeNUmber;
  Logger logger = Logger();
  LocationProvider? locationProvider;
  bool fetchingLocation = false;

  @override
  void initState() {
    locationProvider = Provider.of<LocationProvider>(context, listen: false);
    unitTypeList = [];
    locationList = [];
    partItemList = [];
    equipmentNameController = TextEditingController();
    equipmentDescriptionController = TextEditingController();
    barcodeNUmber = '';
    filterCubit.getFilterData();
    partItemsBloc.add(FetchBOMPartList());
    initBarcodeResponse();
    super.initState();
  }

  ServiceRequestBloc get serviceRequestBloc =>
      BlocProvider.of<ServiceRequestBloc>(context);

  FilterCubit get filterCubit => BlocProvider.of<FilterCubit>(context);

  PartItemsBloc get partItemsBloc => BlocProvider.of<PartItemsBloc>(context);

  @override
  Widget build(BuildContext context) {
    locationProvider = Provider.of<LocationProvider>(context);
    Logger.i("Class Name: $className");
    return GestureDetector(
      child: Scaffold(
        body: Center(
          child: Container(
            constraints: const BoxConstraints(maxWidth: 500),
            child: Column(
              children: [
                ApplicationUtil.displayNotificationWidgetIfExist(
                    context, AssignEquipmentPage.routeName),
                _assignEquipmentAppBar(),
                Expanded(
                    child: SingleChildScrollView(
                  child: BlocConsumer<FilterCubit, FilterState>(
                    listener: (context, state) {
                      if (state is FetchedFilterData) {
                        FilterData filterData = state.filterList;
                        unitTypeList = filterData.unitTypeList;
                        locationList = filterData.locationList;
                      }
                    },
                    builder: (context, state) {
                      if (state is FetchedFilterData) {
                        return Container(
                          margin: const EdgeInsets.symmetric(
                              horizontal: 10, vertical: 10),
                          /*   padding: EdgeInsets.symmetric(
                              vertical: 15, horizontal: 15),
                          width: double.infinity,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10),
                            border: Border.all(
                                color: Theme.of(context).primaryColor),
                          ),*/
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              _getLocationDropdown(),
                              const SizedBox(
                                height: 10,
                              ),
                              _equipmentDetailForm(),
                              const SizedBox(
                                height: 10,
                              ),
                              _generateExtensionUI(),
                              _getPhotosUI(),
                              const SizedBox(
                                height: 23,
                              ),
                              BlocConsumer<ServiceRequestBloc,
                                  ServiceRequestState>(
                                listener: (context, state) {
                                  // TODO: implement listener
                                },
                                builder: (context, state) {
                                  return _applyFilterButton();
                                },
                              ),
                            ],
                          ),
                        );
                      }
                      return const Center(
                        child: CircularProgressIndicator(),
                      );
                    },
                  ),
                ))
              ],
            ),
          ),
        ),
        floatingActionButton: FloatingActionButton(
          onPressed: () {
            ApplicationUtil.showUnsavedWarningAlertDialog(context);
          },
          child: Container(
              margin: const EdgeInsets.only(right: 5),
              child: const FaIcon(FontAwesomeIcons.chevronLeft)),
        ),
      ),
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
    );
  }

  _equipmentNameWidget() => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          RichText(
            textAlign: TextAlign.start,
            text: TextSpan(
                style: const TextStyle(fontSize: 18),
                children: <TextSpan>[
                  const TextSpan(
                    text: "* ",
                    style: TextStyle(
                        color: AppColor.redColor, fontWeight: FontWeight.bold),
                  ),
                  TextSpan(
                    text: AppLocalizations.of(context)!.equipmentName,
                    style: TextStyle(
                        color: Theme.of(context).primaryColor,
                        fontSize: 18,
                        fontWeight: FontWeight.bold),
                  ),
                ]),
          ),
          const SizedBox(
            height: 5,
          ),
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              border: Border.all(color: AppColor.greyBorderColor),
            ),
            constraints: const BoxConstraints(minHeight: 30),
            child: TextFormField(
              controller: equipmentNameController,
              textInputAction: TextInputAction.next,
              keyboardType: TextInputType.multiline,
              style: const TextStyle(fontSize: 18, height: 1.2),
              decoration: InputDecoration(
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.all(10),
                  hintStyle: TextStyle(
                    color: Colors.grey[500],
                    fontSize: 18,
                  ),
                  hintText: AppLocalizations.of(context)!.enterEquipmentName),
            ),
          )
        ],
      );

  _equipmentDescriptionWidget() => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          RichText(
            textAlign: TextAlign.start,
            text: TextSpan(
                style: const TextStyle(fontSize: 18),
                children: <TextSpan>[
                  const TextSpan(
                    // text: "* ",
                    text: "   ",
                    style: TextStyle(
                        color: AppColor.redColor, fontWeight: FontWeight.bold),
                  ),
                  TextSpan(
                    text: AppLocalizations.of(context)!.equipmentDescription,
                    style: TextStyle(
                        color: Theme.of(context).primaryColor,
                        fontSize: 18,
                        fontWeight: FontWeight.bold),
                  ),
                ]),
          ),
          const SizedBox(
            height: 5,
          ),
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              border: Border.all(color: AppColor.greyBorderColor),
            ),
            constraints: const BoxConstraints(minHeight: 30),
            child: TextFormField(
              controller: equipmentDescriptionController,
              textInputAction: TextInputAction.done,
              keyboardType: TextInputType.multiline,
              style: const TextStyle(fontSize: 18, height: 1.2),
              decoration: InputDecoration(
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.all(10),
                  hintStyle: TextStyle(
                    color: Colors.grey[500],
                    fontSize: 18,
                  ),
                  hintText: AppLocalizations.of(context)!
                      .addSomeDescriptionEquipment),
              maxLines: 3,
            ),
          )
        ],
      );

  _getImages() {
    if (imageList == null || imageList.isEmpty) {
      return _imagePlaceHolder();
    }
    return SizedBox(
      height: 90,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: imageList.length,
        itemBuilder: (context, index) {
          var base64Img = imageList[index];
          if (index == 0) {
            return Row(
              children: [
                _imagePlaceHolder(),
                _getSingleImage(base64Img, index)
              ],
            );
          } else {
            return _getSingleImage(base64Img, index);
          }
        },
      ),
    );
  }

  _equipmentCategory() => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          RichText(
            textAlign: TextAlign.start,
            text: TextSpan(
                style: const TextStyle(fontSize: 18),
                children: <TextSpan>[
                  const TextSpan(
                    text: "* ",
                    style: TextStyle(
                        color: AppColor.redColor, fontWeight: FontWeight.bold),
                  ),
                  TextSpan(
                    text: AppLocalizations.of(context)!.equipmentCategory,
                    style: TextStyle(
                        color: Theme.of(context).primaryColor,
                        fontSize: 18,
                        fontWeight: FontWeight.bold),
                  ),
                ]),
          ),
          const SizedBox(
            height: 5,
          ),
          _unitTypeDropdown(),
        ],
      );

  _unitTypeDropdown() => Container(
        decoration: BoxDecoration(
            color: Theme.of(context).primaryColor,
            borderRadius: BorderRadius.circular(10)),
        child: DropdownButton(
          underline: Container(),
          value: selectedUnitType,
          hint: Container(
            margin: const EdgeInsets.symmetric(horizontal: 10),
            alignment: Alignment.centerLeft,
            child: Text(
              AppLocalizations.of(context)!.select,
              style: const TextStyle(
                  color: Colors.white, fontWeight: FontWeight.bold),
            ),
          ),
          icon: const Icon(
            // Add this
            Icons.arrow_drop_down,
            color: Colors.white,
            size: 35, // Add this
          ),
          isExpanded: true,
          items: unitTypeList!.map(
            (val) {
              return DropdownMenuItem(
                value: val,
                child: Text(val.name!),
              );
            },
          ).toList(),
          selectedItemBuilder: (BuildContext ctxt) {
            return unitTypeList!.map<Widget>((item) {
              return DropdownMenuItem(
                  child: Container(
                    margin: const EdgeInsets.only(left: 20),
                    child: Text("${item.name}",
                        style: const TextStyle(
                            color: Colors.white, fontWeight: FontWeight.bold)),
                  ),
                  value: item);
            }).toList();
          },
          onChanged: (value) {
            setState(() {
              selectedCategoryId = "";
              selectedPartItem = null;
              print("value: $value");
              selectedUnitType = value as UnitType;
              equipmentNameController.text = value.name!;
              selectedCategoryId = value.categoryId.toString();
            });
          },
        ),
      );

  _imagePlaceHolder() => InkWell(
        onTap: () async {
          SharedPreferences sharedPreference = getIt<SharedPreferences>();
          String type =
              sharedPreference.getString("cameraPref") ?? AppConstant.CAMERA;
          if (type == AppConstant.CAMERA) {
            _onImageButtonPressed(ImageSource.camera, context: context);
          }
        },
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 10),
          decoration: BoxDecoration(
              border: Border.all(color: Colors.grey),
              borderRadius: BorderRadius.circular(15)),
          width: 85,
          height: 85,
          child: Center(
            child: FaIcon(
              FontAwesomeIcons.solidCamera,
              size: 30,
              color: Theme.of(context).primaryColor,
            ),
          ),
        ),
      );

  _getSingleImage(base64imageString, int index) => SizedBox(
        height: 90,
        child: Stack(
          alignment: Alignment.center,
          children: [
            InkWell(
              onTap: () {
                Navigator.pushNamed(context, ImageViewPage.routeName,
                    arguments:
                        ImageWithTag(base64: base64imageString, index: index));
              },
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 5),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: ClipRRect(
                  borderRadius: const BorderRadius.all(Radius.circular(10)),
                  child: Hero(
                    tag: index,
                    child: Image.memory(
                      base64Decode(base64imageString),
                      fit: BoxFit.cover,
                      height: 80,
                      width: 80,
                    ),
                  ),
                ),
              ),
            ),
            Positioned(
              top: 0,
              right: 0,
              child: InkWell(
                onTap: () {
                  imageList.removeAt(index);
                  setState(() {});
                },
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(15),
                    color: Colors.white,
                  ),
                  child: const FaIcon(
                    FontAwesomeIcons.solidTimesCircle,
                    color: Colors.red,
                    size: 20,
                  ),
                ),
              ),
            )
          ],
        ),
      );

  _applyFilterButton() => BlocConsumer<ServiceRequestBloc, ServiceRequestState>(
        listener: (context, state) {
          if (state is ServiceRequestError) {
            ApplicationUtil.showSnackBar(
                context: context, message: state.error);
          }
          if (state is ServiceRequestSaved) {
            //ApplicationUtil.showToast(msg: 'Service Request Created');
            serviceRequestBloc.add(SendPendingServiceRequestToServer());
            // Navigator.popAndPushNamed(context, DashboardPage.routeName);
            Navigator.pushNamedAndRemoveUntil(context, DashboardPage.routeName,
                (Route<dynamic> route) => false);
          }
        },
        builder: (context, state) {
          if (state is ServiceRequestInitial) {
            return _buildConfirmAndSubmitButton();
          } else if (state is ServiceRequestSaving ||
              state is ServiceRequestUpdating) {
            return _buildProgressButton();
          } else {
            return _buildConfirmAndSubmitButton();
          }
        },
      );

  _assignEquipmentAppBar() => Container(
        margin: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            const SizedBox(
              width: 10,
            ),
            Text(
              AppLocalizations.of(context)!.assignNewEquipment,
              style: const TextStyle(
                  color: AppColor.blackTextColor,
                  fontSize: AppConstant.toolbarTitleFontSize,
                  fontWeight: FontWeight.bold),
            ),
          ],
        ),
      );

  bool _validateForm() {
    if (selectedLocationId.trim().isEmpty) {
      ApplicationUtil.showSnackBar(
          context: context,
          message: AppLocalizations.of(context)!.pleaseSelectLocation);
      return false;
    } else if (selectedUnitType == null) {
      ApplicationUtil.showSnackBar(
          context: context,
          message: AppLocalizations.of(context)!.equipmentCategoryIsRequired);
      return false;
    } else if (selectedPartItem == null) {
      ApplicationUtil.showSnackBar(
          context: context,
          message: AppLocalizations.of(context)!.partItemIsRequired);
      return false;
    } else if (equipmentNameController.text.trim().isEmpty) {
      ApplicationUtil.showSnackBar(
          context: context,
          message: AppLocalizations.of(context)!.equipmentNameIsRequired);

      return false;
    }
    /* else if (equipmentDescriptionController.text.trim().isEmpty) {
      ApplicationUtil.showSnackBar(context: context, message: AppLocalizations.of(context)!.equipmentDescIsRequired);

      return false;
    }*/
    return true;
  }

  void initBarcodeResponse() {
    barcodeResponse = getIt<BR.BarcodeResponse>().barcodeResponseInstance;
    extensionList = getExtensionData();
  }

  Future<void> _saveAssignServiceRequestInDatabase(
      List<Map<String, dynamic>> base64mapList) async {
    List<Map<String, dynamic>> itemList = [];
    if (selectedPartItem != null) {
      Map<String, dynamic> map = {
        'partid': selectedPartItem!.partId,
        'count': selectedPartItem!.count
      };
      itemList.add(map);
    }
    if (!widget.type.fromAudit) {
      int? userId = getIt<SharedPreferences>().getInt('userId');

      ServiceRequestCompanion serviceRequestCompanion = ServiceRequestCompanion(
        locationId: moor.Value(selectedLocationId),
        newLocationId: moor.Value(selectedLocationId),
        newBarcode: moor.Value(widget.type.barcodeNumber),
        createdAt: moor.Value(DateTime.now()),
        equipmentBarcodeNumber: moor.Value(widget.type.barcodeNumber),
        /* equipmentId:
            moor.Value(barcodeResponse!.equipment!.equipmentId.toString()),*/
        extn: moor.Value(json.encode(extensionMap)),
        description: moor.Value(equipmentDescriptionController.text),
        createdBy: moor.Value(userId),
        status: const moor.Value(0),
        //customerId: moor.Value(barcodeResponse!.equipment!.customerId),
        requestType: const moor.Value('EQUIPMENT'),
        equipmentName: moor.Value(equipmentNameController.text.trim()),
        equipmentCategory: moor.Value(selectedUnitType!.categoryId),
        document: moor.Value(json.encode(base64mapList)),
        parts: moor.Value(json.encode(itemList)),
        latitude: moor.Value(locationProvider?.currentPosition!.latitude),
        longitude: moor.Value(locationProvider?.currentPosition!.longitude),
      );
      serviceRequestBloc.add(
        SaveServiceRequest(serviceRequestCompanion: serviceRequestCompanion),
      );
    } else {
      ApplicationUtil.showLoaderDialog(
          context, AppLocalizations.of(context)!.pleaseWait);
      var basicAuth = await _getBasicAuth();
      Map<String, String> requestHeaders = {
        'Content-type': 'application/json',
        'Accept': 'application/json',
        'Authorization': basicAuth
      };
      try {
        dynamic body = {
          'CURRENT_LOCATION_ID': selectedLocationId,
          'NEW_LOCATION_ID': selectedLocationId,
          'DESCRIPTION': equipmentDescriptionController.text,
          'EXTN': json.encode(extensionMap) != null
              ? json.decode(json.encode(extensionMap))
              : null,
          'NEW_BARCODE': widget.type.barcodeNumber,
          'REQUEST_TYPE': 'EQUIPMENT',
          'DOCUMENT': json.encode(base64mapList) != null
              ? json.decode(json.encode(base64mapList))
              : null,
          'PARTS': itemList,
        };
        body['EQUIPMENT_NAME'] = equipmentNameController.text.trim();
        body['EQUIPMENT_CATEGORY'] = selectedUnitType!.categoryId;

        if (locationProvider?.currentPosition!.latitude != null) {
          body['LATITUDE'] = locationProvider?.currentPosition!.latitude;
        }
        if (locationProvider?.currentPosition!.longitude != null) {
          body['LONGITUDE'] = locationProvider?.currentPosition!.longitude;
        }

        final response = await http.post(
            Uri.parse("${AppConstant.BASE_URL}/servicerequest"),
            headers: requestHeaders,
            body: json.encode(body));
        Logger.i(
            "${ApplicationUtil.getFormattedCurrentDateAndTime()}${AppConstant.BASE_URL}/servicerequest  Conversation id: ${response.headers['conversation-id'].toString()}");

        log('======SERVICE REQUEST ISOLATE===========');
        log(json.encode(body));
        log('http respond');
        log('${AppConstant.BASE_URL}/servicerequest');
        log(response.statusCode.toString());
        Navigator.pop(context);
        if (response.statusCode == 204) {
          log('http 204');
          Navigator.of(context).pop(response.statusCode);
        } else if (response.statusCode == 401) {
          Logger.e('======Unknown Error======\n' + response.body);
          Logger.e(
              '======Response Code======\n' + response.statusCode.toString());
        } else if (response.statusCode == 500) {
          Logger.e('======Unknown Error======\n' + response.body);
          Logger.e(
              '======Response Code======\n' + response.statusCode.toString());
        } else if (response.statusCode == 400) {
          Logger.e('======Unknown Error======\n' + response.body);
          Logger.e(
              '======Response Code======\n' + response.statusCode.toString());
        } else {
          Logger.e('======Unknown Error======\n' + response.body);
          Logger.e(
              '======Response Code======\n' + response.statusCode.toString());
        }
      } catch (e) {
        log(e.toString());
      }
    }
  }

  _getBasicAuth() {
    return getIt<SharedPreferences>().getString('token');
    //return base64Encode(utf8.encode('$token'));
  }

  _buildConfirmAndSubmitButton() {
    return ElevatedButton(
      style: ButtonStyle(
        backgroundColor: MaterialStateProperty.all(Colors.green),
        shape: MaterialStateProperty.all(
          const RoundedRectangleBorder(
            borderRadius: BorderRadius.all(
              Radius.circular(14),
            ),
          ),
        ),
      ),
      onPressed: () async {
        if (locationProvider?.currentPosition == null) {
          setState(() {
            fetchingLocation = true;
          });

          ///Fetching the location details using GeoLocator
          var locationDetail = await ApplicationUtil.getGeoLocatorLocation();
          setState(() {
            fetchingLocation = false;
          });
          //Taking too long to fetch the location
          if (locationDetail.runtimeType == String && locationDetail == "TIMEOUT") {
            ApplicationUtil.showSnackBar(
                context: context,
                message: "Fetching Location is taking time please try again");
            return;
          } else if(locationDetail.runtimeType == String && locationDetail == "WEB"){
            Logger.i("Web platform detected - skipping location validation continue submit");
          }else {
            if (locationDetail != null) {
              Logger.i("Lat: ${locationDetail!.latitude}, Long: ${locationDetail.longitude}, Accuracy: ${locationDetail.accuracy}");
              //Update the location provider with the new location
              locationProvider?.mapLocationDataToCurrentPosition(locationDetail);
              //Success case - to save data
              _handleSubmitButton();
              return;
            } else {
              Logger.i("Unable to get the location in assign_equipment_page");
              ApplicationUtil.showWarningAlertDialog(
                context,
                title: AppLocalizations.of(context)!.locationPermissionRequired,
                desc: kIsWeb
                    ? AppLocalizations.of(context)!.appRequiresLocationPermissionforweb
                    : AppLocalizations.of(context)!.appRequiresLocationPermission,
                negativeLabel: AppLocalizations.of(context)!.okay,
              );
              return;
            }
          }
        } 
        _handleSubmitButton();
      },
      child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 0),
          child: fetchingLocation
              ? const SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                    color: Colors.white,
                    strokeWidth: 2,
                  ),
              )
              : Text(
                  AppLocalizations.of(context)!.complete,
                  style: const TextStyle(fontSize: 18, color: Colors.white),
                )),
    );
  }

void _handleSubmitButton() {
    if (_validateForm()) {
      Logger.i("Started _handleSubmitButton in assign_equipment_page");
      dynamic response = validateExtensionMap(extensionMap);
      if (response is bool) {
        return;
      }
      List<Map<String, dynamic>> base64MapList = [];
      for (var element in imageList) {
        var imageMap = {
          "DOCUMENT_TYPE": "image/jpeg",
          "DOCUMENT_BLOB": element
        };
        base64MapList.add(imageMap);
      }
      if (base64MapList.isEmpty) {
        ApplicationUtil.showMissingPhotosWarningDialog(
          context,
          message: AppLocalizations.of(context)!
              .areYouSureAssignEquipmentWithoutImage,
          onSubmit: () async {
            _saveAssignServiceRequestInDatabase(base64MapList);
          },
          onCancel: () {},
        );
      } else {
        _saveAssignServiceRequestInDatabase(base64MapList);
      }
    }
  }
  _buildProgressButton() {
    return const SizedBox(
      width: double.infinity,
      child: Center(
        child: SizedBox(
          width: 30,
          height: 30,
          child: CircularProgressIndicator(),
        ),
      ),
    );
  }

  getExtensionData() {
    List<Extension> extensionList = [];
    Map<String, dynamic>? data = getIt<Customization>().customizationData;
    if (data != null) {
      if (getIt<ServiceType>().type == ServiceRequestType.ASSIGN_EQUIPMENT) {
        if (data.containsKey("EXTENSIONS")) {
          if (data['EXTENSIONS'].containsKey('EQUIPMENT')) {
            Map<String, dynamic> sectionMap = data['EXTENSIONS']['EQUIPMENT'];
            for (var key in sectionMap.keys) {
              Extension extension = Extension();
              extension.title = key;
              List<dynamic> sectionDetailList = sectionMap[key];
              List<ExtensionContent> extensionContentList = [];
              for (var mapData in sectionDetailList) {
                extensionContentList.add(ExtensionContent.fromMap(mapData));
              }
              extension.extensionContent = extensionContentList;
              extensionList.add(extension);
            }
          }
        }
      }
    }
    return extensionList;
  }

  _generateExtensionUI() {
    Widget widget = ListView.builder(
      padding: const EdgeInsets.all(0),
      shrinkWrap: true,
      physics: const ScrollPhysics(),
      itemCount: extensionList.length,
      itemBuilder: (context, index) {
        Extension extension = extensionList[index];
        return _buildWidgetFromExtension(
            extension.title!, extension.extensionContent!);
      },
    );
    //isValueNotExist = false;
    return widget;
  }

  Widget _buildWidgetFromExtension(
      String title, List<ExtensionContent> extensionContent) {
    return Container(
      padding: const EdgeInsets.all(10),
      margin: const EdgeInsets.only(bottom: 10),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Theme.of(context).primaryColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _getSectionHeader(title),
          _getSectionBody(extensionContent, title),
        ],
      ),
    );
  }

  _getSectionBody(List<ExtensionContent> extensionContentList, String title) {
    if (extensionMap[title] == null) {
      extensionMap[title] = [];
    }
    for (var element in extensionContentList) {}
    return ListView.builder(
      shrinkWrap: true,
      physics: const ScrollPhysics(),
      itemCount: extensionContentList.length,
      itemBuilder: (context, index) {
        ExtensionContent extensionContent = extensionContentList[index];
        if (isValueNotExist) {
          extensionMap[title].add({
            extensionContent.fieldTechName: '',
            'FIELD_MANDATORY': extensionContent.fieldMandatory,
            'FIELD_NAME': extensionContent.fieldName,
            'FIELD_TECH_NAME': extensionContent.fieldTechName,
          });
        }
        if (extensionContent.fieldControl == "TEXT") {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 5,
              ),
              _getLabelBaseOnMandatory(extensionContent),
              Container(
                margin: const EdgeInsets.only(
                  top: 5,
                ),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(color: AppColor.greyBorderColor),
                ),
                child: TextField(
                  style: const TextStyle(height: 1.2),
                  decoration: const InputDecoration(
                    contentPadding: EdgeInsets.symmetric(horizontal: 5),
                    border: InputBorder.none,
                    //hintText: extensionContent.fieldName,
                  ),
                  onChanged: (value) {
                    extensionMap[title]
                        [index] = {extensionContent.fieldTechName: value};
                    print(extensionMap.toString());
                  },
                ),
              ),
              const SizedBox(
                height: 10,
              ),
            ],
          );
        } else if (extensionContent.fieldControl == "TEXTAREA") {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 5,
              ),
              _getLabelBaseOnMandatory(extensionContent),
              Container(
                margin: const EdgeInsets.symmetric(
                  vertical: 5,
                ),
                padding: const EdgeInsets.symmetric(vertical: 5),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(color: AppColor.greyBorderColor),
                ),
                child: TextField(
                  minLines: 3,
                  maxLines: 5,
                  style: const TextStyle(height: 1.2),
                  decoration: const InputDecoration(
                    contentPadding: EdgeInsets.symmetric(horizontal: 5),
                    border: InputBorder.none,
                    //  hintText: extensionContent.fieldName,
                  ),
                  onChanged: (value) {
                    extensionMap[title]
                        [index] = {extensionContent.fieldTechName: value};
                    print(extensionMap.toString());
                  },
                ),
              ),
              const SizedBox(
                height: 10,
              ),
            ],
          );
        } else if (extensionContent.fieldControl == "NUMBER") {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 5,
              ),
              _getLabelBaseOnMandatory(extensionContent),
              Container(
                margin: const EdgeInsets.symmetric(
                  vertical: 5,
                ),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(color: AppColor.greyBorderColor),
                ),
                child: TextField(
                  style: const TextStyle(height: 1.2),
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    contentPadding: const EdgeInsets.symmetric(horizontal: 5),
                    border: InputBorder.none,
                    hintText: extensionContent.fieldName,
                  ),
                  onChanged: (value) {
                    extensionMap[title]
                        [index] = {extensionContent.fieldTechName: value};
                    print(extensionMap.toString());
                  },
                ),
              ),
            ],
          );
        } else if (extensionContent.fieldControl == "TOGGLE") {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 5,
              ),
              _getLabelBaseOnMandatory(extensionContent),
              Container(
                  margin: const EdgeInsets.symmetric(
                    vertical: 5,
                  ),
                  padding: const EdgeInsets.symmetric(
                    vertical: 10,
                  ),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(color: AppColor.greyBorderColor),
                  ),
                  child: Row(
                    children: [
                      Switch(
                        value: _getSwitchValue(
                            extensionMap, title, index, extensionContent),
                        onChanged: (bool value) {
                          extensionMap[title][index] = {
                            extensionContent.fieldTechName: value.toString()
                          };
                          isValueNotExist = false;
                          setState(() {});
                        },
                      ),
                    ],
                  )),
            ],
          );
        } else if (extensionContent.fieldControl == "CHOICE") {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 5,
              ),
              _getLabelBaseOnMandatory(extensionContent),
              dropDownOption(extensionContent, index, title)
            ],
          );
        } else {
          return Container();
        }
      },
    );
  }

  dropDownOption(
    ExtensionContent extensionContent,
    int index,
    String title,
  ) {
    List<DropDownChoice> dropDownList = [];
    Map<String, dynamic>? data = getIt<Customization>().customizationData;
    if (data != null && data.containsKey("CHOICE")) {
      if (data['CHOICE'].containsKey(extensionContent.fieldChoiceType)) {
        List<dynamic> dropDownOptionList =
            data['CHOICE'][extensionContent.fieldChoiceType];
        dropDownList
            .add(DropDownChoice(choiceName: 'Select', choiceValue: 'select'));
        for (var choiceMap in dropDownOptionList) {
          dropDownList.add(DropDownChoice.fromMap(choiceMap));
        }
      }
    }

    return Container(
      margin: const EdgeInsets.only(top: 5),
      height: 45,
      decoration: BoxDecoration(
          color: Theme.of(context).primaryColor,
          borderRadius: BorderRadius.circular(15)),
      child: DropdownButton(
          underline: Container(),
          value: _getExtensionDropdownSelectedValue(
              dropDownList, title, index, extensionContent),
          hint: Center(
            child: Text(
              _getExtensionDropdownSelectedName(
                  dropDownList, title, index, extensionContent),
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
          icon: const Icon(
            // Add this
            Icons.arrow_drop_down,
            color: Colors.white,
            size: 40, // Add this
          ),
          isExpanded: true,
          items: dropDownList.map(
            (val) {
              return DropdownMenuItem(
                value: val.choiceValue,
                child: Text(
                  val.choiceName as String,
                  style: const TextStyle(color: Colors.black),
                ),
              );
            },
          ).toList(),
          selectedItemBuilder: (BuildContext ctxt) {
            return dropDownList.map<Widget>((DropDownChoice item) {
              return DropdownMenuItem(
                  child: Container(
                    margin: const EdgeInsets.only(left: 20),
                    child: Text("${item.choiceName}",
                        style: const TextStyle(
                            fontWeight: FontWeight.bold, color: Colors.white)),
                  ),
                  value: item);
            }).toList();
          },
          onChanged: (value) {
            extensionMap[title]
                [index] = {extensionContent.fieldTechName: value};
            print(extensionMap.toString());
            isValueNotExist = false;
            setState(() {});
          }),
    );
  }

  _getExtensionDropdownSelectedValue(List<DropDownChoice> dropDownList,
      String title, int index, ExtensionContent extensionContent) {
    String selected =
        extensionMap[title][index][extensionContent.fieldTechName];
    if (selected.isNotEmpty) return selected;
    return dropDownList[0].choiceValue;
  }

  String _getExtensionDropdownSelectedName(List<DropDownChoice> dropDownList,
      String title, int index, ExtensionContent extensionContent) {
    var selected = extensionMap[title][index][extensionContent.fieldTechName];
    print(selected);

    for (DropDownChoice choice in dropDownList) {
      if (choice.choiceValue == selected) {
        print('element.choiceName' + choice.choiceName!);
        return choice.choiceName!;
      }
    }
    return dropDownList[0].choiceName!;
  }

  _getSectionHeader(String title) => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
                color: Theme.of(context).primaryColor,
                fontSize: 18,
                fontWeight: FontWeight.bold),
          ),
          const SizedBox(
            height: 10,
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 5),
            child: const DottedDivider(
              color: AppColor.redColor,
            ),
          ),
        ],
      );

  _getSwitchValue(Map<String, dynamic> extensionMap, String title, int index,
      ExtensionContent extensionContent) {
    if (extensionMap[title][index][extensionContent.fieldTechName] == null ||
        extensionMap[title][index][extensionContent.fieldTechName].isEmpty) {
      extensionMap[title][index][extensionContent.fieldTechName] = 'false';
    }
    return extensionMap[title][index][extensionContent.fieldTechName] == 'true';
  }

  _getLabelBaseOnMandatory(ExtensionContent extensionContent) {
    return RichText(
      textAlign: TextAlign.start,
      text: TextSpan(style: const TextStyle(fontSize: 18), children: <TextSpan>[
        extensionContent.fieldMandatory == "Y"
            ? const TextSpan(
                text: "* ",
                style: TextStyle(
                    color: AppColor.redColor, fontWeight: FontWeight.bold),
              )
            : const TextSpan(text: ''),
        TextSpan(
          text: extensionContent.fieldName,
          style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColor.blackTextColor),
        ),
      ]),
    );
  }

  void _onImageButtonPressed(ImageSource source,
      {required BuildContext context, bool isMultiImage = false}) async {
    try {
      final pickedFile = await _picker.pickImage(
        source: source,
        imageQuality: 70,
        maxHeight: 480,
        maxWidth: 640,
      );
      if (kIsWeb) {
        final http.Response responseData =
            await http.get(Uri.parse(pickedFile!.path));
        var uint8list = responseData.bodyBytes;
        base64Img = base64Encode(uint8list);
      } else {
        var uint8list = File(pickedFile!.path).readAsBytesSync();
        base64Img = base64Encode(uint8list);
      }
      //NAVIGATE TO EDIT
      pickedImage = await Navigator.pushNamed(
          context, EditImageHomePage.routeName,
          arguments: base64Img) as String;
      if (pickedImage != null) {
        imageList.add(pickedImage);
        pickedImage = "";
        isValueNotExist = false;
        setState(() {});
      }
    } catch (e) {
      Logger.e(e.toString());
      setState(() {
        isValueNotExist = false;
      });
    }
  }

  dynamic validateExtensionMap(Map<String?, dynamic> extensionMap) {
    List<String> errorList = [];
    List<Map<String?, dynamic>> returnedMap = [];
    extensionMap.forEach((key, value) {
      List<dynamic> innerMap = extensionMap[key];
      for (var element in innerMap) {
        Map<String?, dynamic> map = element;
        if (map['FIELD_MANDATORY'] == 'Y' &&
            map[map['FIELD_TECH_NAME']].isEmpty) {
          errorList.add(map['FIELD_NAME']);
        } else {
          returnedMap.add(element);
        }
      }
    });
    if (errorList.isNotEmpty) {
      ApplicationUtil.showSnackBar(
          context: context, message: '${errorList[0]} is required');
      return true;
    }
    return returnedMap;
  }

  _getLocationDropdown() {
    if (widget.type.defaultLocationId != null) {
      selectedLocationId = widget.type.defaultLocationId!;
    }
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 15),
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Theme.of(context).primaryColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          LocationDropdownWidget(
              title: widget.type.fromAudit == true
                  ? AppLocalizations.of(context)!.confirmLocation
                  : AppLocalizations.of(context)!.selectLocation,
              showRequiredSymbol: true,
              description: AppLocalizations.of(context)!.confirmLocationMessage,
              locationList: locationList,
              disableDropDown: widget.type.fromAudit == true ? true : false,
              defaultLocationId: widget.type.fromAudit == true
                  ? widget.type.defaultLocationId
                  : "",
              onLocationSelected: (location, name, category) {
                selectedLocationId = location;
              }),
        ],
      ),
    );
  }

  _equipmentDetailForm() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Theme.of(context).primaryColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _equipmentCategory(),
          const SizedBox(
            height: 10,
          ),
          const SizedBox(
            height: 10,
          ),
          _partItemListDropdown(),
          const SizedBox(
            height: 10,
          ),
          _equipmentNameWidget(),
          const SizedBox(
            height: 10,
          ),
          _equipmentDescriptionWidget(),
        ],
      ),
    );
  }

  _getPhotosUI() {
    return Container(
        padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
        width: double.infinity,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(15),
          border: Border.all(color: Theme.of(context).primaryColor),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                RichText(
                  textAlign: TextAlign.start,
                  text: TextSpan(
                      style: const TextStyle(fontSize: 18),
                      children: <TextSpan>[
                        TextSpan(
                          text: AppLocalizations.of(context)!.addPhotos,
                          style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).primaryColor),
                        ),
                      ]),
                ),
                TextButton(
                  style: ButtonStyle(
                    shape: MaterialStateProperty.all(
                      RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(15.0),
                        side: BorderSide(color: Theme.of(context).primaryColor),
                      ),
                    ),
                    padding: MaterialStateProperty.all(
                      const EdgeInsets.symmetric(horizontal: 10),
                    ),
                  ),
                  onPressed: () {
                    _onImageButtonPressed(ImageSource.gallery,
                        context: context);
                  },
                  child: Text(
                    AppLocalizations.of(context)!.browse,
                    style: const TextStyle(
                      fontSize: 18,
                    ),
                  ),
                )
              ],
            ),
            const SizedBox(
              height: 10,
            ),
            _getImages(),
            const SizedBox(
              height: 10,
            ),
          ],
        ));
  }

  _partItemListDropdown() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        RichText(
          textAlign: TextAlign.start,
          text: TextSpan(
              style: const TextStyle(fontSize: 18),
              children: <TextSpan>[
                const TextSpan(
                  text: "* ",
                  style: TextStyle(
                      color: AppColor.redColor, fontWeight: FontWeight.bold),
                ),
                TextSpan(
                  text: AppLocalizations.of(context)!.equipmentPart,
                  style: TextStyle(
                      color: Theme.of(context).primaryColor,
                      fontSize: 18,
                      fontWeight: FontWeight.bold),
                ),
              ]),
        ),
        const SizedBox(
          height: 5,
        ),
        BlocConsumer<PartItemsBloc, PartItemsState>(
          listener: (context, state) {
            if (state is BOMPartItemListError) {
              if (state.errorMessage == ApiResponse.INVALID_AUTH) {
                Navigator.pushNamedAndRemoveUntil(
                    context, LoginPage.routeName, (route) => false,
                    arguments: true);
              }
            }
          },
          // Inside your DropdownButton builder:
          builder: (context, state) {
            if (state is FetchingBOMPartItemList) {
              return const Center(
                child: CircularProgressIndicator(),
              );
            } else if (state is FetchedBOMPartItemList) {
              List<PartItemResponse> filteredPartItemList;
              if (selectedCategoryId != "") {
                filteredPartItemList = state.partItemList.where((partItem) {
                  // Check if equipmentCategoryIds is not null and contains the selectedCategoryId
                  return partItem.equipmentCategoryIds
                      .any((category) => category == selectedCategoryId);
                }).toList();
                if (filteredPartItemList.length == 1) {
                  selectedPartItem = filteredPartItemList[0];
                }
              } else {
                filteredPartItemList = state.partItemList;
              }
              if (filteredPartItemList.isEmpty) {
                filteredPartItemList = state.partItemList;
              }
              print("filteredPartItemList: $filteredPartItemList");
              if (filteredPartItemList.isEmpty) {
                return Container(
                  margin: const EdgeInsets.only(left: 10, top: 10, bottom: 10),
                  child: Text(
                    AppLocalizations.of(context)!.noPartFound,
                    style: const TextStyle(fontSize: 16),
                  ),
                );
              } else {
                return Container(
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: DropdownButton<PartItemResponse>(
                    underline: Container(),
                    value: selectedPartItem,
                    hint: Container(
                      margin: const EdgeInsets.symmetric(horizontal: 10),
                      alignment: Alignment.centerLeft,
                      child: Text(
                        AppLocalizations.of(context)!.select,
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    icon: const Icon(
                      Icons.arrow_drop_down,
                      color: Colors.white,
                      size: 35,
                    ),
                    isExpanded: true,
                    items: filteredPartItemList.map((val) {
                      return DropdownMenuItem<PartItemResponse>(
                        value: val,
                        key: Key(val.partId), // Ensure each key is unique
                        child: Text(
                          val.name,
                        ),
                      );
                    }).toList(),
                    selectedItemBuilder: (BuildContext ctxt) {
                      return filteredPartItemList!.map<Widget>((item) {
                        return DropdownMenuItem(
                            child: Container(
                              margin: const EdgeInsets.only(left: 20),
                              child: Text("${item.name}",
                                  style: const TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold)),
                            ),
                            value: item.name);
                      }).toList();
                    },
                    onChanged: (value) {
                      setState(() {
                        selectedPartItem = value!;
                      });
                    },
                  ),
                );
              }
            } else if (state is BOMPartItemListError) {
              return Center(
                child: Text(state.errorMessage),
              );
            }
            return const Center(child: CircularProgressIndicator());
          },
        ),
      ],
    );
  }
}
