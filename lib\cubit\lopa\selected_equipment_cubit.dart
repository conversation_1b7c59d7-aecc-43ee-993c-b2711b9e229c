import 'package:alink/data/model/repair_service_request.dart';
import 'package:alink/pages/airline/model/lopa_model.dart';
import 'package:alink/pages/airline/model/tail.dart';
import 'package:alink/util/application_util.dart';
import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';

part 'selected_equipment_state.dart';

class SelectedFleetCubit extends Cubit<SelectedEquipmentState> {
  static SeatDetail? selectedSeat;
  static List<Tail> tailList = [];
  static Tail? selectedTail;
  static FleetLocation fleetLocation = FleetLocation(
      fleetName: 'Fleet', subFleetName: 'Sub-Fleet', tailName: 'Tail');
  static List<SeatCategory> seatCategoryList = [];
  static List<RepairServiceRequest> serviceRequestList = [];
  SelectedFleetCubit() : super(SelectedEquipmentInitial());
}
