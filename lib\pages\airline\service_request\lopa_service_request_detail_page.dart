import 'dart:convert';
import 'package:alink/bloc/repair_db_bloc/repair_bloc.dart';
import 'package:alink/bloc/service/service_request_bloc.dart';
import 'package:alink/cubit/lopa/selected_equipment_cubit.dart';
import 'package:alink/data/model/choice.dart';
import 'package:alink/data/model/customization.dart';
import 'package:alink/data/model/drop_down_option.dart';
import 'package:alink/data/model/extension.dart';
import 'package:alink/database/database.dart';
import 'package:alink/pages/airline/service_request/lopa_service_request_page.dart';
import 'package:alink/pages/dashboard_page.dart';
import 'package:alink/pages/full_image_view.dart';
import 'package:alink/provider/locationProvider.dart';
import 'package:alink/service_locator.dart';
import 'package:alink/util/app_color.dart';
import 'package:alink/util/app_constant.dart';
import 'package:alink/util/application_util.dart';
import 'package:alink/widget/image_picker_afs.dart';
import 'package:alink/widget/seperator.dart';
import 'package:drift/drift.dart' as moor;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:image_picker/image_picker.dart';
import 'package:provider/provider.dart';
import '../../../logger/logger.dart';

import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';

class LopaServiceRequestPage extends StatefulWidget {
  static const routeName = "lopa-service-request";
  final LopaServiceRequestParameter lopaServiceRequestParameter;

  const LopaServiceRequestPage(
      {Key? key, required this.lopaServiceRequestParameter})
      : super(key: key);

  @override
  _LopaServiceRequestPageState createState() => _LopaServiceRequestPageState();
}

class _LopaServiceRequestPageState extends State<LopaServiceRequestPage>
    with TickerProviderStateMixin {
  List<Choice> taskList = [];
  String tailTag = ApplicationUtil.getTailString();
  String fleetTag = ApplicationUtil.getFleetString();
  String subFleetTag = ApplicationUtil.getSubFleetString();
  LocationProvider? locationProvider;

  // Default Radio Button Item
  Choice? radioItem;

  // Group Value for Radio Button.
  String? choiceValue;
  String? subChoiceValue;
  bool isValueNotExist = true;
  late TextEditingController _descriptionTextController;
  Logger logger = Logger();
  bool isRepairWorkStarted = false;
  final String guidId = const Uuid().v4();
  int? customerId = getIt<SharedPreferences>().getInt('customerId');

  //IMage and parts
  Map<String, List<Map<String, dynamic>>> partItemMap = {};
  Map<String, List<Map<String, dynamic>>> imageListMap = {};

  //EXTENION DATA
  List<Extension> extensionList = [];

  // Extension submit response map
  Map<String, dynamic> extensionMap = {};
  bool isSafetyIssue = false;

  @override
  void initState() {
    locationProvider = Provider.of<LocationProvider>(context, listen: false);
    taskList = setTaskListData();
    extensionList = getExtensionData();
    initExtensionMapData();
    _descriptionTextController = TextEditingController(text: '');
    super.initState();
  }

  ServiceRequestBloc get serviceRequestBloc =>
      BlocProvider.of<ServiceRequestBloc>(context);

  RepairBloc get repairBloc => BlocProvider.of<RepairBloc>(context);

  @override
  void dispose() {
    _descriptionTextController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    locationProvider = Provider.of<LocationProvider>(context);
    Logger.i(
        "Class Name: ${this.runtimeType.toString()} time: ${DateTime.now()}");
    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: WillPopScope(
        child: Scaffold(
          body: SafeArea(
            child: Center(
              child: Container(
                color: Colors.white,
                constraints: const BoxConstraints(maxWidth: 500),
                child: Column(
                  children: [
                    ApplicationUtil.displayNotificationWidgetIfExist(
                        context, LopaServiceRequestPage.routeName),
                    _lopaServiceRequestAppbar(),
                    !isRepairWorkStarted
                        ? _getServiceRequestFormBody()
                        : _getRepairBody(),
                  ],
                ),
              ),
            ),
          ),
          floatingActionButton: FloatingActionButton(
            onPressed: () {
              ApplicationUtil.showUnsavedWarningAlertDialog(context);
            },
            child: Container(
                margin: const EdgeInsets.only(right: 5),
                child: const FaIcon(FontAwesomeIcons.chevronLeft)),
          ),
        ),
        onWillPop: () async {
          ApplicationUtil.showUnsavedWarningAlertDialog(context);
          return false;
        },
      ),
    );
  }

  _getServiceRequestFormBody() => Expanded(
        child: SingleChildScrollView(
          physics: const BouncingScrollPhysics(
              parent: AlwaysScrollableScrollPhysics()),
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 10),
            child: Column(
              children: [
                const Column(
                  children: [
                    SizedBox(
                      height: 10,
                    ),
                  ],
                ),
                Column(
                  children: [
                    const SizedBox(
                      height: 10,
                    ),
                    _getIsSafetyIssueCheckBox(),
                    const Divider(),
                    Container(
                      padding: const EdgeInsets.symmetric(
                          vertical: 10, horizontal: 0),
                      width: double.infinity,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(15),
                        border:
                            Border.all(color: Theme.of(context).primaryColor),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(
                            height: 5,
                          ),
                          _getFleetAndSubFleetDetail(),
                          const SizedBox(
                            height: 10,
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 15),
                            child: const DottedDivider(
                              color: AppColor.redColor,
                            ),
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          //GET TASK FORM
                          _getTaskForms(),
                          choiceValue != null
                              ? _getOtherForm(context)
                              : Container(),
                        ],
                      ),
                    ),
                    const SizedBox(
                      height: 30,
                    ),
                  ],
                )
              ],
            ),
          ),
        ),
      );

  _getFleetAndSubFleetDetail() => Container(
        padding: const EdgeInsets.symmetric(horizontal: 15),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.lopaServiceRequestParameter.fleetLocation.fleetName,
                  style: const TextStyle(
                      color: AppColor.greyTextColor,
                      fontSize: 18,
                      fontWeight: FontWeight.bold),
                ),
                Text(
                  fleetTag,
                  style: const TextStyle(
                    fontSize: 12,
                    color: AppColor.greyTextColor,
                  ),
                ),
              ],
            ),
            const SizedBox(
              width: 20,
            ),
            widget.lopaServiceRequestParameter.fleetLocation.subFleetName !=
                    AppLocalizations.of(context)!.select
                ? Container(
                    margin: const EdgeInsets.only(right: 20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.lopaServiceRequestParameter.fleetLocation
                              .subFleetName,
                          style: const TextStyle(
                              color: AppColor.greyTextColor,
                              fontSize: 18,
                              fontWeight: FontWeight.bold),
                        ),
                        Text(
                          subFleetTag,
                          style: const TextStyle(
                            fontSize: 12,
                            color: AppColor.greyTextColor,
                          ),
                        ),
                      ],
                    ),
                  )
                : Container(),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.lopaServiceRequestParameter.fleetLocation.tailName,
                  style: const TextStyle(
                      color: AppColor.greyTextColor,
                      fontSize: 18,
                      fontWeight: FontWeight.bold),
                ),
                Text(
                  tailTag,
                  style: const TextStyle(
                    fontSize: 12,
                    color: AppColor.greyTextColor,
                  ),
                ),
              ],
            ),
          ],
        ),
      );

  _getTaskForms() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
            padding: const EdgeInsets.symmetric(horizontal: 15),
            child: RichText(
              textAlign: TextAlign.start,
              text: TextSpan(
                  style: const TextStyle(fontSize: 18),
                  children: <TextSpan>[
                    const TextSpan(
                      text: "* ",
                      style: TextStyle(
                          color: AppColor.redColor,
                          fontWeight: FontWeight.bold),
                    ),
                    TextSpan(
                      text: AppLocalizations.of(context)!.selectTask,
                      style: TextStyle(
                          color: Theme.of(context).primaryColor,
                          fontSize: 18,
                          fontWeight: FontWeight.bold),
                    ),
                  ]),
            )),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 5),
          child: Column(
            children: taskList
                .map(
                  (Choice data) => Column(
                    children: [
                      SizedBox(
                        height: 40,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Flexible(
                              fit: FlexFit.loose,
                              child: RadioListTile(
                                contentPadding: const EdgeInsets.all(0),
                                title: Transform.translate(
                                  offset: const Offset(-16, 0),
                                  child: Text(
                                    data.choiceName,
                                    textAlign: TextAlign.start,
                                    style: const TextStyle(
                                        color: AppColor.greyTextColor),
                                  ),
                                ),
                                groupValue: choiceValue,
                                value: data.choiceValue,
                                onChanged: (val) {
                                  setState(() {
                                    isValueNotExist = false;
                                    radioItem = data;
                                    choiceValue = data.choiceValue;
                                  });
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                      AnimatedSize(
                        curve: Curves.linearToEaseOut,

                        duration: const Duration(milliseconds: 400),
                        //margin: EdgeInsets.only(left: 30),
                        //vsync: this,
                        child: (data.subChoice != null &&
                                data.choiceValue == choiceValue)
                            ? Container(
                                margin: const EdgeInsets.only(left: 30),
                                child: Column(
                                  children: data.subChoice!
                                      .map((data) => SizedBox(
                                            height: 40,
                                            child: Row(
                                              children: [
                                                Flexible(
                                                  fit: FlexFit.loose,
                                                  child: RadioListTile(
                                                    title: Transform.translate(
                                                      offset:
                                                          const Offset(-16, 0),
                                                      child: Text(
                                                        data.choiceName,
                                                        style: const TextStyle(
                                                            color: AppColor
                                                                .greyTextColor),
                                                      ),
                                                    ),
                                                    groupValue: subChoiceValue,
                                                    value: data.choiceValue,
                                                    onChanged: (val) {
                                                      setState(() {
                                                        isValueNotExist = false;
                                                        subChoiceValue =
                                                            data.choiceValue;
                                                      });
                                                    },
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ))
                                      .toList(),
                                ),
                              )
                            : Container(),
                      ),
                    ],
                  ),
                )
                .toList(),
          ),
        ),
        const SizedBox(
          height: 20,
        ),
      ],
    );
  }

  setTaskListData() {
    List<Choice> choiceDataList = [];
    Map<String, dynamic>? data = getIt<Customization>().customizationData;
    if (data != null) {
      if (data.containsKey("CHOICE")) {
        if (data['CHOICE'].containsKey('TASK_TYPES')) {
          List<dynamic> serviceRequestChoiceDataList =
              data['CHOICE']['TASK_TYPES'];
          for (var choiceMap in serviceRequestChoiceDataList) {
            Choice choice = Choice(
                choiceValue: choiceMap['CHOICE_VALUE'],
                choiceName: choiceMap['CHOICE_NAME']);
            List<SubChoice> subChoiceList = [];
            if (choiceMap.containsKey('SUB_CHOICE')) {
              List<dynamic> serviceRequestSubChoiceDataList =
                  choiceMap['SUB_CHOICE'];
              for (var subChoiceMap in serviceRequestSubChoiceDataList) {
                SubChoice subChoice = SubChoice(
                    choiceName: subChoiceMap['CHOICE_NAME'],
                    choiceValue: subChoiceMap['CHOICE_VALUE']);
                subChoiceList.add(subChoice);
              }
            }
            choice.subChoice = subChoiceList;
            choiceDataList.add(choice);
          }
        }
      }
    }

    return choiceDataList;
  }

  _getOtherForm(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 15),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          choiceValue != 'BASELINE_PICTURES'
              ? _getDescriptionBox()
              : Container(),
          //getExtensionUI
          _generateExtensionUI(),
          //getPartList
          _getPartListBox(),
          //_getPartUsedBox(),
          const SizedBox(
            height: 10,
          ),

          _getFormsButtons(),
        ],
      ),
    );
  }

  _getImages(List<Map<String, dynamic>> imageList) {
    if (imageList.isNotEmpty) {
      return Container(
        margin: const EdgeInsets.only(bottom: 10),
        height: 70,
        child: ListView.builder(
          padding: EdgeInsets.zero,
          scrollDirection: Axis.horizontal,
          itemCount: imageList.length,
          itemBuilder: (context, index) {
            var base64Img = imageList[index]['DOCUMENT_BLOB'];

            if (isRepairWorkStarted) {
              if (index == 0) {
                return Row(
                  children: [
                    _imagePlaceHolder(imageList),
                    _getSingleImage(base64Img, index, imageList)
                  ],
                );
              }
            }

            if (base64Img != null) {
              return _getSingleImage(base64Img, index, imageList);
            }
            return Container();
          },
        ),
      );
    }
    if (isRepairWorkStarted) {
      return Padding(
        padding: const EdgeInsets.only(bottom: 10),
        child: Row(
          children: [_imagePlaceHolder(imageList)],
        ),
      );
    }
    return Container();
  }

  _getSingleImage(
          base64imageString, int index, List<Map<String, dynamic>> imageList) =>
      InkWell(
        onTap: () {
          print(index);
          Navigator.pushNamed(context, ImageViewPage.routeName,
              arguments: ImageWithTag(base64: base64imageString, index: index));
        },
        child: SizedBox(
          height: 75,
          child: Stack(
            alignment: Alignment.center,
            children: [
              InkWell(
                onTap: () {
                  Navigator.pushNamed(context, ImageViewPage.routeName,
                      arguments: ImageWithTag(
                          base64: base64imageString, index: index));
                },
                child: Container(
                  height: 65,
                  margin: const EdgeInsets.only(right: 8),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey),
                    borderRadius: BorderRadius.circular(5),
                  ),
                  child: ClipRRect(
                    borderRadius: const BorderRadius.all(Radius.circular(5)),
                    child: Image.memory(
                      base64Decode(base64imageString),
                      fit: BoxFit.cover,
                      height: 65,
                      width: 65,
                    ),
                  ),
                ),
              ),
              isRepairWorkStarted
                  ? Positioned(
                      top: -2,
                      right: 1,
                      child: InkWell(
                        onTap: () {
                          imageList.removeAt(index);
                          setState(() {});
                        },
                        child: Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(15),
                            color: Colors.white,
                          ),
                          child: const FaIcon(
                            FontAwesomeIcons.solidTimesCircle,
                            color: Colors.red,
                            size: 18,
                          ),
                        ),
                      ),
                    )
                  : Container(),
            ],
          ),
        ),
      );

  _getFormsButtons() => Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          BlocConsumer<ServiceRequestBloc, ServiceRequestState>(
            listener: (context, state) {
              print(state.toString());
              if (state is ServiceRequestError) {
                ApplicationUtil.showSnackBar(
                    context: context, message: state.error);
              }
              if (state is ServiceRequestSaved) {
                serviceRequestBloc.add(SendPendingServiceRequestToServer());
                if (widget.lopaServiceRequestParameter.isDirectRepair) {
                  isRepairWorkStarted = true;
                  //RESET IMAGES
                  imageListMap.forEach((key, value) {
                    imageListMap[key] = [];
                  });
                  setState(() {});
                } else if (widget.lopaServiceRequestParameter.isNotification) {
                  Navigator.pop(context);
                } else {
                  Navigator.pushNamedAndRemoveUntil(context,
                      DashboardPage.routeName, (Route<dynamic> route) => false);
                }
              }
            },
            builder: (context, state) {
              if (state is ServiceRequestInitial) {
                return _buildFinishButton();
              } else if (state is ServiceRequestSaving ||
                  state is ServiceRequestUpdating) {
                return _buildProgressButton();
              } else {
                return _buildFinishButton();
              }
            },
          ),
        ],
      );

  _buildFinishButton() {
    return ElevatedButton(
      style: ButtonStyle(
        backgroundColor: MaterialStateProperty.all(Colors.green),
        shape: MaterialStateProperty.all(
          const RoundedRectangleBorder(
            borderRadius: BorderRadius.all(
              Radius.circular(14),
            ),
          ),
        ),
      ),
      onPressed: () {
        //isRepairWorkStarted = true;
        isValueNotExist = false;
        subChoiceValue ??= choiceValue;
        if (locationProvider?.currentPosition == null) {
          ApplicationUtil.showWarningAlertDialog(
            context,
            title: AppLocalizations.of(context)!.locationPermissionRequired,
            desc: kIsWeb
                ? AppLocalizations.of(context)!
                    .appRequiresLocationPermissionforweb
                : AppLocalizations.of(context)!.appRequiresLocationPermission,
            negativeLabel: AppLocalizations.of(context)!.okay,
          );
        } else {
          if (choiceValue != 'BASELINE_PICTURES') {
            if (_descriptionTextController.text.isEmpty) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content:
                      Text(AppLocalizations.of(context)!.descriptionIsRequired),
                ),
              );
              return;
            }
            dynamic response = validateExtensionMap(extensionMap);
            if (response is bool) {
              return;
            }
          } else {
            dynamic response = validateExtensionMap(extensionMap);
            if (response is bool) {
              return;
            }
            bool isImageRequire = _checkIfEquipmentHasSingleImage();
            if (isImageRequire) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                      AppLocalizations.of(context)!.pleaseAddAtleastOnePhoto),
                ),
              );
              return;
            }
          }

          int? userId = getIt<SharedPreferences>().getInt('userId');
          Map<String, List<Map<String, dynamic>>> mapData = {};
          Map<String, List<Map<String, dynamic>>> imageData = {};
          for (var seatCategory in SelectedFleetCubit.seatCategoryList) {
            // LOOP EACH SEAT
            for (var seatDetail in seatCategory.seatDetailList) {
              List<Map<String, dynamic>> checkPartList = [];
              // LOOP EACH PART
              for (var part in seatDetail.partList) {
                Map<String, dynamic> partsMap = {};
                if (part.isChecked) {
                  partsMap['PartId'] = part.part1;
                  partsMap['Completed'] = false;
                  partsMap['LocationId'] = part.equipmentLocationId;
                  checkPartList.add(partsMap);
                }
              }
              if (checkPartList.isNotEmpty) {
                mapData[seatDetail.name] = checkPartList;
                imageData[seatDetail.name] = seatDetail.imageList;
              }
            }
          }
          //print(json.encode(mapData));
          if (widget.lopaServiceRequestParameter.isDirectRepair) {
            _saveDirectRepairServiceRequestInDatabase(
                mapData, userId, imageData);
          } else if (widget.lopaServiceRequestParameter.isNotification) {
            _saveNewNotificationRequestInDatabase(mapData, userId, imageData);
          } else {
            _saveNewServiceRequestInDatabase(mapData, userId, imageData);
          }
        }
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 0),
        child: Text(
          !widget.lopaServiceRequestParameter.isDirectRepair
              ? AppLocalizations.of(context)!.complete
              : AppLocalizations.of(context)!.startRepair,
          style: const TextStyle(color: Colors.white, fontSize: 18),
        ),
      ),
    );
  }

  _buildProgressButton() {
    return Center(
      child: Container(
        width: 30,
        height: 30,
        margin: const EdgeInsets.only(left: 40),
        child: const CircularProgressIndicator(),
      ),
    );
  }

  _lopaServiceRequestAppbar() => Container(
        margin: const EdgeInsets.only(left: 10),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Text(
              widget.lopaServiceRequestParameter.isNotification
                  ? AppLocalizations.of(context)!.createNotification
                  : AppLocalizations.of(context)!.serviceRequest,
              style: const TextStyle(
                  color: AppColor.blackTextColor,
                  fontSize: AppConstant.toolbarTitleFontSize,
                  fontWeight: FontWeight.bold),
            ),
          ],
        ),
      );

  _getDescriptionBox() => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          RichText(
            textAlign: TextAlign.start,
            text: TextSpan(
                style: const TextStyle(fontSize: 18),
                children: <TextSpan>[
                  const TextSpan(
                    text: "* ",
                    style: TextStyle(
                        color: AppColor.redColor, fontWeight: FontWeight.bold),
                  ),
                  TextSpan(
                    text: AppLocalizations.of(context)!.enterDescription,
                    style: TextStyle(
                        color: Theme.of(context).primaryColor,
                        fontSize: 18,
                        fontWeight: FontWeight.bold),
                  ),
                ]),
          ),
          const SizedBox(
            height: 5,
          ),
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(15),
              border: Border.all(color: AppColor.greyBorderColor),
            ),
            constraints: const BoxConstraints(minHeight: 120),
            child: TextFormField(
              controller: _descriptionTextController,
              keyboardType: TextInputType.multiline,
              textInputAction: TextInputAction.done,
              style: const TextStyle(fontSize: 18, height: 1.2),
              decoration: InputDecoration(
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.all(10),
                  hintStyle: TextStyle(
                    color: Colors.grey[500],
                    fontSize: 17,
                  ),
                  hintText:
                      AppLocalizations.of(context)!.egAllCushionsCoverDamage),
              maxLines: 6,
            ),
          )
        ],
      );

  _getPartListBox() {
    /// IF REPAIR WORK NOT START THEN UPDATE LIST BASE ON LOPA SEAT PARTS SELECTION
    if (!isRepairWorkStarted) {
      partItemMap = {};
      imageListMap = {};
      for (var seatCategory
          in widget.lopaServiceRequestParameter.seatCategoryList) {
        for (var seatDetail in seatCategory.seatDetailList) {
          List<Map<String, dynamic>> partList = [];
          for (var part in seatDetail.partList) {
            Map<String, dynamic> partMap = {};
            if (part.isChecked) {
              partMap['PartId'] = part.part1;
              partMap['Description'] = part.description;
              //FOR REPAIR
              partMap['Completed'] = part.isChecked;
              partMap['IsRepaired'] = false;
              partList.add(partMap);
            }
          }
          if (partList.isNotEmpty) {
            partItemMap[seatDetail.name] = partList;
            imageListMap[seatDetail.name] = seatDetail.imageList;
          }
        }
      }
    }

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 10),
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: Theme.of(context).primaryColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              RichText(
                textAlign: TextAlign.start,
                text: TextSpan(
                    style: const TextStyle(fontSize: 18),
                    children: <TextSpan>[
                      const TextSpan(
                        text: "* ",
                        style: TextStyle(
                            color: AppColor.redColor,
                            fontWeight: FontWeight.bold),
                      ),
                      TextSpan(
                        text: _getPartTitle(),
                        style: TextStyle(
                            color: Theme.of(context).primaryColor,
                            fontSize: 18,
                            fontWeight: FontWeight.bold),
                      ),
                    ]),
              ),
              !isRepairWorkStarted
                  ? InkWell(
                      onTap: () {
                        Navigator.pushNamed(context, DigitalLopaPage.routeName,
                                arguments: true)
                            .then((value) {
                          setState(() {});
                        });
                      },
                      child: Container(
                        decoration: BoxDecoration(
                            border: Border.all(
                                color: Theme.of(context).primaryColor),
                            borderRadius: BorderRadius.circular(5)),
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 5),
                        child: Text(
                          AppLocalizations.of(context)!.update,
                          style: TextStyle(
                              color: Theme.of(context).primaryColor,
                              letterSpacing: 1,
                              height: 1.2,
                              fontWeight: FontWeight.bold),
                        ),
                      ),
                    )
                  : Container(),
            ],
          ),
          const SizedBox(
            height: 10,
          ),
          const DottedDivider(
            color: AppColor.redColor,
          ),
          const SizedBox(
            height: 10,
          ),
          isRepairWorkStarted
              ? Text(
                  AppLocalizations.of(context)!
                      .noteSelectAllPartsThatAreReplaced,
                  style: const TextStyle(
                      fontSize: 16, fontWeight: FontWeight.bold, height: 1.2),
                )
              : Container(),
          const SizedBox(
            height: 5,
          ),
          Stack(
            children: [
              _getEquipmentAndPart(partItemMap, imageListMap),
            ],
          ),
        ],
      ),
    );
  }

  _getEquipmentAndPart(Map<String, List<Map<String, dynamic>>> partItemMap,
      Map<String, List<Map<String, dynamic>>> imageListMap) {
    return ListView.builder(
      padding: EdgeInsets.zero,
      shrinkWrap: true,
      physics: const ScrollPhysics(),
      itemCount: partItemMap.length,
      itemBuilder: (context, index) {
        String key = partItemMap.keys.elementAt(index);
        List<Map<String, dynamic>> partList = partItemMap[key] ?? [];
        List<Map<String, dynamic>> damageImageList = imageListMap[key] ?? [];
        return Container(
          margin: const EdgeInsets.symmetric(vertical: 5),
          child: Stack(
            alignment: Alignment.topLeft,
            children: [
              Container(
                padding:
                    const EdgeInsets.symmetric(vertical: 0, horizontal: 10),
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(color: Theme.of(context).primaryColor),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const SizedBox(
                      height: 30,
                    ),
                    ListView.separated(
                      padding: EdgeInsets.zero,
                      shrinkWrap: true,
                      physics: const ScrollPhysics(),
                      itemCount: partList.length,
                      itemBuilder: (context, index) {
                        Map<String, dynamic> partMap = partList[index];
                        return Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  partMap['Description'],
                                  style: const TextStyle(fontSize: 18),
                                ),
                                const SizedBox(
                                  height: 3,
                                ),
                                Text(
                                  partMap['PartId'],
                                  style: const TextStyle(fontSize: 16),
                                ),
                              ],
                            ),
                            isRepairWorkStarted
                                ? _getCheckBoxIfRepair(partMap)
                                : Container()
                          ],
                        );
                      },
                      separatorBuilder: (BuildContext context, int index) {
                        return const Divider();
                      },
                    ),
                    const SizedBox(
                      height: 5,
                    ),
                    const Divider(),
                    if (choiceValue == 'BASELINE_PICTURES' &&
                        damageImageList.isEmpty)
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 5),
                        child: Text(
                          AppLocalizations.of(context)!
                              .atleastOnePhotosIsRequired,
                          textAlign: TextAlign.center,
                          style:
                              const TextStyle(fontSize: 16, color: Colors.grey),
                        ),
                      ),
                    _getImages(damageImageList),
                  ],
                ),
              ),
              Container(
                padding:
                    const EdgeInsets.symmetric(vertical: 5, horizontal: 10),
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor,
                  borderRadius:
                      const BorderRadius.only(topLeft: Radius.circular(10)),
                  border: Border.all(color: Theme.of(context).primaryColor),
                ),
                child: Text(
                  '${AppLocalizations.of(context)!.seat} $key',
                  style: const TextStyle(color: Colors.white),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  String _getLocationId() {
    FleetLocation fleetLocation = SelectedFleetCubit.fleetLocation;
    String locationId = fleetLocation.fleetName;
    if (fleetLocation.subFleetName != 'Select') {
      if (fleetLocation.subFleetName.contains('-')) {
        locationId += '-' + fleetLocation.subFleetName.replaceAll('-', '_');
      } else {
        locationId += '-' + fleetLocation.subFleetName;
      }
    }
    locationId += '-' + fleetLocation.tailName;
    return locationId;
  }

  void _saveNewServiceRequestInDatabase(
      Map<String, List<Map<String, dynamic>>> mapData,
      int? userId,
      Map<String, List<Map<String, dynamic>>> imageData) {
    print(json.encode(mapData));
    if (mapData.isNotEmpty) {
      serviceRequestBloc.add(
        SaveServiceRequest(
          serviceRequestCompanion: ServiceRequestCompanion(
            locationId: moor.Value(_getLocationId()),
            parts: moor.Value(json.encode(mapData)),
            newLocationId: const moor.Value(null),
            createdAt: moor.Value(DateTime.now()),
            equipmentBarcodeNumber: const moor.Value(null),
            equipmentId: const moor.Value(null),
            extn: moor.Value(json.encode(extensionMap)),
            choiceId: const moor.Value(1),
            description: moor.Value(_descriptionTextController.text),
            createdBy: moor.Value(userId),
            status: const moor.Value(0),
            customerId: const moor.Value(null),
            requestChoiceType: moor.Value(subChoiceValue),
            requestType: const moor.Value('SERVICE_REQUEST'),
            equipmentName: const moor.Value(null),
            equipmentCategory: const moor.Value(null),
            document: moor.Value(json.encode(imageData)),
            latitude: moor.Value(locationProvider?.currentPosition!.latitude),
            longitude: moor.Value(locationProvider?.currentPosition!.longitude),
            safety: moor.Value(isSafetyIssue),
          ),
        ),
      );
    }
  }

  _getRepairBody() {
    return Expanded(
      child: SingleChildScrollView(
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 10),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _getPartListBox(),
              BlocConsumer<RepairBloc, RepairState>(
                listener: (context, state) {
                  if (state is RepairError) {
                    ApplicationUtil.showSnackBar(
                        context: context, message: state.error);
                  }
                  if (state is RepairSaved) {
                    repairBloc.add(SendPendingRepairToServer());
                    // Navigator.popAndPushNamed(context, DashboardPage.routeName);
                    /*Navigator.pushNamedAndRemoveUntil(context, DashboardPage.routeName,
                    (Route<dynamic> route) => false);*/
                    Navigator.pop(context, false);
                  }
                },
                builder: (context, state) {
                  return ElevatedButton(
                    style: ButtonStyle(
                      backgroundColor: MaterialStateProperty.all(Colors.green),
                      shape: MaterialStateProperty.all(
                        const RoundedRectangleBorder(
                          borderRadius: BorderRadius.all(
                            Radius.circular(10),
                          ),
                        ),
                      ),
                    ),
                    onPressed: () {
                      if (locationProvider?.currentPosition == null) {
                        ApplicationUtil.showWarningAlertDialog(
                          context,
                          title: AppLocalizations.of(context)!
                              .locationPermissionRequired,
                          desc: kIsWeb
                              ? AppLocalizations.of(context)!
                                  .appRequiresLocationPermissionforweb
                              : AppLocalizations.of(context)!
                                  .appRequiresLocationPermission,
                          negativeLabel: AppLocalizations.of(context)!.okay,
                        );
                      } else {
                        Map<String, List<Map<String, dynamic>>> finalPartsMap =
                            {};
                        partItemMap.forEach((key, value) {
                          List<Map<String, dynamic>> finalMapList = [];
                          for (var singleParts in value) {
                            Map<String, dynamic> finalInnerMap = {};
                            finalInnerMap['PartId'] = singleParts['PartId'];
                            finalInnerMap['Completed'] =
                                singleParts['Completed'];
                            finalInnerMap['IsRepaired'] =
                                singleParts['IsRepaired'];
                            finalMapList.add(finalInnerMap);
                          }
                          finalPartsMap[key] = finalMapList;
                        });
                        //print(json.encode(finalPartsMap));
                        _saveRepairInDataBase(finalPartsMap, imageListMap);
                      }
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          vertical: 12, horizontal: 0),
                      child: Text(
                        AppLocalizations.of(context)!.complete,
                        style:
                            const TextStyle(color: Colors.white, fontSize: 18),
                      ),
                    ),
                  );
                },
              ),
              const SizedBox(
                height: 10,
              ),
            ],
          ),
        ),
      ),
    );
  }

  _imagePlaceHolder(List<Map<String, dynamic>> imageList) => InkWell(
        onTap: () async {
          PermissionStatus cameraPermission = await Permission.camera.status;
          //If permission is ask before once
          bool isShown = await Permission.camera.shouldShowRequestRationale;
          if (!isShown && cameraPermission.isDenied ||
              (cameraPermission.isGranted)) {
            AfsImagePicker.onImageButtonPressed(
              ImageSource.camera,
              context: context,
              onReturn: (editedImageBase64) {
                imageList.add({
                  "DOCUMENT_TYPE": "image/jpeg",
                  "DOCUMENT_BLOB": editedImageBase64
                });
                setState(() {});
              },
            );
          } else {
            AfsImagePicker.askCameraPermission(context);
          }
        },
        child: Container(
          margin: const EdgeInsets.only(
            right: 10,
          ),
          decoration: BoxDecoration(
              border: Border.all(color: Colors.grey),
              borderRadius: BorderRadius.circular(15)),
          width: 65,
          height: 65,
          child: Center(
            child: FaIcon(
              FontAwesomeIcons.solidCamera,
              size: 25,
              color: Theme.of(context).primaryColor,
            ),
          ),
        ),
      );

  void _saveDirectRepairServiceRequestInDatabase(
      Map<String, List<Map<String, dynamic>>> mapData,
      int? userId,
      Map<String, List<Map<String, dynamic>>> imageData) {
    serviceRequestBloc.add(
      SaveServiceRequest(
        serviceRequestCompanion: ServiceRequestCompanion(
          locationId: moor.Value(_getLocationId()),
          parts: moor.Value(json.encode(mapData)),
          newLocationId: const moor.Value(null),
          createdAt: moor.Value(DateTime.now()),
          equipmentBarcodeNumber: const moor.Value(null),
          equipmentId: const moor.Value(null),
          //extn: moor.Value(json.encode(extensionMap)),
          choiceId: const moor.Value(1),
          description: moor.Value(_descriptionTextController.text),
          createdBy: moor.Value(userId),
          status: const moor.Value(0),
          customerId: const moor.Value(null),
          requestChoiceType: moor.Value(subChoiceValue),
          requestType: const moor.Value('SERVICE_REQUEST'),
          equipmentName: const moor.Value(null),
          equipmentCategory: const moor.Value(null),
          document: moor.Value(json.encode(imageData)),
          latitude: moor.Value(locationProvider?.currentPosition!.latitude),
          longitude: moor.Value(locationProvider?.currentPosition!.longitude),
          refId: moor.Value(guidId),
          //safety: moor.Value(isSafetyIssue),
        ),
      ),
    );
  }

  void _saveRepairInDataBase(Map<String, List<Map<String, dynamic>>> mapData,
      Map<String, List<Map<String, dynamic>>> imageData) {
    int? userId = getIt<SharedPreferences>().getInt('userId');
    repairBloc.add(
      SaveRepairRequest(
        repairCompanion: RepairCompanion(
            createdAt: moor.Value(DateTime.now()),
            createdBy: moor.Value(userId),
            currentLocationId: moor.Value(_getLocationId()),
            newLocationId: const moor.Value(null),
            parts: moor.Value(json.encode(mapData)),
            //extn: moor.Value(json.encode(extensionMap)),
            repairDocument: moor.Value(json.encode(imageData)),
            customerId: const moor.Value(null),
            equipmentId: const moor.Value(null),
            //requestId: moor.Value(startRepairBtnCubit.selectedRequestDetail!.requestId!),
            refId: moor.Value(guidId),
            latitude: moor.Value(
              locationProvider?.currentPosition!.latitude,
            ),
            longitude: moor.Value(
              locationProvider?.currentPosition!.longitude,
            )),
      ),
    );
  }

  _getCheckBoxIfRepair(Map<String, dynamic> partMap) {
    return InkWell(
      onTap: () {
        partMap['Completed'] = !partMap['Completed'];
        setState(() {});
      },
      child: partMap['Completed']
          ? Icon(
              Icons.check_box,
              color: Theme.of(context).primaryColor,
            )
          : Icon(
              Icons.check_box_outline_blank,
              color: Theme.of(context).primaryColor,
            ),
    );
  }

  void _saveNewNotificationRequestInDatabase(
      Map<String, List<Map<String, dynamic>>> mapData,
      int? userId,
      Map<String, List<Map<String, dynamic>>> imageData) {
    if (mapData.isNotEmpty) {
      serviceRequestBloc.add(
        SaveServiceRequest(
          serviceRequestCompanion: ServiceRequestCompanion(
            locationId: moor.Value(_getLocationId()),
            parts: moor.Value(json.encode(mapData)),
            newLocationId: const moor.Value(null),
            createdAt: moor.Value(DateTime.now()),
            equipmentBarcodeNumber: const moor.Value(null),
            equipmentId: const moor.Value(null),
            //extn: moor.Value(json.encode(extensionMap)),
            choiceId: const moor.Value(1),
            description: moor.Value(_descriptionTextController.text),
            createdBy: moor.Value(userId),
            status: const moor.Value(0),
            customerId: const moor.Value(null),
            requestChoiceType: moor.Value(subChoiceValue),
            requestType: const moor.Value('NOTIFICATION'),
            equipmentName: const moor.Value(null),
            equipmentCategory: const moor.Value(null),
            document: moor.Value(json.encode(imageData)),
            latitude: moor.Value(locationProvider?.currentPosition!.latitude),
            longitude: moor.Value(locationProvider?.currentPosition!.longitude),
          ),
        ),
      );
    }
  }

  _getPartTitle() {
    if (isRepairWorkStarted) {
      return AppLocalizations.of(context)!.repairParts;
    } else if (widget.lopaServiceRequestParameter.isNotification) {
      return AppLocalizations.of(context)!.partsSuggested;
    } else if (choiceValue == 'BASELINE_PICTURES') {
      return AppLocalizations.of(context)!.baseLinePhotos;
    }
    return AppLocalizations.of(context)!.damageParts;
  }

  bool _checkIfEquipmentHasSingleImage() {
    bool isImageRequired = false;

    for (int i = 0; i < SelectedFleetCubit.seatCategoryList.length; i++) {
      for (var j = 0;
          j < SelectedFleetCubit.seatCategoryList[i].seatDetailList.length;
          j++) {
        bool atleatOnePartIsChecked = SelectedFleetCubit
            .seatCategoryList[i].seatDetailList[j].partList
            .where((element) => element.isChecked == true)
            .isNotEmpty;
        if (atleatOnePartIsChecked) {
          if (SelectedFleetCubit
              .seatCategoryList[i].seatDetailList[j].imageList.isEmpty) {
            isImageRequired = true;
            break;
          }
        }
      }
    }
    return isImageRequired;
  }

  getExtensionData() {
    List<Extension> extensionList = [];
    Map<String, dynamic>? data = getIt<Customization>().customizationData;
    if (data != null) {
      if (data.containsKey("EXTENSIONS")) {
        if (data['EXTENSIONS'].containsKey('SERVICE_REQUEST')) {
          Map<String, dynamic> sectionMap =
              data['EXTENSIONS']['SERVICE_REQUEST'];
          for (var key in sectionMap.keys) {
            Extension extension = Extension();
            extension.title = key;
            List<dynamic> sectionDetailList = sectionMap[key];
            List<ExtensionContent> extensionContentList = [];
            for (var mapData in sectionDetailList) {
              extensionContentList.add(ExtensionContent.fromMap(mapData));
            }
            extension.extensionContent = extensionContentList;
            extensionList.add(extension);
          }
        }
      }
    }
    return extensionList;
  }

/*------------------------------EXTENSION FIELD----------------------------*/
  _generateExtensionUI() {
    Widget widget = ListView.builder(
      padding: EdgeInsets.zero,
      shrinkWrap: true,
      physics: const ScrollPhysics(),
      itemCount: extensionList.length,
      itemBuilder: (context, index) {
        Extension extension = extensionList[index];
        return _buildWidgetFromExtension(
            extension.title!, extension.extensionContent!);
      },
    );
    //isValueNotExist = false;
    return widget;
  }

  Widget _buildWidgetFromExtension(
      String title, List<ExtensionContent> extensionContent) {
    return Container(
      padding: const EdgeInsets.all(10),
      margin: const EdgeInsets.only(top: 20),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Theme.of(context).primaryColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _getSectionHeader(title),
          _getSectionBody(extensionContent, title),
        ],
      ),
    );
  }

  _getSectionBody(List<ExtensionContent> extensionContentList, String title) {
    if (extensionMap[title] == null) {
      extensionMap[title] = [];
    }
    return ListView.builder(
      shrinkWrap: true,
      physics: const ScrollPhysics(),
      itemCount: extensionContentList.length,
      itemBuilder: (context, index) {
        ExtensionContent extensionContent = extensionContentList[index];

        if (extensionContent.fieldControl == "TEXT") {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 5,
              ),
              _getLabelBaseOnMandatory(extensionContent),
              Container(
                margin: const EdgeInsets.only(
                  top: 5,
                ),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(color: AppColor.greyBorderColor),
                ),
                child: TextField(
                  style: const TextStyle(height: 1.2),
                  decoration: const InputDecoration(
                    contentPadding: EdgeInsets.symmetric(horizontal: 5),
                    border: InputBorder.none,
                    //hintText: extensionContent.fieldName,
                  ),
                  onChanged: (value) {
                    extensionMap[title]
                        [index] = {extensionContent.fieldTechName: value};
                    print(extensionMap.toString());
                  },
                ),
              ),
              const SizedBox(
                height: 10,
              ),
            ],
          );
        } else if (extensionContent.fieldControl == "TEXTAREA") {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 5,
              ),
              _getLabelBaseOnMandatory(extensionContent),
              Container(
                margin: const EdgeInsets.symmetric(
                  vertical: 5,
                ),
                padding: const EdgeInsets.symmetric(vertical: 5),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(color: AppColor.greyBorderColor),
                ),
                child: TextField(
                  minLines: 3,
                  maxLines: 5,
                  style: const TextStyle(height: 1.2),
                  decoration: const InputDecoration(
                    contentPadding: EdgeInsets.symmetric(horizontal: 5),
                    border: InputBorder.none,
                    //  hintText: extensionContent.fieldName,
                  ),
                  onChanged: (value) {
                    extensionMap[title]
                        [index] = {extensionContent.fieldTechName: value};
                    print(extensionMap.toString());
                  },
                ),
              ),
              const SizedBox(
                height: 10,
              ),
            ],
          );
        } else if (extensionContent.fieldControl == "NUMBER") {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 5,
              ),
              _getLabelBaseOnMandatory(extensionContent),
              Container(
                margin: const EdgeInsets.symmetric(
                  vertical: 5,
                ),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(color: AppColor.greyBorderColor),
                ),
                child: TextField(
                  style: const TextStyle(height: 1.2),
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    contentPadding: const EdgeInsets.symmetric(horizontal: 5),
                    border: InputBorder.none,
                    hintText: extensionContent.fieldName,
                  ),
                  onChanged: (value) {
                    extensionMap[title]
                        [index] = {extensionContent.fieldTechName: value};
                    print(extensionMap.toString());
                  },
                ),
              ),
            ],
          );
        } else if (extensionContent.fieldControl == "TOGGLE") {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 5,
              ),
              _getLabelBaseOnMandatory(extensionContent),
              Container(
                  margin: const EdgeInsets.symmetric(
                    vertical: 5,
                  ),
                  padding: const EdgeInsets.symmetric(
                    vertical: 10,
                  ),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(color: AppColor.greyBorderColor),
                  ),
                  child: Row(
                    children: [
                      Switch(
                        value: _getSwitchValue(
                            extensionMap, title, index, extensionContent),
                        onChanged: (bool value) {
                          extensionMap[title][index] = {
                            extensionContent.fieldTechName: value.toString()
                          };
                          isValueNotExist = false;
                          setState(() {});
                        },
                      ),
                    ],
                  )),
            ],
          );
        } else if (extensionContent.fieldControl == "CHOICE") {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 5,
              ),
              _getLabelBaseOnMandatory(extensionContent),
              dropDownOption(extensionContent, index, title)
            ],
          );
        } else {
          return Container();
        }
      },
    );
  }

  _getSectionHeader(String title) => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
                color: Theme.of(context).primaryColor,
                fontSize: 18,
                fontWeight: FontWeight.bold),
          ),
          const SizedBox(
            height: 10,
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 5),
            child: const DottedDivider(
              color: AppColor.redColor,
            ),
          ),
        ],
      );

  _getSwitchValue(Map<String, dynamic> extensionMap, String title, int index,
      ExtensionContent extensionContent) {
    if (extensionMap[title][index][extensionContent.fieldTechName] == null ||
        extensionMap[title][index][extensionContent.fieldTechName].isEmpty) {
      extensionMap[title][index][extensionContent.fieldTechName] = 'false';
    }
    return extensionMap[title][index][extensionContent.fieldTechName] == 'true';
  }

  dropDownOption(
    ExtensionContent extensionContent,
    int index,
    String title,
  ) {
    List<DropDownChoice> dropDownList = [];
    Map<String, dynamic>? data = getIt<Customization>().customizationData;
    if (data != null && data.containsKey("CHOICE")) {
      if (data['CHOICE'].containsKey(extensionContent.fieldChoiceType)) {
        List<dynamic> dropDownOptionList =
            data['CHOICE'][extensionContent.fieldChoiceType];
        dropDownList
            .add(DropDownChoice(choiceName: 'Select', choiceValue: 'select'));
        for (var choiceMap in dropDownOptionList) {
          dropDownList.add(DropDownChoice.fromMap(choiceMap));
        }
      }
    }

    return Container(
      margin: const EdgeInsets.only(top: 5),
      height: 45,
      decoration: BoxDecoration(
          color: Theme.of(context).primaryColor,
          borderRadius: BorderRadius.circular(15)),
      child: DropdownButton(
          underline: Container(),
          value: _getExtensionDropdownSelectedValue(
              dropDownList, title, index, extensionContent),
          hint: Center(
            child: Text(
              _getExtensionDropdownSelectedName(
                  dropDownList, title, index, extensionContent),
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
          icon: const Icon(
            // Add this
            Icons.arrow_drop_down,
            color: Colors.white,
            size: 40, // Add this
          ),
          isExpanded: true,
          items: dropDownList.map(
            (val) {
              return DropdownMenuItem(
                value: val.choiceValue,
                child: Text(
                  val.choiceName as String,
                  style: const TextStyle(color: Colors.black),
                ),
              );
            },
          ).toList(),
          selectedItemBuilder: (BuildContext ctxt) {
            return dropDownList.map<Widget>((DropDownChoice item) {
              return DropdownMenuItem(
                  child: Container(
                    margin: const EdgeInsets.only(left: 20),
                    child: Text("${item.choiceName}",
                        style: const TextStyle(
                            fontWeight: FontWeight.bold, color: Colors.white)),
                  ),
                  value: item);
            }).toList();
          },
          onChanged: (value) {
            extensionMap[title][index][extensionContent.fieldTechName] = value;
            isValueNotExist = false;
            setState(() {});
          }),
    );
  }

  _getLabelBaseOnMandatory(ExtensionContent extensionContent) {
    return RichText(
      textAlign: TextAlign.start,
      text: TextSpan(style: const TextStyle(fontSize: 18), children: <TextSpan>[
        extensionContent.fieldMandatory == "Y"
            ? const TextSpan(
                text: "* ",
                style: TextStyle(
                    color: AppColor.redColor, fontWeight: FontWeight.bold),
              )
            : const TextSpan(text: ''),
        TextSpan(
          text: extensionContent.fieldName,
          style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColor.blackTextColor),
        ),
      ]),
    );
  }

  _getExtensionDropdownSelectedValue(List<DropDownChoice> dropDownList,
      String title, int index, ExtensionContent extensionContent) {
    String selected =
        extensionMap[title][index][extensionContent.fieldTechName];
    if (selected.isNotEmpty) return selected;
    return dropDownList[0].choiceValue;
  }

  String _getExtensionDropdownSelectedName(List<DropDownChoice> dropDownList,
      String title, int index, ExtensionContent extensionContent) {
    var selected = extensionMap[title][index][extensionContent.fieldTechName];
    print(selected);

    for (DropDownChoice choice in dropDownList) {
      if (choice.choiceValue == selected) {
        print('element.choiceName' + choice.choiceName!);
        return choice.choiceName!;
      }
    }
    return dropDownList[0].choiceName!;
  }

  void initExtensionMapData() {
    for (var extension in extensionList) {
      for (var extensionContent in extension.extensionContent!) {
        if (extensionMap[extension.title] == null) {
          extensionMap[extension.title!] = [];
        }
        extensionMap[extension.title].add({
          extensionContent.fieldTechName: '',
          'FIELD_MANDATORY': extensionContent.fieldMandatory,
          'FIELD_NAME': extensionContent.fieldName,
          'FIELD_TECH_NAME': extensionContent.fieldTechName,
        });
      }
    }
  }

  validateExtensionMap(Map<String, dynamic> extensionMap) {
    List<String> errorList = [];
    List<Map<String?, dynamic>> returnedMap = [];
    extensionMap.forEach((key, value) {
      List<dynamic> innerMap = extensionMap[key];
      for (var element in innerMap) {
        Map<String?, dynamic> map = element;
        if (map['FIELD_MANDATORY'] == 'Y') {
          if (map[map['FIELD_TECH_NAME']].isEmpty ||
              map[map['FIELD_TECH_NAME']] == 'select') {
            errorList.add(map['FIELD_NAME']);
          }
        } else {
          returnedMap.add(element);
        }
      }
    });
    if (errorList.isNotEmpty) {
      ApplicationUtil.showSnackBar(
          context: context, message: '${errorList[0]} is required');
      return true;
    }
    return returnedMap;
  }

  _getIsSafetyIssueCheckBox() {
    if (!widget.lopaServiceRequestParameter.isNotification) {
      return ListTile(
        contentPadding: const EdgeInsets.only(left: 5),
        visualDensity: const VisualDensity(horizontal: -3, vertical: -4),
        title: Text(
          AppLocalizations.of(context)!.markAsSafetyIssue,
          style: const TextStyle(
              fontSize: 18,
              color: AppColor.blackTextColor,
              fontWeight: FontWeight.bold),
        ),
        leading: Image.asset(
          width: 25,
          height: 25,
          "assets/images/hazard.png",
        ),
        trailing: Padding(
          padding: const EdgeInsets.only(left: 5),
          child: Checkbox(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(2.0),
              ),
              side: MaterialStateBorderSide.resolveWith(
                (states) => BorderSide(width: 1.5, color: AppColor.redColor),
              ),
              value: isSafetyIssue,
              checkColor: Colors.white,
              activeColor: AppColor.redColor,
              //fillColor: MaterialStateProperty.resolveWith(getColor),
              onChanged: (bool? value) {
                setState(() {
                  isSafetyIssue = value!;
                });
              }),
        ),
      );
    } else {
      return Container();
    }
  }

  Color getColor(Set<MaterialState> states) {
    const Set<MaterialState> interactiveStates = <MaterialState>{
      MaterialState.pressed,
      MaterialState.hovered,
      MaterialState.focused,
    };

    return AppColor.redColor;
  }
}
