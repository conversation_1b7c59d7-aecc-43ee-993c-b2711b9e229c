import 'package:equatable/equatable.dart';

class Choice {
  String choiceName;
  String choiceValue;
  List<SubChoice>? subChoice;
  Choice({required this.choiceName, required this.choiceValue, this.subChoice});
}

class SubChoice {
  String choiceValue;
  String choiceName;
  SubChoice({required this.choiceValue, required this.choiceName});
}

class LocationChoice extends Equatable {
  final String name;
  final String locationId;

  const LocationChoice({
    required this.name,
    required this.locationId,
  });

  @override
  List<Object> get props => [name, locationId];
}
