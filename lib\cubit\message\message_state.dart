part of 'message_cubit.dart';

abstract class MessageState extends Equatable {
  const MessageState();
}

class MessageInitial extends MessageState {
  @override
  List<Object?> get props => [];
}

class FetchingMessageData extends MessageState {
  @override
  List<Object?> get props => [];
}

class FetchedMessageData extends MessageState {
  final List<MessageData> messageDataList;
  const FetchedMessageData({required this.messageDataList});
  @override
  List<Object?> get props => [messageDataList];
}

class FetchMessageDataError extends MessageState {
  final String errorMessage;
  const FetchMessageDataError({required this.errorMessage});
  @override
  List<Object?> get props => [];
}
