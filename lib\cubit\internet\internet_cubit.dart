import 'dart:async';
import 'dart:io';

import 'package:alink/util/enums/app_enum.dart';
import 'package:bloc/bloc.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:meta/meta.dart';
import 'package:http/http.dart' as http;

part 'internet_state.dart';

class InternetCubit extends Cubit<InternetState> {
  final Connectivity connectivity;
  StreamSubscription? connectivityStreamSubscription;

  InternetCubit({required this.connectivity})
      : super(InternetConnected(connectionType: InternetConnection.CONNECTED)) {
    print('start monitoring');
    monitorInternetConnection();
  }

  StreamSubscription<ConnectivityResult> monitorInternetConnection() {
    return connectivityStreamSubscription =
        connectivity.onConnectivityChanged.listen((connectivityResult) async {
      if (connectivityResult == ConnectivityResult.wifi ||
          connectivityResult == ConnectivityResult.mobile) {
        if (Platform.isAndroid) {
          final result = await http.get(Uri.parse('https://www.google.com/'));
          if (result.statusCode == 200) {
            print("connected");
            emitInternetConnected(InternetConnection.CONNECTED);
          } else {
            emitInternetConnected(InternetConnection.DISCONNECTED);
          }
        } else {
          emitInternetConnected(InternetConnection.CONNECTED);
        }
      } else if (connectivityResult == ConnectivityResult.none) {
        emitInternetDisconnected();
      }
    });
  }

  void emitInternetConnected(InternetConnection _connectionType) =>
      emit(InternetConnected(connectionType: _connectionType));

  void emitInternetDisconnected() => emit(InternetDisconnected());
  @override
  Future<void> close() {
    connectivityStreamSubscription!.cancel();
    return super.close();
  }
}
