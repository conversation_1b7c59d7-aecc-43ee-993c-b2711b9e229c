{"hosting": {"public": "build/web", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}]}, "flutter": {"platforms": {"android": {"default": {"projectId": "afs-alink", "appId": "1:655096066337:android:974e59fa6645895f4a8e60", "fileOutput": "android/app/google-services.json"}}, "ios": {"default": {"projectId": "afs-alink", "appId": "1:655096066337:ios:acef1b92c7aeacb54a8e60", "uploadDebugSymbols": true, "fileOutput": "ios/Runner/GoogleService-Info.plist"}}, "dart": {"lib/firebase_options.dart": {"projectId": "afs-alink", "configurations": {"android": "1:655096066337:android:974e59fa6645895f4a8e60", "ios": "1:655096066337:ios:acef1b92c7aeacb54a8e60", "web": "1:655096066337:web:7953c9841471c0564a8e60"}}}}}}