import 'dart:convert';
import 'dart:html';
import 'dart:typed_data';
import 'dart:js' as js;

//WEB LOGIC IMPLEMENTATION TO AVOID COMPLILE TIME ERROR
class AppImplementation {
  static Future<void> downloadImage(Uint8List image) async {
    if (image != null) {
      try {
        final base64data = base64Encode(image);
        js.context.callMethod(
            'compressAndDownloadImage', ['data:image/jpeg;base64,$base64data']);
      } catch (e) {
        print(e);
      }
    }
  }

  static dynamic webCompressListener() {
    window.addEventListener("message", (event) {
      MessageEvent event2 = event as MessageEvent;
      Blob blob = event2.data;
      print(blob.type); // type/img
    });
  }
}
