name: font_awesome_flutter
description: The Font Awesome Icon pack available as Flutter Icons. Provides 1500 additional icons to use in your apps.
maintainer: <PERSON> (@michaelspiss)
homepage: https://github.com/fluttercommunity/font_awesome_flutter
version: 9.1.0

environment:
  sdk: ">=2.12.0 <3.0.0"

dependencies:
  flutter:
    sdk: flutter

dev_dependencies:
  recase: ^4.1.0
  flutter_test:
    sdk: flutter
  version: ^3.0.2

flutter:
  fonts:
    - family: FontAwesomeBrands
      fonts:
        - asset: lib/fonts/fa-brands-400.ttf
          weight: 400
    - family: FontAwesomeRegular
      fonts:
        - asset: lib/fonts/fa-regular-400.ttf
          weight: 400
    - family: FontAwesomeSolid
      fonts:
        - asset: lib/fonts/fa-solid-900.ttf
          weight: 900
# Uncomment the following lines to support pro icons
    - family: FontAwesomeLight
      fonts:
        - asset: lib/fonts/fa-light-300.ttf
          weight: 300
# Leave commented out if you don't own duotone icons
    - family: FontAwesomeDuotone
      fonts:
        - asset: lib/fonts/fa-duotone-900.ttf
          weight: 900
# Thin style is available in font awesome pro 6 and beyond
# Leave commented out if you don't own version 6
#    - family: FontAwesomeThin
#      fonts:
#        - asset: lib/fonts/fa-thin-100.ttf
#          weight: 100