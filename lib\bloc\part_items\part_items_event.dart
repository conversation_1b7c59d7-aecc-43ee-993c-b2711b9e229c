part of 'part_items_bloc.dart';

@immutable
abstract class PartItemsEvent {}

class FetchPartItemList extends PartItemsEvent {
  final bool refresh;
  final String query;
  final String? partId;
  FetchPartItemList({required this.refresh, required this.query, this.partId});
}

class FetchBOMPartList extends PartItemsEvent {}

class FetchAvailablePartList extends PartItemsEvent {
  final String query;
  FetchAvailablePartList({required this.query});
}
