import 'package:equatable/equatable.dart';

class AuditScoreOption extends Equatable {
  final String choiceName;
  final String choiceValue;
  final int choiceScore;
  final List<SubChoice> subChoiceList;

  const AuditScoreOption(
      {required this.choiceName,
      required this.choiceValue,
      required this.choiceScore,
      required this.subChoiceList});

  factory AuditScoreOption.fromJson(Map<String, dynamic> json) {
    return AuditScoreOption(
        choiceName: json['CHOICE_NAME'] as String,
        choiceValue: json['CHOICE_VALUE'] as String,
        choiceScore: json['CHOICE_SCORE'] as int,
        subChoiceList: json['SUB_CHOICE'] != null
            ? List<SubChoice>.from(
                json['SUB_CHOICE'].map((e) => SubChoice.fromJson(e))).toList()
            : <SubChoice>[]);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['CHOICE_NAME'] = choiceName;
    data['CHOICE_VALUE'] = choiceValue;
    data['CHOICE_SCORE'] = choiceScore;
    data['SUB_CHOICE'] = subChoiceList.map((v) => v.toJson()).toList();
    return data;
  }

  @override
  // TODO: implement props
  List<Object?> get props =>
      [choiceValue, choiceName, choiceScore, subChoiceList];
}

class SubChoice extends Equatable {
  final String choiceName;
  final String choiceValue;
  final int choiceScore;

  const SubChoice(
      {required this.choiceName,
      required this.choiceValue,
      required this.choiceScore});

  factory SubChoice.fromJson(Map<String, dynamic> json) {
    return SubChoice(
      choiceName: json['CHOICE_NAME'] as String,
      choiceValue: json['CHOICE_VALUE'] as String,
      choiceScore: json['CHOICE_SCORE'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['CHOICE_NAME'] = choiceName;
    data['CHOICE_VALUE'] = choiceValue;
    data['CHOICE_SCORE'] = choiceScore;
    return data;
  }

  @override
  List<Object?> get props => [choiceValue, choiceName, choiceScore];
}
