import 'dart:async';

import 'package:alink/bloc/api/api_bloc.dart';
import 'package:alink/bloc/repair_db_bloc/repair_bloc.dart';
import 'package:alink/bloc/repair_list_api/repair_list_api_bloc.dart';
import 'package:alink/cubit/filter/filter_cubit.dart';
import 'package:alink/cubit/pages/repair_detail/repair_detail_api_cubit.dart';
import 'package:alink/data/model/barcode_response.dart';
import 'package:alink/data/model/filter_data.dart';
import 'package:alink/data/model/repair_service_request.dart';
import 'package:alink/data/model/service_request_detail.dart';
import 'package:alink/database/database.dart';
import 'package:alink/pages/airport/repair/assign_equipment/assign_equipment_page.dart';
import 'package:alink/pages/airport/repair/repair_detail_page.dart';
import 'package:alink/pages/airport/service_request/add_service_request_page.dart';
import 'package:alink/pages/auth/login_page.dart';
import 'package:alink/scanner/barcode/ai_scanner_page.dart';
import 'package:alink/service_locator.dart';
import 'package:alink/util/app_color.dart';
import 'package:alink/util/app_constant.dart';
import 'package:alink/util/application_util.dart';
import 'package:alink/util/enums/app_enum.dart';
import 'package:alink/widget/image_view.dart';
import 'package:alink/widget/scan_barcode_button_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:location/location.dart' as gps;
import 'package:shared_preferences/shared_preferences.dart';

import '../../../cubit/location/location_cubit.dart';
import '../../../data/model/audit_location.dart';
import '../../../data/model/location_detail.dart';
import '../../../data/model/singleton_model.dart';
import '../../../data/repository/api_service.dart';
import '../../../logger/logger.dart';
import '../../../widget/location_widget.dart';

class TimedServiceRequests extends StatefulWidget {
  static const routeName = "timed-service-request";

  const TimedServiceRequests({Key? key}) : super(key: key);

  @override
  State<TimedServiceRequests> createState() => _TimedServiceRequestsState();
}

class _TimedServiceRequestsState extends State<TimedServiceRequests> {
  static const String className = '_TimedServiceRequestsState';
  var serviceRequestCount = 0;
  final scrollController = ScrollController();
  String query = "&requestType=SCHEDULED";
  List<LocationDetail> locationList = [];
  String selectedLocationId = '';
  bool isGetServiceCalled = false;
  gps.LocationData? locationData;

  void setupScrollController(context) {
    scrollController.addListener(() {
      if (scrollController.position.atEdge) {
        if (scrollController.position.pixels != 0) {
          repairListBloc
              .add(FetchRepairServiceRequestList(refresh: false, query: query));
        }
      }
    });
  }

  int? customerId = getIt<SharedPreferences>().getInt('customerId');

  LocationCubit get locationCubit => BlocProvider.of<LocationCubit>(context);

  RepairListApiBloc get repairListBloc =>
      BlocProvider.of<RepairListApiBloc>(context);

  ApiBloc get apiBloc => BlocProvider.of<ApiBloc>(context);

  RepairBloc get repairBloc => BlocProvider.of<RepairBloc>(context);

  FilterCubit get filterCubit => BlocProvider.of<FilterCubit>(context);

  RepairDetailApiCubit get repairDetailApiCubit =>
      BlocProvider.of<RepairDetailApiCubit>(context);

  @override
  void initState() {
    locationCubit.getLocationData();
    //repairListBloc.add(FetchRepairServiceRequestList(refresh: true, query: query));
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    Logger.i("Class Name: " + className);
    setupScrollController(context);
    return Scaffold(
      body: SafeArea(
        child: Center(
          child: Stack(
            alignment: Alignment.center,
            children: [
              Container(
                constraints: const BoxConstraints(maxWidth: 500),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    ApplicationUtil.displayNotificationWidgetIfExist(
                        context, TimedServiceRequests.routeName),
                    _repairAppbar(),
                    Expanded(
                      child: SingleChildScrollView(
                        child: Container(
                          margin: const EdgeInsets.symmetric(vertical: 5),
                          padding: const EdgeInsets.symmetric(
                              vertical: 15, horizontal: 0),
                          child: Column(
                            children: [
                              BlocConsumer<LocationCubit, LocationState>(
                                listener: (context, state) {
                                  if (state is FetchLocationDataError) {
                                    if (state.errorMessage ==
                                        ApiResponse.INVALID_AUTH) {
                                      Navigator.pushNamedAndRemoveUntil(context,
                                          LoginPage.routeName, (route) => false,
                                          arguments: true);
                                    }
                                  } else if (state is FetchedLocationData) {
                                    locationList = state.locationList;
                                  }
                                },
                                builder: (context, state) {
                                  if (state is FetchedLocationData) {
                                    if (InMemoryAudiData.timedServiceLocation !=
                                        null) {
                                      selectedLocationId = InMemoryAudiData
                                          .timedServiceLocation!;
                                    }
                                    return Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Container(
                                          padding: const EdgeInsets.symmetric(
                                              vertical: 10, horizontal: 15),
                                          margin: const EdgeInsets.symmetric(
                                              horizontal: 15),
                                          width: double.infinity,
                                          decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(15),
                                            border: Border.all(
                                                color: Theme.of(context)
                                                    .primaryColor),
                                          ),
                                          child: LocationDropdownWidget(
                                            title: AppLocalizations.of(context)!
                                                .selectLocation,
                                            locationList: locationList,
                                            defaultLocationId: InMemoryAudiData
                                                .timedServiceLocation,
                                            onLocationSelected:
                                                (selectedId, name, category) {
                                              InMemoryAudiData
                                                      .timedServiceLocation =
                                                  selectedId;
                                              selectedLocationId = selectedId;
                                            },
                                          ),
                                        ),
                                        const SizedBox(height: 10),
                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceAround,
                                          children: [
                                            _getTimedServiceRequestListButton(),
                                          ],
                                        ),
                                        _getTimedServiceListUI(),
                                      ],
                                    );
                                  }
                                  return const Center(
                                    child: CircularProgressIndicator(),
                                  );
                                },
                              ),
                              const SizedBox(
                                height: 10,
                              )
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              _getScanToRepairButton(),
            ],
          ),
        ),
      ),
      floatingActionButton: ApplicationUtil.getBackButton(
        context,
        onBackPressed: () {
          InMemoryFilterData.clear();
          Navigator.pop(context);
        },
      ),
    );
  }

  Widget _loadingIndicator() {
    return Container(
      padding: const EdgeInsets.all(8.0),
      child: const Center(child: CircularProgressIndicator()),
    );
  }

  Widget _getSingleServiceRequestItem(
      RepairServiceRequest serviceRequest, int index) {
    //print("index");
    //print(index);
    return StreamBuilder<List<RepairData>>(
        stream: repairBloc.repairDao.getPendingRepairFromDbAsStream(),
        builder: (context, snapshot) {
          bool isPresentInDb = false;

          if (snapshot.hasData) {
            List<RepairData> repairData = snapshot.data!
                .where(
                    (element) => element.requestId == serviceRequest.requestId)
                .toList();
            if (repairData.isNotEmpty) {
              isPresentInDb = true;
            }
          }

          return InkWell(
            onTap: () async {
              /*if (isPresentInDb) {
                ApplicationUtil.showSnackBar(
                    context: context,
                    message: 'Repair service is still in progress');
              } else {
                await Navigator.pushNamed(context, RepairDetailPage.routeName,
                    arguments: serviceRequest);
              }*/
            },
            child: Card(
              elevation: 2,
              child: ClipPath(
                clipper: ShapeBorderClipper(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(3),
                  ),
                ),
                child: Container(
                  decoration: BoxDecoration(
                    border: isPresentInDb
                        ? const Border(
                            left: BorderSide(color: Colors.orange, width: 5),
                          )
                        : null,
                  ),
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 10, vertical: 10),
                    child: Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    serviceRequest.equipmentName ?? '',
                                    style: const TextStyle(
                                        fontSize: 24,
                                        color: AppColor.blackTextColor),
                                  ),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      FaIcon(
                                        FontAwesomeIcons.solidBarcode,
                                        color: Theme.of(context).primaryColor,
                                      ),
                                      const SizedBox(
                                        width: 5,
                                      ),
                                      Text(
                                        serviceRequest.tag ?? '',
                                        style: const TextStyle(
                                            height: 1.2,
                                            fontSize: 18,
                                            fontWeight: FontWeight.bold,
                                            color: AppColor.blackTextColor),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(
                                    height: 5,
                                  ),
                                  Text(
                                    serviceRequest.requestType == "SCHEDULED"
                                        ? "TIMED SERVICE"
                                        : serviceRequest.requestType!,
                                    overflow: TextOverflow.ellipsis,
                                    style: const TextStyle(
                                        fontSize: 16,
                                        color: AppColor.blackTextColor),
                                  ),
                                  const SizedBox(
                                    height: 2,
                                  ),
                                ],
                              ),
                            ),
                            Flexible(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      const SizedBox(
                                        width: 10,
                                      ),
                                      Flexible(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            _getStatusType(serviceRequest),
                                          ],
                                        ),
                                      )
                                    ],
                                  ),
                                ],
                              ),
                            )
                          ],
                        ),
                        const SizedBox(
                          height: 5,
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Flexible(
                              child: Text(
                                '${serviceRequest.description}',
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: const TextStyle(
                                    fontSize: 16,
                                    color: AppColor.blackTextColor),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(
                          height: 5,
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: Text(
                                '${ApplicationUtil.getEndLocationFromLocation(locationId: serviceRequest.equipmentLocationId!, locationMap: serviceRequest.location!)['NAME']}',
                                style: const TextStyle(
                                    fontSize: 16,
                                    color: AppColor.blackTextColor),
                              ),
                            ),
                            _getStatusFromDatabase(isPresentInDb),
                            const SizedBox(width: 10),
                            /*if (serviceRequest.requestType == 'SCHEDULED')
                              Tooltip(
                                message: AppLocalizations.of(context)!.scheduleService,
                                child: FaIcon(
                                  FontAwesomeIcons.clock,
                                  color: Theme.of(context).primaryColor,
                                  size: 20,
                                ),
                              ),*/
                            const SizedBox(width: 10),
                            _getImageCount(serviceRequest),
                          ],
                        )
                      ],
                    ),
                  ),
                ),
              ),
            ),
          );
        });
  }

  _getStatusType(RepairServiceRequest serviceRequest) {
    if (serviceRequest.remainingRequestTime != null) {
      if (serviceRequest.remainingRequestTime! > 0) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: 130,
              padding: const EdgeInsets.symmetric(
                vertical: 5,
              ),
              decoration: BoxDecoration(
                color: AppColor.orangeColor,
                borderRadius: BorderRadius.circular(5),
              ),
              child: Text(
                serviceRequest.status!,
                textAlign: TextAlign.center,
                style: const TextStyle(
                    color: Colors.white,
                    fontSize: 13,
                    fontWeight: FontWeight.bold),
              ),
            ),
            const SizedBox(
              height: 5,
            ),
            Container(
              width: 130,
              padding: const EdgeInsets.symmetric(vertical: 2),
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor,
                borderRadius: BorderRadius.circular(5),
              ),
              child: _getDateTime(serviceRequest.remainingRequestTime),
            ),
          ],
        );
      } else {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: 130,
              padding: const EdgeInsets.symmetric(vertical: 5),
              decoration: BoxDecoration(
                color: AppColor.redColor,
                borderRadius: BorderRadius.circular(5),
              ),
              child: Text(
                AppLocalizations.of(context)!.overdue,
                textAlign: TextAlign.center,
                style: const TextStyle(
                    color: Colors.white,
                    fontSize: 13,
                    fontWeight: FontWeight.bold),
              ),
            ),
            const SizedBox(
              height: 5,
            ),
            Container(
              width: 130,
              padding: const EdgeInsets.symmetric(vertical: 2),
              decoration: BoxDecoration(
                color: AppColor.redColor,
                borderRadius: BorderRadius.circular(5),
              ),
              child: Text(
                ApplicationUtil.getHourAndMinuteOrDayFromMinute(
                        serviceRequest.remainingRequestTime!) +
                    ' ' +
                    AppLocalizations.of(context)!.overdue,
                textAlign: TextAlign.center,
                style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold),
              ),
            )
          ],
        );
      }
    }
    return Container();
  }

  _getStatusFromDatabase(bool isPresentInDb) {
    if (isPresentInDb) {
      return Text(AppLocalizations.of(context)!.processing);
    }
    return Container();
  }

  _repairAppbar() => Container(
        margin: const EdgeInsets.only(left: 10),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            BlocBuilder<RepairListApiBloc, RepairListApiState>(
              builder: (context, state) {
                if (state is FetchedRepairServiceRequestList) {
                  return Text(
                    '${AppLocalizations.of(context)!.timedServices} ',
                    style: const TextStyle(
                        color: AppColor.blackTextColor,
                        fontSize: AppConstant.toolbarTitleFontSize,
                        fontWeight: FontWeight.bold),
                  );
                }
                return Text(
                  '${AppLocalizations.of(context)!.timedServices} ',
                  style: const TextStyle(
                      color: AppColor.blackTextColor,
                      fontSize: AppConstant.toolbarTitleFontSize,
                      fontWeight: FontWeight.bold),
                );
              },
            ),
            Expanded(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Flexible(
                    child: IconButton(
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(minWidth: 50),
                      iconSize: AppConstant.toolbarIconSize,
                      onPressed: () {
                        repairListBloc.add(FetchRepairServiceRequestList(
                            refresh: true, query: query));
                      },
                      icon: FaIcon(
                        FontAwesomeIcons.solidRedo,
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                  ),
                  /*Flexible(
                    child: IconButton(
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                      onPressed: () async {
                        query += await Navigator.pushNamed(context, ServiceRequestFilterPage.routeName) as String;
                        repairListBloc.add(FetchRepairServiceRequestList(refresh: true, query: query));
                      },
                      icon: FaIcon(
                        FontAwesomeIcons.solidFilter,
                        color: Theme.of(context).primaryColor,
                        size: 20,
                      ),
                    ),
                  ),*/
                  Flexible(
                    child: _getMenuOption(),
                  )
                ],
              ),
            )
          ],
        ),
      );

  _getMenuOption() => BlocListener<ApiBloc, ApiState>(
        listener: (context, state) {
          if (state is BarcodeApiCalling) {
            SchedulerBinding.instance.addPostFrameCallback((_) {
              ApplicationUtil.showLoaderDialog(
                  context, AppLocalizations.of(context)!.fetching + '..');
            });
          } else if (state is BarcodeApiCalled) {
            apiBloc.add(ResetBarCodeApi());
            Navigator.pop(context);
            getIt<BarcodeResponse>().barcodeResponseInstance =
                state.barcodeResponse;
            if (getIt<ServiceType>().type ==
                ServiceRequestType.MOVE_EQUIPMENT) {
              Navigator.pushNamed(context, AddServiceRequestPage.routeName,
                  arguments:
                      ServiceType(type: ServiceRequestType.MOVE_EQUIPMENT));
            } else if (getIt<ServiceType>().type ==
                ServiceRequestType.ASSIGN_EQUIPMENT) {
              ApplicationUtil.showSnackBar(
                  context: context,
                  message:
                      AppLocalizations.of(context)!.barcodeAssignedToEquipment);
            } else if (getIt<ServiceType>().type ==
                ServiceRequestType.SCHEDULED) {
              repairDetailApiCubit.getSingleRepairDetailByEquipmentId(
                  equipmentId: state.barcodeResponse.equipment!.equipmentId,
                  bothRequestType: true,
                  isTimedService: true,
                  isFromMap: state.isFromMap);
            }
          } else if (state is BarcodeApiError) {
            if (getIt<ServiceType>().type ==
                    ServiceRequestType.ASSIGN_EQUIPMENT &&
                state.errorMessage == ApiResponse.BAR_CODE_NOT_FOUND) {
              Navigator.pop(context);

              Navigator.pushNamed(context, AssignEquipmentPage.routeName,
                  arguments: ServiceType(
                      type: ServiceRequestType.ASSIGN_EQUIPMENT,
                      barcodeNumber: state.barcodeNumber));
            } else {
              ScaffoldMessenger.of(context)
                  .showSnackBar(SnackBar(content: Text(state.errorMessage)));
              Navigator.pop(context);
            }
          }
        },
        child: const SizedBox(
          width: 10,
        ),
      );

  _getScanToRepairButton() => Container(
        alignment: Alignment.bottomCenter,
        child: BlocConsumer<RepairDetailApiCubit, RepairDetailState>(
          listener: (context, state) async {
            if (state is FetchSingleRepairsDetailEquipmentIdError) {
              if (state.errorMessage == ApiResponse.INVALID_AUTH) {
                Navigator.pushNamedAndRemoveUntil(
                    context, LoginPage.routeName, (route) => false,
                    arguments: true);
              }
            } else if (state is FetchedSingleRepairsDetailByEquipmentId) {
              if (state.serviceRequestDetailList.isEmpty) {
                /// Check if user is afs user then allow user to create service and repar
                //bool? afsUser = getIt<SharedPreferences>().getBool('isAfs');
                //if (afsUser != null && afsUser == true) {
                /// GOTO SERVICE PAGE IF THERE IS NO EXISTING SERVICE
                //showAlertDialog(context);
                // } else {
                ApplicationUtil.showWarningAlertDialog(context,
                    title: AppLocalizations.of(context)!.barcodeNotfound,
                    negativeLabel: AppLocalizations.of(context)!.okay,
                    desc: AppLocalizations.of(context)!
                        .noTimedServiceRequestWithBarcode);
                //}
              } else {
                ServiceRequestDetail serviceRequestDetail =
                    state.serviceRequestDetailList[0];
                RepairServiceRequest serviceRequest = RepairServiceRequest(
                    requestId: serviceRequestDetail.requestId!,
                    customerId: serviceRequestDetail.customerId,
                    equipmentName: serviceRequestDetail.equipmentName,
                    equipmentId: serviceRequestDetail.equipmentId,
                    requestType: serviceRequestDetail.requestType,
                    currentLocationId: serviceRequestDetail.currentLocationId,
                    newLocationId: serviceRequestDetail.newLocationId,
                    taskType: serviceRequestDetail.taskType,
                    description: serviceRequestDetail.description,
                    createdBy: serviceRequestDetail.createdBy,
                    createdAt: serviceRequestDetail.createdDate,
                    status: serviceRequestDetail.status,
                    remainingRequestTime:
                        serviceRequestDetail.remainingRequestTime,
                    equipmentCategoryName:
                        serviceRequestDetail.equipmentCategoryName,
                    location: serviceRequestDetail.location,
                    equipmentLocationId:
                        serviceRequestDetail.equipmentLocationId,
                    tag: serviceRequestDetail.tag);
                //todo : remove result check and use .whencomplete() method for Navigator.push()
                var result = await Navigator.pushNamed(
                    context, RepairDetailPage.routeName,
                    arguments: RepairDetailPageParams(
                        repairServiceRequest: serviceRequest,
                        isTimedService: true));
                if (result is String) {
                  print("result string is $result");
                  ApplicationUtil.showSnackBar(
                      context: context, message: result);
                  if (result ==
                      AppLocalizations.of(context)!
                          .cancelledTimedServiceSuccess) {
                    Future.delayed(const Duration(milliseconds: 500));
                  }
                  repairListBloc.add(
                    FetchRepairServiceRequestList(refresh: true, query: query),
                  );
                  setState(() {});
                }
              }
            }
          },
          builder: (context, state) {
            if (state is FetchingSingleRepairsDetailByEquipmentId) {
              return const SizedBox(
                  width: 30, height: 30, child: CircularProgressIndicator());
            }
            return ScanBarCodeButton.getScanBarcodeButton(
              context,
              onTap: () async {
                getIt<ServiceType>().type = ServiceRequestType.SCHEDULED;
                var result = await Navigator.pushNamed(
                    context, AIBarCodeScannerPage.routeName);
                if (result is Map) {
                  Map<String, dynamic> resultData =
                      result as Map<String, dynamic>;
                  String returnedCode = resultData['code'];
                  bool isScanned = resultData['isScanned'];
                  if (isScanned) {
                    if (locationData != null) {
                      apiBloc.add(CallBarCodeApi(
                          barcodeNumber: returnedCode,
                          isTimedService: true,
                          lattitude: locationData?.latitude!,
                          longitude: locationData?.longitude!));
                    } else {
                      apiBloc.add(CallBarCodeApi(
                          barcodeNumber: returnedCode, isTimedService: true));
                    }
                  } else {
                    apiBloc.add(CallBarCodeApi(
                        barcodeNumber: returnedCode, isTimedService: true));
                  }
                }
              },
              label: AppLocalizations.of(context)!.scanToRepair,
            );
            //return scanToRepairButton();
          },
        ),
      );

  _getImageCount(RepairServiceRequest serviceRequest) {
    if (serviceRequest.repairImageList?.length != null &&
        serviceRequest.repairImageList!.length != 0) {
      return InkWell(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            FaIcon(
              FontAwesomeIcons.image,
              size: 20,
              color: Theme.of(context).primaryColor,
            ),
            const SizedBox(
              width: 2,
            ),
            _getImageCountText(serviceRequest),
          ],
        ),
        onTap: () async {
          var response = await ApiService().getServiceRequestListByEquipmentId(
              serviceRequest.equipmentId!, true, false);
          ServiceRequestDetail serviceRequestDetail = response;
          //List<Document> imageListFromServer = serviceRequestDetail.document!;
          //serviceRequest.imageListMap = imageListFromServer.toJson();

          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ImagePageView(
                serviceRequest: serviceRequestDetail,
              ),
            ),
            /*MaterialPageRoute(
              builder: (context) => GalleryExample(
                serviceRequest: serviceRequest,
              ),
            ),*/
          );
        },
      );
    }
    {
      return const SizedBox();
    }
  }

  _getImageCountText(RepairServiceRequest serviceRequest) {
    if (serviceRequest.repairImageList!.length > 1) {
      return Text(
        "+" + (serviceRequest.repairImageList!.length - 1).toString(),
        textAlign: TextAlign.center,
        style: TextStyle(
          color: Theme.of(context).primaryColor,
        ),
      );
    } else {
      return const Text('');
    }
  }

  _getTimedServiceRequestListButton() {
    return TextButton(
      onPressed: () {
        isGetServiceCalled = true;
        query = "&location=$selectedLocationId&requestType=SCHEDULED";
        if (selectedLocationId.isEmpty) {
          ApplicationUtil.showSnackBar(
              context: context,
              message: AppLocalizations.of(context)!.pleaseSelectLocation);
        } else {
          repairListBloc
              .add(FetchRepairServiceRequestList(refresh: true, query: query));
        }
      },
      child: Text(
        AppLocalizations.of(context)!.getTimedServiceRequest,
        style: const TextStyle(color: Colors.black, fontSize: 14),
      ),
      style: ButtonStyle(
        padding: MaterialStateProperty.all(
            const EdgeInsets.symmetric(vertical: 12, horizontal: 10)),
        shape: MaterialStateProperty.all(RoundedRectangleBorder(
          side: const BorderSide(
              color: AppColor.greenSentColor,
              width: 1,
              style: BorderStyle.solid),
          borderRadius: BorderRadius.circular(15),
        )),
      ),
    );
  }

  _getTimedServiceListUI() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 5),
      alignment: Alignment.center,
      child: BlocConsumer<RepairListApiBloc, RepairListApiState>(
        listener: (context, state) {
          if (state is FetchedRepairServiceError) {
            if (state.errorMessage == ApiResponse.INVALID_AUTH) {
              Navigator.pushNamedAndRemoveUntil(
                  context, LoginPage.routeName, (route) => false,
                  arguments: true);
            }
          }
        },
        builder: (context, state) {
          List<RepairServiceRequest>? srList = [];
          int? srCount = 0;
          bool isLoading = false;

          if (state is FetchingRepairServiceRequestList && state.isFirstFetch) {
            return _loadingIndicator();
          } else if (state is FetchingRepairServiceRequestList) {
            srList = state.oldList;
            isLoading = true;
          } else if (state is FetchedRepairServiceRequestList) {
            srCount = state.repairServiceRequestList.serviceRequestCount;
            serviceRequestCount =
                state.repairServiceRequestList.serviceRequestCount;
            srList = state.repairServiceRequestList.repairServiceRequestList;
          } else if (state is FetchedRepairServiceError) {
            return Center(
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Text(
                  state.errorMessage,
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                      color: AppColor.greyBorderColor, fontSize: 18),
                ),
              ),
            );
          }
          return Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              ListView.builder(
                shrinkWrap: true,
                padding: const EdgeInsets.only(bottom: 60),
                controller: scrollController,
                itemCount: (srList!.length + 1) /*+ (isLoading ? 1 : 0)*/,
                itemBuilder: (context, index) {
                  //print("number of timed services: $srCount");
                  if (index < srList!.length) {
                    return _getSingleServiceRequestItem(srList[index], index);
                  } else if (srList.length < srCount!) {
                    return isLoading
                        ? _loadingIndicator()
                        : Container(
                            padding: EdgeInsets.symmetric(horizontal: 5),
                            child: ElevatedButton(
                                onPressed: () {
                                  print("Load more called");
                                  repairListBloc.add(
                                      FetchRepairServiceRequestList(
                                          refresh: false, query: query));
                                },
                                child: const Text("Load More")),
                          );
                  } else if (srList.length == srCount) {
                    if (srList.isEmpty && isGetServiceCalled) {
                      return Container(
                        width: double.maxFinite,
                        alignment: Alignment.center,
                        child: const Text(
                          "No Timed Services found",
                          style: TextStyle(fontSize: 20),
                        ),
                      );
                    } else {
                      return const SizedBox(height: 70);
                    }
                  } else if (srList.length >= srCount) {
                    print(
                        "service request length is: ${srList.length}\nservice request count: $srCount\nindex is: $index \nisLoading: $isLoading");
                    if (isLoading && srCount == 0) {
                      return _loadingIndicator();
                    } else {
                      return Container();
                    }
                  }
                  {
                    Timer(const Duration(milliseconds: 30), () {
                      scrollController
                          .jumpTo(scrollController.position.maxScrollExtent);
                    });
                    return _loadingIndicator();
                  }
                },
              ),
            ],
          );
        },
      ),
    );
  }

  _getDateTime(int? timeInMinutes) {
    return Text(
      ApplicationUtil.getHourAndMinuteOrDayFromMinute(timeInMinutes!) +
          ' ' +
          AppLocalizations.of(context)!.remaining,
      textAlign: TextAlign.center,
      style: const TextStyle(
          color: Colors.white, fontSize: 12, fontWeight: FontWeight.bold),
    );
  }
}
