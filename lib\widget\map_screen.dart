import 'dart:async';
import 'dart:math';
import 'package:alink/pages/airport/repair/repair_detail_page.dart';
import 'package:alink/provider/locationProvider.dart';
import 'package:alink/widget/scan_barcode_button_widget.dart';
import 'package:alink/widget/validate_and_scan_widget.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../bloc/api/api_bloc.dart';
import '../bloc/audit/audit_equipment_cubit.dart';
import '../bloc/repair_db_bloc/repair_bloc.dart';
import '../bloc/repair_list_api/repair_list_api_bloc.dart';
import '../bloc/service/service_request_bloc.dart';
import '../bloc/user_bloc.dart';
import '../cubit/pages/repair_detail/repair_detail_api_cubit.dart';
import '../data/model/audit_id_model.dart';
import '../data/model/audit_response.dart';
import '../data/model/barcode_response.dart';
import '../data/model/filter_data.dart';
import '../data/model/repair_service_request.dart';
import '../data/model/service_request_detail.dart';
import '../data/model/singleton_model.dart';
import '../data/repository/api_service.dart';
import '../database/database.dart';
import '../logger/logger.dart';
import '../pages/airport/audit/audit_barcode/audit_scanner_page.dart';
import '../pages/airport/service_request/add_service_request_page.dart';
import '../pages/airport/service_request/afs_service_repair_page.dart';
import '../pages/airport/service_request/bar_code_result_detail_page.dart';
import '../pages/airport/service_request/service_result_page.dart';
import '../pages/auth/login_page.dart';
import '../provider/locationDistanceProvider.dart';
import '../scanner/barcode/ai_scanner_page.dart';
import '../service_locator.dart';
import '../util/app_color.dart';
import '../util/app_constant.dart';
import '../util/application_util.dart';
import '../util/enums/app_enum.dart';
import 'app_expansion_tile.dart';
import 'image_view.dart';

class MapScreenArguments {
  final AuditRefId? auditRefId;
  final bool isConditionAudit;
  final dynamic result;
  final bool isFromAudit;

  MapScreenArguments(this.result, this.isFromAudit,
      {this.isConditionAudit = false, this.auditRefId});
}

class MapScreen extends StatefulWidget {
  static const String routeName = "maps";
  final MapScreenArguments mapArguments;

  const MapScreen({super.key, required this.mapArguments});

  @override
  _MapScreenState createState() => _MapScreenState();
}

class _MapScreenState extends State<MapScreen> {
  final Completer<GoogleMapController> _controller = Completer();
  bool _isBottomSheetOpen = false;
  Map<String, dynamic>? _selectedMarker;
  LatLng? _currentPosition;
  LatLng? initialPosition;
  List locations = [];
  bool isInValidBarcode = false;
  String? _selectedMarkerId;
  bool isConditionalAudit = false;
  bool _isMapReady = false;
  StreamSubscription<Position>? _positionStreamSubscription;
  bool _hasMovedToUserLocation = false;
  int? customerId = getIt<SharedPreferences>().getInt('customerId');
  LocationProvider? locationProvider;
  String query = "";
  Set<Marker> _markers = {};
  Map<LatLng, List<Map<String, dynamic>>> _equipmentMap = {};
  Set<Marker> expandedMarkers = {};
  LocationPermission? permission;
  bool serviceEnabled = false;
  ApiBloc get apiBloc => BlocProvider.of<ApiBloc>(context);
  ServiceRequestBloc get serviceRequestBloc =>
      BlocProvider.of<ServiceRequestBloc>(context);
  RepairDetailApiCubit get repairDetailApiCubit =>
      BlocProvider.of<RepairDetailApiCubit>(context);
  RepairListApiBloc get repairListBloc =>
      BlocProvider.of<RepairListApiBloc>(context);
  RepairBloc get repairBloc => BlocProvider.of<RepairBloc>(context);

  @override
  void initState() {
    super.initState();
    locationProvider = Provider.of<LocationProvider>(context, listen: false);
    initialPosition = LatLng(locationProvider!.currentPosition!.latitude!,
        locationProvider!.currentPosition!.longitude);
    _getUserLocation();
    locations = widget.mapArguments.result;
    isConditionalAudit = widget.mapArguments.isConditionAudit;
    if (widget.mapArguments.isFromAudit) {
      _compareAndAddEquipment();
    }
    if (!widget.mapArguments.isFromAudit) {}
    _loadMarkers();
  }

  void _compareAndAddEquipment({String? result}) {
    for (var equipment in ServiceRequestBloc.equipmentList) {
      //Map<String,dynamic>? data = locations.firstWhere((location) => location["TAG"] == equipment.tag);
      int value =
          locations.indexWhere((location) => location["TAG"] == equipment.tag);
      if (value == -1) {
        locations.add({
          "TAG": equipment.tag,
          "LATITUDE": equipment.latitude,
          "LONGITUDE": equipment.longitude,
          "EQUIPMENT_ID": equipment.equipmentId,
          "NAME": equipment.name,
          "LOCATION_ID": equipment.locationId,
          "CATEGORY_NAME": equipment.categoryName,
          "BOM_ID": equipment.bomId,
          "STATUS": equipment.status,
        });
      } else {
        if ((locations[value]["LATITUDE"] != equipment.latitude ||
                locations[value]["LONGITUDE"] != equipment.longitude) &&
            locations[value]["EQUIPMENT_ID"] == equipment.equipmentId &&
            equipment.latitude != null &&
            equipment.longitude != null &&
            result != null) {
          locations[value]["EQUIPMENT_ID"] = equipment.equipmentId;
          locations[value]["LATITUDE"] = equipment.latitude;
          locations[value]["LONGITUDE"] = equipment.longitude;
        }
      }
    }
    _loadMarkers();
  }

  void _compareAndAddEquipmentForRepairScreen({String? result}) {
    for (var equipment in ServiceRequestBloc.equipmentList) {
      Map<String, dynamic>? data =
          locations.firstWhere((location) => location["TAG"] == equipment.tag);
      if (data == null) {
        locations.add({
          "TAG": equipment.tag,
          "LATITUDE": equipment.latitude,
          "LONGITUDE": equipment.longitude,
          "EQUIPMENT_ID": equipment.equipmentId,
          "NAME": equipment.name,
          "LOCATION_ID": equipment.locationId,
          "CATEGORY_NAME": equipment.categoryName,
          "BOM_ID": equipment.bomId,
        });
      } else {
        if ((data["LATITUDE"] != equipment.latitude ||
                data["LONGITUDE"] != equipment.longitude) &&
            data["EQUIPMENT_ID"] == equipment.equipmentId &&
            result != null) {
          int value = locations
              .indexWhere((location) => location["TAG"] == data["TAG"]);
          if (value != -1) {
            locations[value]["EQUIPMENT_ID"] = equipment.equipmentId;
            locations[value]["LATITUDE"] = equipment.latitude;
            locations[value]["LONGITUDE"] = equipment.longitude;
          }
        }
      }
    }
    _loadMarkers();
  }

  void _onMapTap(LatLng position) {
    if (_isBottomSheetOpen) {
      setState(() {
        _isBottomSheetOpen = false;
        _selectedMarkerId = null;
        _setMarkers(); // Reset markers to their original color
      });
    }
  }

  void _onMarkerTap(Map<String, dynamic> map) {
    if (widget.mapArguments.isFromAudit) {
      if (_selectedMarkerId !=
          LatLng(map["LATITUDE"], map["LONGITUDE"]).toString()) {
        setState(() {
          _isBottomSheetOpen = true;
          _selectedMarker = map;
          _selectedMarkerId =
              LatLng(map["LATITUDE"], map["LONGITUDE"]).toString();
          _setMarkers();
        });
        if (map["EQUIPMENT_ID"] != null) {
          repairDetailApiCubit.getRepairDetailListForEquipment(
              map["EQUIPMENT_ID"], true, false);
          setState(() {
            _selectedMarkerId =
                LatLng(map["LATITUDE"], map["LONGITUDE"]).toString();
          });
          _updateBottomSheet();
        }
      }
    } else {
      if (_selectedMarkerId !=
          LatLng(map["LATITUDE"], map["LONGITUDE"]).toString()) {
        setState(() {
          _isBottomSheetOpen = true;
          _selectedMarker = map;
          _selectedMarkerId =
              LatLng(map["LATITUDE"], map["LONGITUDE"]).toString();
          _setMarkers();
          _updateBottomSheet();
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final bool isIOS = Theme.of(context).platform == TargetPlatform.iOS;
    return WillPopScope(
      onWillPop: () async {
        if (_isBottomSheetOpen) {
          setState(() {
            _isBottomSheetOpen = false;
          });
          Navigator.pop(context);
          return false; // Prevents exiting the screen
        }
        return true; // Allows normal back button behavior
      },
      child: Scaffold(
        body: SafeArea(
          child: Center(
            child: Column(
              children: [
                Container(
                  constraints: const BoxConstraints(maxWidth: 500),
                  height: isIOS
                      ? MediaQuery.of(context).size.height * 0.80
                      : MediaQuery.of(context).size.height * 0.85,
                  child: Column(
                    children: [
                      widget.mapArguments.isFromAudit
                          ? _equipmentListAppBar()
                          : _repairAppbar(),
                      widget.mapArguments.isFromAudit
                          ? _getConditionAuditCheckBox()
                          : Container(),
                      Expanded(
                        child: Stack(
                          children: [
                            GoogleMap(
                              myLocationEnabled: true,
                              myLocationButtonEnabled: true,
                              compassEnabled: true,
                              onTap: _onMapTap,
                              onMapCreated: _onMapCreated,
                              initialCameraPosition: CameraPosition(
                                target: initialPosition!,
                                zoom: 12.0,
                              ),
                              markers: _markers,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                const Spacer(),
                Container(
                  child: widget.mapArguments.isFromAudit
                      ? Stack(
                          children: [
                            ValidateAndScanButtons(
                              isFromMap: true,
                              equipmentList: ServiceRequestBloc.equipmentList,
                              auditId: AuditEquipmentCubit.auditId ?? 0,
                              apiBloc: apiBloc,
                              auditRefId: widget.mapArguments.auditRefId,
                              isConditionAudit: isConditionalAudit,
                              serviceRequestBloc: serviceRequestBloc,
                              onTapScan: () async {
                                serviceRequestBloc
                                    .add(DismissServiceRequestNotification());
                                serviceRequestBloc.add(
                                    GetEquipmentDataFromAuditStatusForMap(
                                        AuditEquipmentCubit.auditId!));
                                var result = await Navigator.pushNamed(
                                    context, AuditBarCodeScannerPage.routeName);
                                _compareAndAddEquipment(
                                    result: result.toString());
                              },
                            ),
                          ],
                        )
                      : Stack(
                          children: [
                            _getScanToRepairButton(),
                            Container(
                              alignment: Alignment.bottomRight,
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 16, vertical: 12),
                              child: ApplicationUtil.getBackButton(
                                context,
                                onBackPressed: () {
                                  InMemoryFilterData.clear();
                                  Navigator.pop(context);
                                },
                                heroTag: "map_back_button"
                              ),
                            )
                          ],
                        ),
                ),
              ],
            ),
          ),
        ),
        bottomSheet: _isBottomSheetOpen
            ? Container(
                decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(20),
                    topRight: Radius.circular(20),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey,
                      blurRadius: 10,
                      spreadRadius: 2,
                    ),
                  ],
                ),
                child: StatefulBuilder(
                  builder: (context, setState) {
                    _getUserLocation();
                    List list = [];
                    if (widget.mapArguments.isFromAudit) {
                      if(_selectedMarker!["EQUIPMENT_ID"] == null) {
                        list = ServiceRequestBloc.equipmentList
                          .where((element) =>
                              element.tag ==
                              _selectedMarker!["TAG"])
                          .toList();
                      }else{
                      list = ServiceRequestBloc.equipmentList
                          .where((element) =>
                              element.equipmentId ==
                              _selectedMarker!["EQUIPMENT_ID"])
                          .toList();
                      }
                    } else {
                      list = locations
                          .where((element) =>
                              element["REQUEST_ID"] ==
                              _selectedMarker!["REQUEST_ID"])
                          .toList();
                    }
                    return Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        widget.mapArguments.isFromAudit
                            ? getSingleListTileWidget(list[0])
                            : _getSingleServiceRequestItem(
                                RepairServiceRequest.fromJson(list[0])),
                        widget.mapArguments.isFromAudit
                            ? ValidateAndScanButtons(
                                isFromMap: true,
                                equipmentList: ServiceRequestBloc.equipmentList,
                                auditId: AuditEquipmentCubit.auditId ?? 0,
                                apiBloc: apiBloc,
                                auditRefId: widget.mapArguments.auditRefId,
                                isConditionAudit: isConditionalAudit,
                                serviceRequestBloc: serviceRequestBloc,
                                onTapScan: () async {
                                  if (_isBottomSheetOpen) {
                                    setState(() {
                                      _isBottomSheetOpen = false;
                                      _selectedMarkerId = null;
                                    });
                                  }
                                  serviceRequestBloc
                                      .add(DismissServiceRequestNotification());
                                  serviceRequestBloc.add(
                                      GetEquipmentDataFromAuditStatusForMap(
                                          AuditEquipmentCubit.auditId!));
                                  var result = await Navigator.pushNamed(
                                      context,
                                      AuditBarCodeScannerPage.routeName);
                                  if (widget.mapArguments.isFromAudit) {
                                    _compareAndAddEquipment(
                                        result: result.toString());
                                  }
                                },
                              )
                            : Stack(
                                children: [
                                  _getScanToRepairButton(),
                                  Container(
                                    alignment: Alignment.bottomRight,
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 16, vertical: 16),
                                    child: ApplicationUtil.getBackButton(
                                      context,
                                      onBackPressed: () {
                                        InMemoryFilterData.clear();
                                        Navigator.pop(context);
                                      },
                                    ),
                                  )
                                ],
                              ),
                      ],
                    );
                  },
                ),
              )
            : null,
      ),
    );
  }

  @override
  void dispose() {
    if (_positionStreamSubscription != null) {
      _positionStreamSubscription!.cancel();
    }
    super.dispose();
  }

  void _onMapCreated(GoogleMapController controller) {
    _controller.complete(controller);
    setState(() {
      _isMapReady = true;
    });
    if (_hasMovedToUserLocation) {
      _getUserLocation();
    }
    _focusOnMarkers();
  }

  Future<void> _getUserLocation() async {
    try {
      serviceEnabled = await Geolocator.isLocationServiceEnabled();
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied ||
          permission == LocationPermission.deniedForever) {
        debugPrint("Location permission denied");
        return;
      }
      Position location = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.best,
      );
      setState(() {
        _currentPosition = LatLng(location.latitude, location.longitude);
      });

      if (_isMapReady && !_hasMovedToUserLocation) {
        GoogleMapController controller = await _controller.future;
        _hasMovedToUserLocation = true; // Mark as moved
      }
      _positionStreamSubscription = Geolocator.getPositionStream(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.bestForNavigation,
          distanceFilter: 5,
        ),
      ).listen((Position newPosition) {
        if (_selectedMarker != null) {
          double distance = Geolocator.distanceBetween(
            newPosition.latitude,
            newPosition.longitude,
            _selectedMarker!["LATITUDE"],
            _selectedMarker!["LONGITUDE"],
          );
          if (distance > 5) {
            if (mounted) {
              Provider.of<DistanceCalculator>(context, listen: false)
                  .setDistance(distance);
              setState(() {
                _currentPosition =
                    LatLng(newPosition.latitude, newPosition.longitude);
              });
            }
            /*if (_isBottomSheetOpen) {
              _updateBottomSheet();
            }*/
          }
        }
      });

      if (_isMapReady) {
        GoogleMapController controller = await _controller.future;
      }
    } catch (e) {
      debugPrint("Error getting location: $e");
    }
  }

  void _updateBottomSheet() {
    Logger.i("updating the distace");
    if (mounted) {
      if(_currentPosition == null) {
        return; // Avoid null pointer exception
      }
      if (_selectedMarker != null) {
        var newDistance = Geolocator.distanceBetween(
          _currentPosition!.latitude,
          _currentPosition!.longitude,
          _selectedMarker!["LATITUDE"],
          _selectedMarker!["LONGITUDE"],
        );
        if (newDistance != null) {
          Provider.of<DistanceCalculator>(context, listen: false)
              .setDistance(newDistance);
        }
        List list = [];
        if (widget.mapArguments.isFromAudit) {
          list = ServiceRequestBloc.equipmentList
              .where(
                (element) =>
                    element.equipmentId == _selectedMarker!["EQUIPMENT_ID"],
              )
              .toList();
          getSingleListTileWidget(list[0]);
        } else {
          list = locations
              .where((element) =>
                  element["REQUEST_ID"] == _selectedMarker!["REQUEST_ID"])
              .toList();
          _getSingleServiceRequestItem(RepairServiceRequest.fromJson(list[0]));
        }
      }
    }
  }

  getSingleListTileWidget(AuditEquipment equipment) {
    if (AuditEquipmentCubit.auditServiceType != AppConstant.SERVICE_TYPE_TASK) {
      return SizedBox(
        child: Column(
          children: [
            Container(
              margin: const EdgeInsets.only(
                  top: 10, left: 10, right: 10, bottom: 0),
              decoration: BoxDecoration(
                  border: Border.all(color: AppColor.greyBorderColor),
                  borderRadius: BorderRadius.circular(10)),
              child: AppExpansionTile(
                  initiallyExpanded: true,
                  isExpandable: false,
                  trailing: Container(
                    width: kIsWeb ? 150 : 100,
                    child: getDistanceCountWidget(),
                  ),
                  title: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      _getLeadingData(equipment),
                      const SizedBox(
                        width: 3,
                      ),
                      Expanded(
                        child: Text(
                          (equipment.status == "Movement" &&
                                  equipment.name == null)
                              ? AppLocalizations.of(context)!.newEquipment
                              : '(${equipment.name!})',
                          overflow: TextOverflow.ellipsis,
                          style: const TextStyle(
                            fontSize: 20,
                            color: Color(0xff101010),
                          ),
                        ),
                      ),
                      (equipment.isScanned && equipment.equipmentId != null) ||
                              equipment.status == "Scanned" ||
                              equipment.status == "Movement"
                          ? InkWell(
                              onTap: () {
                                serviceRequestBloc.add(
                                    UpdateEquipmentDataFromAuditStatus(
                                        AuditEquipmentCubit.auditId!,
                                        equipment.tag!,
                                        false));
                                int movementEquipmentIndex =
                                    locations.indexWhere((element) =>
                                        element["TAG"] == equipment.tag &&
                                        element["STATUS"] == "Movement");
                                if (movementEquipmentIndex != -1) {
                                  locations.removeAt(movementEquipmentIndex);
                                  _loadMarkers();
                                  setState(() {
                                    _isBottomSheetOpen = false;
                                    _selectedMarkerId = null;
                                    _selectedMarker = null;
                                  });
                                }
                                setState(() {});
                              },
                              child: Container(
                                  margin: const EdgeInsets.only(left: 10),
                                  child: const FaIcon(
                                    FontAwesomeIcons.timesCircle,
                                    size: 25,
                                    color: AppColor.redColor,
                                  )))
                          : Container(),
                    ],
                  ),
                  backgroundColor: Theme.of(context)
                      .colorScheme
                      .secondary
                      .withOpacity(0.025),
                  children: <Widget>[
                    const Divider(
                      thickness: 1,
                    ),
                    _checkAndReturnEquipmentDetailExpansionDetailUI(equipment)
                  ]),
            ),
          ],
        ),
      );
    } else {
      return Container(
        margin: const EdgeInsets.only(top: 10, left: 5, right: 5, bottom: 0),
        decoration: BoxDecoration(
            border: Border.all(color: AppColor.greyBorderColor),
            borderRadius: BorderRadius.circular(10)),
        child: ListTile(
          trailing: Container(
            width: kIsWeb ? 150 : 100,
            child: getDistanceCountWidget(),
          ),
          title: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _getLeadingData(equipment),
              const SizedBox(
                width: 3,
              ),
              Expanded(
                child: Text(
                  (equipment.status == "Movement" && equipment.name == null)
                      ? AppLocalizations.of(context)!.newEquipment
                      : '(${equipment.name!})',
                  style: const TextStyle(
                    fontSize: 20,
                    color: Color(0xff101010),
                  ),
                ),
              ),
              (equipment.isScanned && equipment.equipmentId != null) ||
                      equipment.status == "Scanned" ||
                      equipment.status == "Movement"
                  ? InkWell(
                      onTap: () {
                        serviceRequestBloc.add(
                            UpdateEquipmentDataFromAuditStatus(
                                AuditEquipmentCubit.auditId!,
                                equipment.tag!,
                                false));
                        int movementEquipmentIndex = locations.indexWhere(
                            (element) =>
                                element["TAG"] == equipment.tag &&
                                element["STATUS"] == "Movement");
                        if (movementEquipmentIndex != -1) {
                          locations.removeAt(movementEquipmentIndex);
                          _loadMarkers();
                          setState(() {
                            _isBottomSheetOpen = false;
                            _selectedMarkerId = null;
                            _selectedMarker = null;
                          });
                        }
                      },
                      child: Container(
                          margin: const EdgeInsets.only(left: 10),
                          child: const FaIcon(
                            FontAwesomeIcons.timesCircle,
                            size: 25,
                            color: AppColor.redColor,
                          )))
                  : Container(),
            ],
          ),
          hoverColor:
              Theme.of(context).colorScheme.secondary.withOpacity(0.025),
        ),
      );
    }
  }

  _getLeadingData(AuditEquipment equipment) {
    return Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          _getLeadingIcon(equipment),
          equipment.name != null
              ? const SizedBox(
                  width: 5,
                )
              : const SizedBox(),
          _getTag(equipment),
        ]);
  }

  _checkAndReturnEquipmentDetailExpansionDetailUI(
    AuditEquipment equipment,
  ) {
    return Container(
      height: 130,
      padding: const EdgeInsets.only(top: 5, bottom: 10),
      child: BlocConsumer<RepairDetailApiCubit, RepairDetailState>(
        listener: (context, state) {
          if (state is FetchRepairsListError) {
            if (state.errorMessage == ApiResponse.INVALID_AUTH) {
              Navigator.pushNamedAndRemoveUntil(
                  context, LoginPage.routeName, (route) => false,
                  arguments: true);
            }
          }
        },
        builder: (context, state) {
          if (state is FetchedRepairsListForEquipment) {
            if (state.serviceRequestDetailList.isEmpty) {
              return Row(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  _createServiceRequestWidget(equipment),
                ],
              );
            }
            return ListView.builder(
              physics: const BouncingScrollPhysics(
                  parent: AlwaysScrollableScrollPhysics()),
              scrollDirection: Axis.horizontal,
              itemCount: state.serviceRequestDetailList.length,
              itemBuilder: (context, index) {
                ServiceRequestDetail serviceRequestDetail =
                    state.serviceRequestDetailList[index];
                if (index == 0) {
                  return Row(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      _createServiceRequestWidget(equipment),
                      _getExistingServiceRequestWidget(serviceRequestDetail)
                    ],
                  );
                } else {
                  return _getExistingServiceRequestWidget(serviceRequestDetail);
                }
              },
            );
          } else if(state is FetchingRepairsListForEquipment) { 
            return Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                _createServiceRequestWidget(equipment),
                const SizedBox(
                  width: 50,
                ),
                isInValidBarcode != true
                    ? const CircularProgressIndicator() 
                    : Container()
              ],
            );
            }else {
            return Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                _createServiceRequestWidget(equipment),
              ],
            );
          }
        },
      ),
    );
  }

  _getLeadingIcon(AuditEquipment equipment) {
    if (equipment.name == null || equipment.status == "Movement") {
      return Container(
        padding: const EdgeInsets.only(right: 5),
        child: const FaIcon(
          FontAwesomeIcons.solidMagic,
          color: Colors.pink,
          size: 23,
        ),
      );
    } else {
      if (equipment.isScanned || equipment.status == "Scanned") {
        return const FaIcon(
          FontAwesomeIcons.checkCircle,
          color: Color(0xff259B24),
        );
      } else if (!equipment.isScanned) {
        return const FaIcon(FontAwesomeIcons.barcode);
      }
    }
  }

  _getTag(AuditEquipment equipment) {
    if (equipment.isScanned == true) {
      if (equipment.scannedDateTime == null) {
        return Text(
          equipment.tag!,
          style: const TextStyle(
              height: 1.2,
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColor.blackTextColor),
        );
      } else {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Text(
              equipment.tag!,
              style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColor.blackTextColor),
            ),
            const SizedBox(
              height: 2,
            )
          ],
        );
      }
    } else {
      return Text(
        equipment.tag!,
        style: const TextStyle(
            height: 1.2,
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppColor.blackTextColor),
      );
    }
  }

  _createServiceRequestWidget(AuditEquipment equipment) => Container(
        margin: const EdgeInsets.symmetric(horizontal: 10),
        padding: const EdgeInsets.symmetric(vertical: 10),
        decoration: BoxDecoration(
            border: Border.all(color: Colors.grey),
            borderRadius: BorderRadius.circular(15)),
        width: 130,
        height: 100,
        child: InkWell(
          onTap: () async {
            _positionStreamSubscription!.cancel();
            setState(() {
              // _isBottomSheetOpen = false;
              _selectedMarkerId = null;
            });
            getIt<ServiceType>().type = ServiceRequestType.NOTIFICATION;
            BlocConsumer<ApiBloc, ApiState>(
              listener: (listenerContext, state) {
                if (state is BarcodeApiCalled &&
                    getIt<ServiceType>().type == ServiceRequestType.ADD) {
                  apiBloc.add(ResetBarCodeApi());

                  getIt<BarcodeResponse>().barcodeResponseInstance =
                      state.barcodeResponse;
                  if (state.barcodeResponse.serviceRequestCount == null ||
                      state.barcodeResponse.serviceRequestCount == 0) {
                    Navigator.pushNamed(
                        context, AddServiceRequestPage.routeName,
                        arguments: ServiceType(
                          type: ServiceRequestType.NOTIFICATION,
                        ));
                  } else {
                    Navigator.pushNamed(context, BardCodeResultPage.routeName,
                        arguments: ServiceType(
                          type: ServiceRequestType.NOTIFICATION,
                        ));
                  }
                } else if (state is BarcodeApiCalled &&
                    getIt<ServiceType>().type ==
                        ServiceRequestType.MOVE_EQUIPMENT) {
                  Navigator.pushNamed(context, AddServiceRequestPage.routeName,
                      arguments:
                          ServiceType(type: ServiceRequestType.MOVE_EQUIPMENT));
                }
              },
              builder: (context, state) {
                bool isCalling = state is BarcodeApiCalling;

                return AnimatedContainer(
                  duration: const Duration(milliseconds: 500),
                  height: isCalling ? 0 : 50,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      ElevatedButton(
                        onPressed: () {},
                        child: const Text("_okayText"),
                      ),
                    ],
                  ),
                );
              },
            );
            await initBarcodeData(equipment);
          },
          child: Center(
            child: Column(
              children: [
                FaIcon(
                  FontAwesomeIcons.plus,
                  size: 40,
                  color: Theme.of(context).primaryColor,
                ),
                Text(
                  AppLocalizations.of(context)!.createNewNotification,
                  textAlign: TextAlign.center,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
          ),
        ),
      );

  _getExistingServiceRequestWidget(ServiceRequestDetail serviceRequestDetail) {
    if (kDebugMode) {
      print(serviceRequestDetail.toString());
    }
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 10),
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 5),
      decoration: BoxDecoration(
          border: Border.all(color: Colors.grey),
          borderRadius: BorderRadius.circular(15)),
      width: 130,
      height: 100,
      child: InkWell(
        onTap: () {
          if (kDebugMode) {}
          Navigator.pushNamed(context, BarcodeResultDetailPage.routeName,
              arguments: FetchSingleServiceRequestInAudit(
                  id: serviceRequestDetail.requestId!,
                  isCalledFromAudit: true,
                  isTimedService: false));
        },
        child: Center(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                serviceRequestDetail.description!,
                textAlign: TextAlign.start,
                style:
                    const TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                overflow: TextOverflow.ellipsis,
                maxLines: 4,
              ),
              const SizedBox(
                height: 10,
              ),
              Expanded(
                child: Text(
                  serviceRequestDetail.taskTypeDesc == null
                      ? serviceRequestDetail.requestType!
                      : serviceRequestDetail.taskTypeDesc!,
                  textAlign: TextAlign.start,
                  style: const TextStyle(
                      color: AppColor.greyTextColor, fontSize: 12),
                ),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Flexible(
                    child: Text(
                      serviceRequestDetail.requestId.toString(),
                      textAlign: TextAlign.start,
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ),
                  _getStatusType(serviceRequestDetail),
                ],
              )
            ],
          ),
        ),
      ),
    );
  }

  Future<void> initBarcodeData(AuditEquipment equipment) async {
    if (equipment.equipmentId == null) {
      try {
        BarcodeResponse barcodeResponse = await ApiService()
            .getEquipmentDetailByBarcodeNumber(equipment.tag!, false);

        if (barcodeResponse is BarcodeResponse) {
          getIt<BarcodeResponse>().barcodeResponseInstance = BarcodeResponse(
              equipment: barcodeResponse.equipment,
              auditId: AuditEquipmentCubit.auditId);
          if (getIt<BarcodeResponse>().barcodeResponseInstance
              is BarcodeResponse) {
            if (getIt<BarcodeResponse>()
                    .barcodeResponseInstance!
                    .equipment!
                    .isInActiveEquipment ==
                true) {
              ApplicationUtil.showSnackBar(
                  context: context,
                  message: AppLocalizations.of(context)!
                      .isInActiveEquipmentCannotCreateNotification);
            } else {
              Navigator.pushNamed(context, AddServiceRequestPage.routeName,
                  arguments:
                      ServiceType(type: ServiceRequestType.NOTIFICATION));
            }
          } else {
            ApplicationUtil.showSnackBar(
                context: context,
                message: AppLocalizations.of(context)!.barcodeNotfound +
                    ". Barcode is: ${equipment.tag!}");
          }
        } else {
          isInValidBarcode = true;
        }
      } catch (e) {
        isInValidBarcode = true;
        Logger.e('${ApplicationUtil.getFormattedCurrentDateAndTime()} $e');
        ApplicationUtil.showSnackBar(
            context: context,
            message: AppLocalizations.of(context)!.barcodeNotfound +
                ". Barcode is: ${equipment.tag!}");
      }
    } else {
      getIt<BarcodeResponse>().barcodeResponseInstance = BarcodeResponse(
          equipment: Equipment(
            tag: equipment.tag,
            bomId: equipment.bomId,
            equipmentId: equipment.equipmentId,
            locationId: equipment.locationId,
            name: equipment.name,
            categoryName: equipment.categoryName,
            location: AuditEquipmentCubit.location,
          ),
          auditId: AuditEquipmentCubit.auditId);
      Navigator.pushNamed(context, AddServiceRequestPage.routeName,
          arguments: ServiceType(type: ServiceRequestType.NOTIFICATION));
    }
  }

  _getStatusType(ServiceRequestDetail serviceRequestDetail) {
    if (serviceRequestDetail.requestType != "NOTIFICATION") {
      if (serviceRequestDetail.remainingRequestTime != null) {
        if (serviceRequestDetail.remainingRequestTime! > 0) {
          return Container(
            width: 60,
            padding: const EdgeInsets.symmetric(vertical: 5),
            decoration: BoxDecoration(
              color: AppColor.orangeColor,
              borderRadius: BorderRadius.circular(5),
            ),
            child: Text(
              serviceRequestDetail.status!,
              textAlign: TextAlign.center,
              style: const TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold),
            ),
          );
        } else {
          return Container(
            width: 60,
            padding: const EdgeInsets.symmetric(vertical: 5),
            decoration: BoxDecoration(
              color: AppColor.redColor,
              borderRadius: BorderRadius.circular(5),
            ),
            child: Text(
              AppLocalizations.of(context)!.overdue,
              textAlign: TextAlign.center,
              style: const TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold),
            ),
          );
        }
      }
    } else {
      return Container(
        width: 70,
        padding: const EdgeInsets.symmetric(vertical: 5),
        decoration: BoxDecoration(
          color: const Color.fromRGBO(97, 97, 97, 100),
          borderRadius: BorderRadius.circular(5),
        ),
        child: Text(
          serviceRequestDetail.requestType.toString(),
          textAlign: TextAlign.center,
          style: const TextStyle(
              color: Colors.white, fontSize: 9, fontWeight: FontWeight.bold),
        ),
      );
    }
    return Container();
  }

  getDistanceCountWidget() {
    String distance =
        "${formatNumber(Provider.of<DistanceCalculator>(context, listen: false).distance?.toInt() ?? 0)} ft";
    if (distance == "0 ft") {
      distance = " ";
    }
    return (serviceEnabled &&
            permission != LocationPermission.deniedForever &&
            permission != LocationPermission.denied)
        ? Container(
            child: Row(
              children: [
                /*ColorFiltered(
                    colorFilter: const ColorFilter.mode(Color(0xff5555FF), BlendMode.srcATop),
                    child: Container(height: 30, width: 30, child: Image.asset("assets/images/placeholder.png"))),*/
                Container(
                    height: 30,
                    width: 30,
                    child: Image.asset("assets/images/loc.png")),
                const SizedBox(
                  width: 3,
                ),
                Expanded(
                  child: Text(
                    distance,
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                )
              ],
            ),
          )
        : Container();
  }

  _equipmentListAppBar() {
    List auditLocation = [];
    for (var element in AuditEquipmentCubit.location!) {
      element.forEach((key, value) {
        if (key == "NAME") {
          auditLocation.add(value);
        }
      });
    }
    String locationId = AuditEquipmentCubit.location!.last['LOCATION_ID'];
    locationId.replaceAll("-", " • ");
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Expanded(
          child: Container(
            margin: const EdgeInsets.only(left: 20, right: 20, top: 5),
            child: Text(
              auditLocation.join(" • ").toString(),
              softWrap: true,
              style: const TextStyle(
                  color: AppColor.blackTextColor,
                  fontSize: AppConstant.toolbarTitleFontSize,
                  fontWeight: FontWeight.bold),
            ),
          ),
        ),
      ],
    );
  }

  _getConditionAuditCheckBox() {
    return BlocBuilder<UserBloc, UserState>(
      builder: (context, state) {
        if (state is LoginUserFetched &&
            state.userData.MODULE!.contains("ENABLE_CONDITION_AUDIT")) {
          return Container(
            margin: const EdgeInsets.only(left: 0, top: 0),
            child: CheckboxListTile(
              visualDensity: const VisualDensity(horizontal: -4, vertical: -4),
              title: Text(
                  "Enable Conditional ${widget.mapArguments.auditRefId?.serviceType}"),
              value: isConditionalAudit,
              onChanged: (newValue) {
                setState(() {
                  isConditionalAudit = newValue!;
                });
                if (isConditionalAudit) {
                  ApplicationUtil.showSnackBar(
                      context: context,
                      message:
                          "Enable Conditional ${widget.mapArguments.auditRefId?.serviceType}");
                } else {
                  ApplicationUtil.showSnackBar(
                      context: context,
                      message:
                          "Disable Conditional ${widget.mapArguments.auditRefId?.serviceType}");
                }
              },
              controlAffinity: ListTileControlAffinity.leading,
            ),
          );
        } else {
          return Container();
        }
      },
    );
  }

  Future<void> _focusOnMarkers() async {
    if (locations.isEmpty) {
      if (_currentPosition != null) {
        GoogleMapController controller = await _controller.future;
        controller.animateCamera(CameraUpdate.newCameraPosition(
          CameraPosition(target: _currentPosition!, zoom: 15.0),
        ));
      }
      return;
    } else {
      double minLat = double.infinity, maxLat = -double.infinity;
      double minLng = double.infinity, maxLng = -double.infinity;

      for (var e in locations) {
        if (e["LATITUDE"] != null && e["LONGITUDE"] != null) {
          double lat = e["LATITUDE"];
          double lng = e["LONGITUDE"];
          if (lat < minLat) minLat = lat;
          if (lat > maxLat) maxLat = lat;
          if (lng < minLng) minLng = lng;
          if (lng > maxLng) maxLng = lng;
        }
      }

      LatLngBounds bounds = LatLngBounds(
        southwest: LatLng(minLat, minLng),
        northeast: LatLng(maxLat, maxLng),
      );

      GoogleMapController controller = await _controller.future;
      controller.animateCamera(CameraUpdate.newLatLngBounds(bounds, 100));
    }
  }

  _getScanToRepairButton() => Container(
        alignment: Alignment.bottomCenter,
        child: Center(
          child: BlocConsumer<RepairDetailApiCubit, RepairDetailState>(
            listener: (context, state) async {
              if (state is FetchSingleRepairsDetailEquipmentIdError) {
                if (state.errorMessage == ApiResponse.INVALID_AUTH) {
                  Navigator.pushNamedAndRemoveUntil(
                      context, LoginPage.routeName, (route) => false,
                      arguments: true);
                }
              } else if (state
                  is FetchedSingleRepairsDetailByEquipmentIdForMap) {
                if (state.serviceRequestDetailList.isEmpty) {
                  bool? afsUser = getIt<SharedPreferences>().getBool('isAfs');
                  if (afsUser != null && afsUser == true) {
                    showAlertDialog(context);
                  } else {
                    ApplicationUtil.showSnackBar(
                        context: context,
                        message: customerId == 15
                            ? AppLocalizations.of(context)!
                                .thereIsNoServiceRequestWithThisAsset
                            : AppLocalizations.of(context)!
                                .thereIsNoServiceRequestWithThisEquipment);
                  }
                } else {
                  ServiceRequestDetail serviceRequestDetail =
                      state.serviceRequestDetailList[0];
                  RepairServiceRequest serviceRequest = RepairServiceRequest(
                      requestId: serviceRequestDetail.requestId!,
                      customerId: serviceRequestDetail.customerId,
                      equipmentName: serviceRequestDetail.equipmentName,
                      equipmentId: serviceRequestDetail.equipmentId,
                      requestType: serviceRequestDetail.requestType,
                      currentLocationId: serviceRequestDetail.currentLocationId,
                      newLocationId: serviceRequestDetail.newLocationId,
                      taskType: serviceRequestDetail.taskType,
                      taskTypeDesc: serviceRequestDetail.taskTypeDesc,
                      description: serviceRequestDetail.description,
                      createdBy: serviceRequestDetail.createdBy,
                      createdAt: serviceRequestDetail.createdDate,
                      status: serviceRequestDetail.status,
                      remainingRequestTime:
                          serviceRequestDetail.remainingRequestTime,
                      equipmentCategoryName:
                          serviceRequestDetail.equipmentCategoryName,
                      location: serviceRequestDetail.location,
                      equipmentLocationId:
                          serviceRequestDetail.equipmentLocationId,
                      tag: serviceRequestDetail.tag);
                  var result = await Navigator.pushNamed(
                      context, RepairDetailPage.routeName,
                      arguments: RepairDetailPageParams(
                          repairServiceRequest: serviceRequest,
                          isTimedService: false));
                  if (result is String) {
                    ApplicationUtil.showSnackBar(
                        context: context, message: result);
                    if (result ==
                        AppLocalizations.of(context)!
                            .cancelServiceRequestSuccess) {
                      Future.delayed(const Duration(milliseconds: 500));
                    }
                    if (result == "SAVED") {
                      locations.removeWhere((element) =>
                          element["REQUEST_ID"] == serviceRequest.requestId);
                      print("locations: ${locations.length}");
                      _loadMarkers();
                      setState(() {
                        _isBottomSheetOpen = false;
                        _selectedMarkerId = null;
                        _selectedMarker = null;
                      });
                    }
                    repairListBloc.add(
                      FetchRepairServiceRequestList(
                          refresh: true, query: query),
                    );
                    setState(() {});
                  }
                }
              }
            },
            builder: (context, state) {
              if (state is FetchingSingleRepairsDetailByEquipmentId) {
                return const SizedBox(
                    width: 30,
                    height: 30,
                    child: Padding(
                      padding: EdgeInsets.all(8.0),
                      child: CircularProgressIndicator(),
                    ));
              }
              return ScanBarCodeButton.getScanBarcodeButton(
                context,
                onTap: () async {
                  getIt<ServiceType>().type = ServiceRequestType.DIRECT_REPAIR;
                  var result = await Navigator.pushNamed(
                          context, AIBarCodeScannerPage.routeName) ??
                      "";
                  if (result != null) {
                    Map<String, dynamic> resultData =
                        result as Map<String, dynamic>;
                    String returnedCode = resultData['code'];
                    bool isScanned = resultData['isScanned'];
                    if (isScanned) {
                      apiBloc.add(CallBarCodeApi(
                          barcodeNumber: returnedCode,
                          isTimedService: false,
                          lattitude: _currentPosition!.latitude!,
                          longitude: _currentPosition!.longitude!,
                          isFromMap: true));
                    } else {
                      apiBloc.add(CallBarCodeApi(
                          barcodeNumber: returnedCode,
                          isTimedService: false,
                          isFromMap: true));
                    }
                  }
                },
                label: AppLocalizations.of(context)!.scanToRepair,
              );
            },
          ),
        ),
      );

  void showAlertDialog(BuildContext ctx) {
    AlertDialog alert = AlertDialog(
      title: Text(AppLocalizations.of(context)!.noServiceFound),
      content: Text(
        customerId == 15
            ? AppLocalizations.of(context)!.noServiceRequestDescForStadium
            : AppLocalizations.of(context)!.noServiceRequestDesc,
        style: const TextStyle(height: 1.2),
      ),
      actions: [
        ElevatedButton(
          child: Text(AppLocalizations.of(context)!.continueString),
          onPressed: () {
            Navigator.of(ctx).pop();
            getIt<ServiceType>().type = ServiceRequestType.DIRECT_REPAIR;

            BlocProvider.of<ServiceRequestBloc>(context)
                .add(ResetServiceRequestBloc());
            Navigator.pushNamed(
                    context, AfsServiceRequestAndRepairPage.routeName,
                    arguments:
                        ServiceType(type: ServiceRequestType.DIRECT_REPAIR))
                .then((shouldRefresh) {
              if (shouldRefresh is bool && shouldRefresh == true) {
                repairListBloc.add(
                    FetchRepairServiceRequestList(refresh: true, query: query));
              }
            });
          },
        ),
        ElevatedButton(
          child: Text(AppLocalizations.of(context)!.cancel),
          onPressed: () {
            Navigator.of(ctx).pop();
          },
        ),
      ],
    );
    showDialog(
      barrierDismissible: false,
      context: ctx,
      builder: (BuildContext context) {
        return alert;
      },
    );
  }

  Widget _getSingleServiceRequestItem(RepairServiceRequest serviceRequest) {
    return StreamBuilder<List<RepairData>>(
        stream: repairBloc.repairDao.getPendingRepairFromDbAsStream(),
        builder: (context, snapshot) {
          bool isPresentInDb = false;

          if (snapshot.hasData) {
            List<RepairData> repairData = snapshot.data!
                .where(
                    (element) => element.requestId == serviceRequest.requestId)
                .toList();
            if (repairData.isNotEmpty) {
              isPresentInDb = true;
            }
          }

          return InkWell(
            child: Container(
              margin: const EdgeInsets.only(
                  top: 10, left: 10, right: 10, bottom: 0),
              child: Card(
                elevation: 2,
                child: ClipPath(
                  clipper: ShapeBorderClipper(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(3),
                    ),
                  ),
                  child: Container(
                    decoration: BoxDecoration(
                      border: isPresentInDb
                          ? Border.all(color: AppColor.greyBorderColor)
                          : null,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 10, vertical: 10),
                      child: Column(
                        children: [
                          Row(
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      serviceRequest.equipmentName ?? '',
                                      style: const TextStyle(
                                          fontSize: 24,
                                          color: AppColor.blackTextColor),
                                    ),
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      children: [
                                        FaIcon(
                                          FontAwesomeIcons.solidBarcode,
                                          color: Theme.of(context).primaryColor,
                                        ),
                                        const SizedBox(
                                          width: 5,
                                        ),
                                        Text(
                                          serviceRequest.tag ?? '',
                                          style: const TextStyle(
                                              height: 1.2,
                                              fontSize: 18,
                                              fontWeight: FontWeight.bold,
                                              color: AppColor.blackTextColor),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(
                                      height: 5,
                                    ),
                                    Text(
                                      serviceRequest.taskTypeDesc == null
                                          ? serviceRequest.requestType!
                                          : serviceRequest.taskTypeDesc!,
                                      overflow: TextOverflow.ellipsis,
                                      style: const TextStyle(
                                          fontSize: 16,
                                          color: AppColor.blackTextColor),
                                    ),
                                    const SizedBox(
                                      height: 2,
                                    ),
                                  ],
                                ),
                              ),
                              Flexible(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.end,
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.end,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        const SizedBox(
                                          width: 10,
                                        ),
                                        Flexible(
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              _getRepairStatusType(
                                                  serviceRequest),
                                            ],
                                          ),
                                        )
                                      ],
                                    ),
                                  ],
                                ),
                              )
                            ],
                          ),
                          const SizedBox(
                            height: 5,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Flexible(
                                child: Text(
                                  '${serviceRequest.description}',
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                  style: const TextStyle(
                                      fontSize: 16,
                                      color: AppColor.blackTextColor),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(
                            height: 5,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                child: Text(
                                  '${ApplicationUtil.getEndLocationFromLocation(locationId: serviceRequest.equipmentLocationId!, locationMap: serviceRequest.location!)['NAME']}',
                                  style: const TextStyle(
                                      fontSize: 16,
                                      color: AppColor.blackTextColor),
                                ),
                              ),
                              _getStatusFromDatabase(isPresentInDb),
                              const SizedBox(width: 10),
                              if (serviceRequest.requestType == 'SCHEDULED')
                                Tooltip(
                                  message: AppLocalizations.of(context)!
                                      .scheduleService,
                                  child: FaIcon(
                                    FontAwesomeIcons.clock,
                                    color: Theme.of(context).primaryColor,
                                    size: 20,
                                  ),
                                ),
                              const SizedBox(width: 10),
                              _getImageCount(serviceRequest),
                            ],
                          )
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          );
        });
  }

  _getRepairStatusType(RepairServiceRequest serviceRequest) {
    if (serviceRequest.remainingRequestTime != null) {
      if (serviceRequest.remainingRequestTime! > 0) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Container(
                width: 130,
                padding: const EdgeInsets.symmetric(vertical: 5),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(5),
                ),
                child: getDistanceCountWidget()),
            const SizedBox(
              height: 5,
            ),
            Container(
              width: 130,
              padding: const EdgeInsets.symmetric(
                vertical: 5,
              ),
              decoration: BoxDecoration(
                color: AppColor.orangeColor,
                borderRadius: BorderRadius.circular(5),
              ),
              child: Text(
                serviceRequest.status!,
                textAlign: TextAlign.center,
                style: const TextStyle(
                    color: Colors.white,
                    fontSize: 13,
                    fontWeight: FontWeight.bold),
              ),
            ),
            const SizedBox(
              height: 5,
            ),
            Container(
              width: 130,
              padding: const EdgeInsets.symmetric(vertical: 2),
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor,
                borderRadius: BorderRadius.circular(5),
              ),
              child: Text(
                ApplicationUtil.getHourAndMinuteFromMinute(
                        serviceRequest.remainingRequestTime!) +
                    ' ' +
                    AppLocalizations.of(context)!.remaining,
                textAlign: TextAlign.center,
                style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold),
              ),
            ),
            const SizedBox(
              height: 5,
            ),
            serviceRequest.safetyIssue != null
                ? Image.asset(
                    width: 25,
                    height: 25,
                    "assets/images/hazard.png",
                  )
                : Container(
                    width: 0,
                  ),
          ],
        );
      } else {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Container(
                width: 130,
                padding: const EdgeInsets.symmetric(vertical: 5),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(5),
                ),
                child: getDistanceCountWidget()),
            const SizedBox(
              height: 5,
            ),
            Container(
              width: 130,
              padding: const EdgeInsets.symmetric(vertical: 5),
              decoration: BoxDecoration(
                color: AppColor.redColor,
                borderRadius: BorderRadius.circular(5),
              ),
              child: Text(
                AppLocalizations.of(context)!.overdue,
                textAlign: TextAlign.center,
                style: const TextStyle(
                    color: Colors.white,
                    fontSize: 13,
                    fontWeight: FontWeight.bold),
              ),
            ),
            const SizedBox(
              height: 5,
            ),
            Container(
              width: 130,
              padding: const EdgeInsets.symmetric(vertical: 2),
              decoration: BoxDecoration(
                color: AppColor.redColor,
                borderRadius: BorderRadius.circular(5),
              ),
              child: Text(
                ApplicationUtil.getHourAndMinuteFromMinute(
                        serviceRequest.remainingRequestTime!) +
                    ' ' +
                    AppLocalizations.of(context)!.overdue,
                textAlign: TextAlign.center,
                style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold),
              ),
            ),
            const SizedBox(
              height: 5,
            ),
            serviceRequest.safetyIssue != null
                ? Image.asset(
                    width: 25,
                    height: 25,
                    "assets/images/hazard.png",
                  )
                : Container(
                    width: 0,
                  ),
          ],
        );
      }
    }
    return Container();
  }

  _getStatusFromDatabase(bool isPresentInDb) {
    if (isPresentInDb) {
      return Text(AppLocalizations.of(context)!.processing);
    }
    return Container();
  }

  _getImageCount(RepairServiceRequest serviceRequest) {
    if (serviceRequest.documentCount != null &&
        serviceRequest.documentCount != 0) {
      return InkWell(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            FaIcon(
              FontAwesomeIcons.image,
              size: 20,
              color: Theme.of(context).primaryColor,
            ),
            const SizedBox(
              width: 2,
            ),
            _getImageCountText(serviceRequest),
          ],
        ),
        onTap: () async {
          ApplicationUtil.showLoaderDialog(
              context, AppLocalizations.of(context)!.pleaseWait);
          try {
            var response = await ApiService()
                .getServiceRequestListByEquipmentId(
                    serviceRequest.equipmentId!, true, false);
            if (response is List<ServiceRequestDetail>) {
              List<ServiceRequestDetail> serviceRequestDetailList = response;
              var srToShowPhotos;
              serviceRequestDetailList.forEach((element) {
                if (element.requestId == serviceRequest.requestId) {
                  srToShowPhotos = element;
                }
              });
              Navigator.of(context).pop();
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => ImagePageView(
                    serviceRequest: srToShowPhotos,
                  ),
                ),
              );
            } else {
              Navigator.of(context).pop();
              ApplicationUtil.showWarningAlertDialog(context,
                  title: AppLocalizations.of(context)!.errorOccurWhileFetching,
                  desc: AppLocalizations.of(context)!.netWorkErrorTryAgain,
                  negativeLabel: AppLocalizations.of(context)!.okay);
            }
          } catch (e) {
            Navigator.of(context).pop();
            ApplicationUtil.showWarningAlertDialog(context,
                title: AppLocalizations.of(context)!.error,
                desc: AppLocalizations.of(context)!.netWorkErrorTryAgain,
                negativeLabel: AppLocalizations.of(context)!.okay);
          }
        },
      );
    }
    {
      return const SizedBox();
    }
  }

  _getImageCountText(RepairServiceRequest serviceRequest) {
    if (serviceRequest.documentCount != null &&
        serviceRequest.documentCount! > 1) {
      return Text(
        "+" + (serviceRequest.documentCount! - 1).toString(),
        textAlign: TextAlign.center,
        style: TextStyle(
          color: Theme.of(context).primaryColor,
        ),
      );
    } else {
      return const Text('');
    }
  }

  _repairAppbar() => Container(
        margin: const EdgeInsets.only(left: 10, bottom: 10),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            BlocBuilder<RepairListApiBloc, RepairListApiState>(
              builder: (context, state) {
                if (state is FetchedRepairServiceRequestList) {
                  RepairServiceRequestCount repairCountResponse;
                  int count =
                      state.repairServiceRequestList.serviceRequestCount;
                  return Text(
                    count > 0
                        ? '${AppLocalizations.of(context)!.serviceRequests} ($count)'
                        : '${AppLocalizations.of(context)!.serviceRequests} ',
                    style: const TextStyle(
                        color: AppColor.blackTextColor,
                        fontSize: AppConstant.toolbarTitleFontSize,
                        fontWeight: FontWeight.bold),
                  );
                }
                return Text(
                  '${AppLocalizations.of(context)!.serviceRequests} ',
                  style: const TextStyle(
                      color: AppColor.blackTextColor,
                      fontSize: AppConstant.toolbarTitleFontSize,
                      fontWeight: FontWeight.bold),
                );
              },
            ),
          ],
        ),
      );

  String formatNumber(int value) {
    final formatter = NumberFormat('#,##0.###'); // Formats numbers with commas
    return formatter.format(value);
  }

  void _loadMarkers() {
    _equipmentMap.clear();
    for (var equip in locations) {
      if (equip['LATITUDE'] != null && equip['LONGITUDE'] != null) {
        LatLng position = LatLng(equip['LATITUDE'], equip['LONGITUDE']);
        _equipmentMap.putIfAbsent(position, () => []).add(equip);
      }
    }
    _setMarkers();
  }

  void _setMarkers() {
    _markers.clear();
    if (_equipmentMap.isEmpty) {
      Logger.i("No equipment data available to display markers.");
      return;
    }
    _equipmentMap.forEach((position, equipmentList) async {
      _markers.add(
        Marker(
          markerId: MarkerId(position.toString()),
          position: position,
          icon: BitmapDescriptor.defaultMarkerWithHue(_selectedMarkerId ==
                  position.toString()
              // (widget.mapArguments.isFromAudit ? equipmentList[0]["EQUIPMENT_ID"].toString() : equipmentList[0]["REQUEST_ID"].toString())
              ? BitmapDescriptor.hueBlue
              : widget.mapArguments.isFromAudit
                  ? ServiceRequestBloc.equipmentList
                          .where((element) =>
                              element.equipmentId ==
                              equipmentList[0]["EQUIPMENT_ID"])
                          .toList()
                          .first
                          .isScanned
                      ? BitmapDescriptor.hueGreen
                      : BitmapDescriptor.hueRed
                  : 0.0),
          /*icon: await getCustomMarker(
            _selectedMarkerId ==
                (widget.mapArguments.isFromAudit ? equipmentList[0]["EQUIPMENT_ID"].toString() : equipmentList[0]["REQUEST_ID"].toString())
                ? "blue"
                : widget.mapArguments.isFromAudit
                ? ServiceRequestBloc.equipmentList.where((element) => element.equipmentId == equipmentList[0]["EQUIPMENT_ID"]).first.isScanned
                ? "green"
                : "red"
                : "red",
          ),*/
          onTap: () => _onMarkerTap1(position, equipmentList.toSet().toList()),
        ),
      );
    });
    setState(() {});
  }

  void _collapseMarkers() {
    setState(() {
      expandedMarkers.clear();
      _setMarkers();
    });
  }

  void _onMarkerTap1(
      LatLng position, List<Map<String, dynamic>> equipmentList) {
    if (equipmentList.isNotEmpty) {
      _onMarkerTap(equipmentList[0]);
    }
    /* if (equipmentList.length == 1) {
      _onMarkerTap(equipmentList[0]);
    } else {
      if (expandedMarkers.isNotEmpty) {
        _collapseMarkers();
      } else {
        _expandMarkers(position, equipmentList);
      }
    }*/
  }

  void _expandMarkers(
      LatLng center, List<Map<String, dynamic>> equipmentList) async {
    expandedMarkers = {};
    double radius = 0.00004;

    for (int i = 0; i < equipmentList.length; i++) {
      double angle = (2 * pi / equipmentList.length) * i;
      LatLng newPosition = LatLng(
        center.latitude + radius * cos(angle),
        center.longitude + radius * sin(angle),
      );

      expandedMarkers.add(
        Marker(
          markerId: MarkerId(newPosition.toString()),
          position: newPosition,
          icon: BitmapDescriptor.defaultMarkerWithHue(_selectedMarkerId ==
                  newPosition.toString()
              //  (widget.mapArguments.isFromAudit ? equipmentList[0]["EQUIPMENT_ID"].toString() : equipmentList[0]["REQUEST_ID"].toString())
              ? BitmapDescriptor.hueBlue
              : widget.mapArguments.isFromAudit
                  ? ServiceRequestBloc.equipmentList
                          .where((element) =>
                              element.equipmentId ==
                              equipmentList[0]["EQUIPMENT_ID"])
                          .toList()
                          .first
                          .isScanned
                      ? BitmapDescriptor.hueGreen
                      : BitmapDescriptor.hueRed
                  : 0.0),
          /* icon: await getCustomMarker(
            _selectedMarkerId ==
                    (widget.mapArguments.isFromAudit ? equipmentList[0]["EQUIPMENT_ID"].toString() : equipmentList[0]["REQUEST_ID"].toString())
                ? "blue"
                : widget.mapArguments.isFromAudit
                    ? ServiceRequestBloc.equipmentList.where((element) => element.equipmentId == equipmentList[0]["EQUIPMENT_ID"]).first.isScanned
                        ? "green"
                        : "red"
                    : "red",
          ),*/
          onTap: () {
            _onMarkerTap(equipmentList[i]);
            //_onMarkerTap1(newPosition, [equipmentList[i]]);
          },
        ),
      );
    }

    setState(() {
      _markers.addAll(expandedMarkers);
    });
  }

  getCustomMarker(String color) async {
    return BitmapDescriptor.fromAssetImage(
      const ImageConfiguration(size: Size(30, 30)),
      'assets/images/$color.png',
    );
  }
}