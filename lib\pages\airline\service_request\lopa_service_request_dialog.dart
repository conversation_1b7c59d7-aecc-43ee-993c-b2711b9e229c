import 'dart:convert';
import 'dart:io';

import 'package:alink/cubit/lopa/selected_equipment_cubit.dart';
import 'package:alink/pages/airline/model/lopa_model.dart';
import 'package:alink/pages/airline/model/single_equipment.dart';
import 'package:alink/pages/full_image_view.dart';
import 'package:alink/util/app_color.dart';
import 'package:alink/util/application_util.dart';
import 'package:alink/widget/image_picker_afs.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:image_picker/image_picker.dart';
import '../../../logger/logger.dart';

import 'package:permission_handler/permission_handler.dart';

class LopaServiceRequestDialog extends StatefulWidget {
  final List<Part> seatItemList;
  final String title;
  final List<Map<String, dynamic>> imageList;
  final List<Map<String, dynamic>> repairImageList;
  final bool isDirectRepair;
  final String equipmentName;
  final SeatDetail seatDetail;

  const LopaServiceRequestDialog({
    Key? key,
    required this.seatItemList,
    required this.title,
    required this.imageList,
    required this.isDirectRepair,
    required this.repairImageList,
    required this.equipmentName,
    required this.seatDetail,
  }) : super(key: key);

  @override
  _LopaServiceRequestDialogState createState() =>
      _LopaServiceRequestDialogState();
}

class _LopaServiceRequestDialogState extends State<LopaServiceRequestDialog> {
  static const double padding = 20;
  Logger logger = Logger();
  List<LopaRepairPartItem> partHavingSRList = [];
  bool oneItemChecked = false;
  int checkedCount = 0;

  @override
  void initState() {
    //setRepair data
    if (widget.isDirectRepair) {
      setDirectRepairData(widget.equipmentName);
    }

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    Logger.i(
        "Class Name: ${this.runtimeType.toString()} time: ${DateTime.now()}");
    return Dialog(
      insetPadding: const EdgeInsets.symmetric(horizontal: 10),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(padding),
      ),
      elevation: 0,
      backgroundColor: Colors.transparent,
      child: contentBox(context),
    );
  }

  contentBox(context) {
    return Container(
      constraints: const BoxConstraints(maxWidth: 500),
      child: Stack(
        alignment: Alignment.topRight,
        children: <Widget>[
          Container(
            //width: MediaQuery.of(context).size.width,
            padding: const EdgeInsets.only(
                left: padding,
                top: padding,
                //top: avatarRadius + padding,
                right: padding,
                bottom: 10),
            //margin: EdgeInsets.only(top: avatarRadius),
            decoration: BoxDecoration(
                shape: BoxShape.rectangle,
                color: Colors.white,
                borderRadius: BorderRadius.circular(15),
                boxShadow: const [
                  BoxShadow(
                      color: Colors.black,
                      offset: Offset(0, 10),
                      blurRadius: 10),
                ]),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                const SizedBox(
                  height: 10,
                ),
                Text(
                  widget.title,
                  style: const TextStyle(
                      fontSize: 22, fontWeight: FontWeight.w600),
                ),
                const Divider(),
                Text(
                  AppLocalizations.of(context)!
                      .selectPartsWhichNeedToReplacedYouCannotSelectOpenServiceRequest,
                  style: const TextStyle(
                    fontSize: 18,
                  ),
                ),
                const Divider(),
                ListView.builder(
                  shrinkWrap: true,
                  itemCount: widget.seatItemList.length,
                  itemBuilder: (context, index) {
                    if ((partHavingSRList
                        .where((element) =>
                            element.partId == widget.seatItemList[index].part1)
                        .isNotEmpty)) {
                      return _disbaleUI(index);
                    }
                    if (widget.seatDetail.continueForRepairOrSR) {
                      return onlyPartNameListTile(index);
                    }
                    return _checkBoxListTile(index);
                  },
                ),
                //if (widget.seatItemList.length != partHavingSRList.length)
                (!widget.seatDetail.continueForRepairOrSR &&
                        widget.isDirectRepair == true)
                    ? TextButton(
                        onPressed: () {
                          var partsCheckedCount = 0;
                          widget.seatDetail.partList.forEach((element) {
                            if (element.isChecked) {
                              partsCheckedCount += 1;
                            }
                          });
                          if (partsCheckedCount > 0) {
                            setState(() {
                              widget.seatDetail.continueForRepairOrSR = true;
                            });
                          } else {
                            ApplicationUtil.showSnackBar(
                                context: context,
                                message: AppLocalizations.of(context)!
                                    .selectAtLeastOnePartContinue);
                          }
                        },
                        child: Text(
                          AppLocalizations.of(context)!.continueString,
                          style: const TextStyle(
                            fontSize: 18,
                          ),
                        ),
                      )
                    : _getSRAndRepairPhotos(),
              ],
            ),
          ),
          IconButton(
            onPressed: () {
              Navigator.pop(context);
            },
            icon: const FaIcon(FontAwesomeIcons.times),
          ),
        ],
      ),
    );
  }

  _getDamagePhotosWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            RichText(
              textAlign: TextAlign.start,
              text: TextSpan(
                  style: const TextStyle(fontSize: 18),
                  children: <TextSpan>[
                    TextSpan(
                      text: AppLocalizations.of(context)!.addPhotos,
                      style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).primaryColor),
                    ),
                  ]),
            ),
            TextButton(
              style: ButtonStyle(
                shape: MaterialStateProperty.all(
                  RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8.0),
                    side: BorderSide(color: Theme.of(context).primaryColor),
                  ),
                ),
                padding: MaterialStateProperty.all(
                  const EdgeInsets.symmetric(horizontal: 10),
                ),
              ),
              onPressed: () async {
                if (!kIsWeb) {
                  if (Platform.isAndroid) {
                    final androidInfo = await DeviceInfoPlugin().androidInfo;
                    if (androidInfo.version.sdkInt <= 32) {
                      /// use [Permissions.storage.status]
                      PermissionStatus photosPermission =
                          await Permission.storage.status;
                      print(photosPermission.toString());
                    } else {
                      /// use [Permissions.photos.status]
                      PermissionStatus photosPermission =
                          await Permission.photos.status;
                      print(photosPermission.toString());
                    }
                  }
                  // Plugin Works only for mobiles
                  PermissionStatus photosPermission =
                      await Permission.photos.status;
                  //If permission is ask before once
                  var status = await Permission.photos.status;
                  if (!status.isGranted) {
                    print("Permission not granted");
                    // We didn't ask for permission yet.
                    await Permission.photos.request();
                    //showPermissionDialog=true;
                  }
                  if (/*!isShown && photosPermission.isDenied || */ (photosPermission
                      .isGranted)) {
                    AfsImagePicker.onImageButtonPressed(
                      ImageSource.gallery,
                      context: context,
                      onReturn: (editedImageBase64) {
                        widget.imageList.add({
                          "DOCUMENT_TYPE": "image/jpeg",
                          "DOCUMENT_BLOB": editedImageBase64
                        });
                        // imageList.add(pickedImage);
                        setState(() {});
                      },
                    );
                  } else {
                    AfsImagePicker.askCameraPermission(context);
                  }
                } else {
                  AfsImagePicker.onImageButtonPressed(
                    ImageSource.gallery,
                    context: context,
                    onReturn: (editedImageBase64) {
                      widget.imageList.add({
                        "DOCUMENT_TYPE": "image/jpeg",
                        "DOCUMENT_BLOB": editedImageBase64
                      });
                      // imageList.add(pickedImage);
                      setState(() {});
                    },
                  );
                }
              },
              child: Text(
                AppLocalizations.of(context)!.browse,
                style: const TextStyle(
                  fontSize: 16,
                ),
              ),
            )
          ],
        ),
        const SizedBox(
          height: 5,
        ),
        _getImages(widget.imageList, isRepairedPhotos: false),
        const SizedBox(
          height: 10,
        ),
      ],
    );
  }

  _getRepairPhotosWidget() {
    return Visibility(
      visible: widget.seatDetail.markSeatIsRepaired,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              RichText(
                textAlign: TextAlign.start,
                text: TextSpan(
                    style: const TextStyle(fontSize: 18),
                    children: <TextSpan>[
                      TextSpan(
                        text: AppLocalizations.of(context)!.addRepairPhotos,
                        style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).primaryColor),
                      ),
                    ]),
              ),
              TextButton(
                style: ButtonStyle(
                  shape: MaterialStateProperty.all(
                    RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.0),
                      side: BorderSide(color: Theme.of(context).primaryColor),
                    ),
                  ),
                  padding: MaterialStateProperty.all(
                    const EdgeInsets.symmetric(horizontal: 10),
                  ),
                ),
                onPressed: () async {
                  if (!kIsWeb) {
                    if (Platform.isAndroid) {
                      final androidInfo = await DeviceInfoPlugin().androidInfo;
                      if (androidInfo.version.sdkInt <= 32) {
                        /// use [Permissions.storage.status]
                        PermissionStatus photosPermission =
                            await Permission.storage.status;
                        print(photosPermission.toString());
                      } else {
                        /// use [Permissions.photos.status]
                        PermissionStatus photosPermission =
                            await Permission.photos.status;
                        print(photosPermission.toString());
                      }
                    }
                    // Plugin Works only for mobiles
                    PermissionStatus photosPermission =
                        await Permission.photos.status;
                    //If permission is ask before once
                    var status = await Permission.photos.status;
                    if (!status.isGranted) {
                      print("Permission not granted");
                      // We didn't ask for permission yet.
                      await Permission.photos.request();
                      //showPermissionDialog=true;
                    }
                    if (/*!isShown && photosPermission.isDenied || */ (photosPermission
                        .isGranted)) {
                      AfsImagePicker.onImageButtonPressed(
                        ImageSource.gallery,
                        context: context,
                        onReturn: (editedImageBase64) {
                          widget.repairImageList.add({
                            "DOCUMENT_TYPE": "image/jpeg",
                            "DOCUMENT_BLOB": editedImageBase64
                          });
                          // imageList.add(pickedImage);
                          setState(() {});
                        },
                      );
                    } else {
                      AfsImagePicker.askCameraPermission(context);
                    }
                  } else {
                    AfsImagePicker.onImageButtonPressed(
                      ImageSource.gallery,
                      context: context,
                      onReturn: (editedImageBase64) {
                        widget.repairImageList.add({
                          "DOCUMENT_TYPE": "image/jpeg",
                          "DOCUMENT_BLOB": editedImageBase64
                        });
                        // imageList.add(pickedImage);
                        setState(() {});
                      },
                    );
                  }
                },
                child: Text(
                  AppLocalizations.of(context)!.browse,
                  style: const TextStyle(
                    fontSize: 16,
                  ),
                ),
              )
            ],
          ),
          const SizedBox(
            height: 5,
          ),
          _getRepairImage(widget.repairImageList, isRepaired: true),
          const SizedBox(
            height: 10,
          ),
        ],
      ),
    );
  }

  _getImages(List<Map<String, dynamic>> imageList,
      {required bool isRepairedPhotos}) {
    if (imageList.isEmpty) {
      return _imagePlaceHolder(isRepaired: isRepairedPhotos);
    }
    return SizedBox(
      height: 75,
      child: ListView.builder(
        padding: const EdgeInsets.all(0),
        scrollDirection: Axis.horizontal,
        itemCount: imageList.length,
        itemBuilder: (context, index) {
          var base64Img = imageList[index]['DOCUMENT_BLOB'];
          if (index == 0) {
            return Row(
              children: [
                _imagePlaceHolder(isRepaired: isRepairedPhotos),
                _getSingleImage(base64Img, index, imageList)
              ],
            );
          } else {
            return _getSingleImage(base64Img, index, imageList);
          }
        },
      ),
    );
  }

  _getRepairImage(List<Map<String, dynamic>> imageList,
      {required bool isRepaired}) {
    if (imageList.isEmpty) {
      return _imagePlaceHolder(isRepaired: isRepaired);
    }
    return SizedBox(
      height: 75,
      child: ListView.builder(
        padding: const EdgeInsets.all(0),
        scrollDirection: Axis.horizontal,
        itemCount: imageList.length,
        itemBuilder: (context, index) {
          var base64Img = imageList[index]['DOCUMENT_BLOB'];
          if (index == 0) {
            return Row(
              children: [
                _imagePlaceHolder(isRepaired: isRepaired),
                _getSingleImage(base64Img, index, imageList)
              ],
            );
          } else {
            return _getSingleImage(base64Img, index, imageList);
          }
        },
      ),
    );
  }

  _imagePlaceHolder({required bool isRepaired}) => InkWell(
        onTap: () async {
          PermissionStatus cameraPermission = await Permission.camera.status;
          //If permission is ask before once
          bool isShown = await Permission.camera.shouldShowRequestRationale;
          if (!isShown && cameraPermission.isDenied ||
              (cameraPermission.isGranted)) {
            AfsImagePicker.onImageButtonPressed(
              ImageSource.camera,
              context: context,
              onReturn: (editedImageBase64) {
                if (isRepaired) {
                  widget.repairImageList.add({
                    "DOCUMENT_TYPE": "image/jpeg",
                    "DOCUMENT_BLOB": editedImageBase64
                  });
                } else {
                  widget.imageList.add({
                    "DOCUMENT_TYPE": "image/jpeg",
                    "DOCUMENT_BLOB": editedImageBase64
                  });
                }
                // imageList.add(pickedImage);
                setState(() {});
              },
            );
          } else {
            AfsImagePicker.askCameraPermission(context);
          }
        },
        child: Container(
          margin: const EdgeInsets.only(right: 10),
          decoration: BoxDecoration(
              border: Border.all(color: Colors.grey),
              borderRadius: BorderRadius.circular(15)),
          width: 70,
          height: 70,
          child: Center(
            child: FaIcon(
              FontAwesomeIcons.solidCamera,
              size: 25,
              color: Theme.of(context).primaryColor,
            ),
          ),
        ),
      );

  _getSingleImage(
          base64imageString, int index, List<Map<String, dynamic>> imageList) =>
      SizedBox(
        height: 75,
        child: Stack(
          alignment: Alignment.center,
          children: [
            InkWell(
              onTap: () {
                print(index);
                Navigator.pushNamed(context, ImageViewPage.routeName,
                    arguments:
                        ImageWithTag(base64: base64imageString, index: index));
              },
              child: Container(
                height: 65,
                margin: const EdgeInsets.symmetric(horizontal: 5),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(5),
                ),
                child: ClipRRect(
                  borderRadius: const BorderRadius.all(Radius.circular(5)),
                  child: Hero(
                    tag: index,
                    child: Image.memory(
                      base64Decode(base64imageString),
                      fit: BoxFit.cover,
                      height: 65,
                      width: 65,
                    ),
                  ),
                ),
              ),
            ),
            Positioned(
              top: -1,
              right: 0,
              child: InkWell(
                onTap: () {
                  imageList.removeAt(index);
                  setState(() {});
                },
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(15),
                    color: Colors.white,
                  ),
                  child: const FaIcon(
                    FontAwesomeIcons.solidTimesCircle,
                    color: Colors.red,
                    size: 18,
                  ),
                ),
              ),
            ),
          ],
        ),
      );

  void setDirectRepairData(String equipmentName) {
    List<SingleEquipmentAndPart> equipmentAndPartList = [];

    for (var element in SelectedFleetCubit.serviceRequestList) {
      if (element.requestId > -1) {
        element.partsMap!.forEach((key, value) {
          Map<String, dynamic> map = {};
          map['equipmentName'] = key;
          map['partData'] = value;
          SingleEquipmentAndPart singleEquipment =
              SingleEquipmentAndPart.fromMap(map, element);
          equipmentAndPartList.add(singleEquipment);
        });
      }
    }
    if (equipmentAndPartList.isNotEmpty) {
      bool isEmpty = equipmentAndPartList
          .where((element) => element.equipmentName == equipmentName)
          .isEmpty;

      if (!isEmpty) {
        for (var singleEquipment in equipmentAndPartList) {
          if (singleEquipment.equipmentName == equipmentName) {
            partHavingSRList = [
              ...partHavingSRList,
              ...singleEquipment.partList
            ];
          }
        }
      }
    }
  }

  _getSRAndRepairPhotos() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(
          height: 10,
        ),
        const Divider(),
        _getDamagePhotosWidget(),
        if (widget.isDirectRepair)
          Column(
            children: [
              const Divider(),
              ListTile(
                onTap: () {
                  setState(() {
                    widget.seatDetail.markSeatIsRepaired =
                        !widget.seatDetail.markSeatIsRepaired;
                  });
                },
                contentPadding: EdgeInsets.zero,
                visualDensity: const VisualDensity(horizontal: 0, vertical: -4),
                title: Text(
                  AppLocalizations.of(context)!.markAsRepaired,
                  style: TextStyle(
                      fontSize: 18,
                      color: Theme.of(context).primaryColor,
                      fontWeight: FontWeight.bold),
                ),
                trailing: widget.seatDetail.markSeatIsRepaired
                    ? Icon(
                        Icons.check_box,
                        color: Theme.of(context).primaryColor,
                      )
                    : Icon(
                        Icons.check_box_outline_blank,
                        color: Theme.of(context).primaryColor,
                      ),
              ),
              _getRepairPhotosWidget(),
            ],
          ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            widget.isDirectRepair
                ? TextButton(
                    onPressed: () {
                      ApplicationUtil.showWarningAlertDialog(context,
                          title: AppLocalizations.of(context)!.alert,
                          desc: AppLocalizations.of(context)!
                              .clickingOnThisResetWillClearAllTheTempData,
                          positiveLabel: AppLocalizations.of(context)!.reset,
                          positiveLabelColor: AppColor.redColor,
                          negativeLabel: AppLocalizations.of(context)!.cancel,
                          onPositiveClickListener: () {
                        widget.seatDetail.continueForRepairOrSR = false;
                        widget.seatDetail.markSeatIsRepaired = false;
                        widget.imageList.clear();
                        widget.repairImageList.clear();
                        for (var element in widget.seatItemList) {
                          element.isChecked = false;
                          element.checkedCount = 0;
                        }

                        Navigator.pop(context);
                        Navigator.pop(context);
                      });
                    },
                    child: Text(
                      AppLocalizations.of(context)!.reset,
                      style: const TextStyle(
                        fontSize: 18,
                        color: AppColor.redColor,
                      ),
                    ),
                  )
                : Container(),
            TextButton(
              onPressed: () {
                var partsCheckedCount = 0;
                widget.seatDetail.partList.forEach((element) {
                  if (element.isChecked) {
                    partsCheckedCount += 1;
                  }
                });
                if (partsCheckedCount > 0) {
                  Navigator.pop(context);
                } else {
                  ApplicationUtil.showSnackBar(
                      context: context,
                      message: AppLocalizations.of(context)!
                          .selectAtLeastOnePartContinue);
                }
              },
              child: Text(
                AppLocalizations.of(context)!.continueString,
                style: const TextStyle(fontSize: 18),
              ),
            ),
          ],
        ),
      ],
    );
  }

  _disbaleUI(int index) => InkWell(
        onTap: () {},
        child: ListTile(
          contentPadding: EdgeInsets.zero,
          visualDensity: const VisualDensity(horizontal: 0, vertical: -4),
          //leading: Icon(widget.seatItemList[index].icon),
          subtitle: Text(
            widget.seatItemList[index].part1,
            style: const TextStyle(
              color: Colors.grey,
              fontSize: 16,
            ),
          ),
          title: Text(
            widget.seatItemList[index].description,
            style: const TextStyle(
              fontSize: 18,
              color: Colors.grey,
            ),
          ),
          trailing: const Icon(
            Icons.check_box_outline_blank,
            color: Colors.grey,
          ),
        ),
      );

  _checkBoxListTile(int index) {
    if (widget.seatItemList[index].hasServiceRequest) {
      return InkWell(
        onTap: () {},
        child: ListTile(
            contentPadding: EdgeInsets.zero,
            visualDensity: const VisualDensity(horizontal: 0, vertical: -4),
            //leading: Icon(widget.seatItemList[index].icon),
            subtitle: Text(
              widget.seatItemList[index].part1,
              style: const TextStyle(color: Colors.grey, fontSize: 16),
            ),
            title: Text(
              widget.seatItemList[index].description,
              style: const TextStyle(
                fontSize: 18,
                color: Colors.grey,
              ),
            ),
            trailing: const Icon(
              Icons.check_box,
              color: Colors.grey,
            )),
      );
    } else {
      return InkWell(
        onTap: () {
          if (widget.isDirectRepair) {
            if (!widget.seatItemList[index].isChecked) {
              if (checkedCount == 0) {
                widget.seatItemList[index].isChecked = true;
                widget.seatItemList[index].checkedCount++;
                oneItemChecked = true;
                checkedCount++;
                print(
                    "checked count: $checkedCount\noneItemChecked: $oneItemChecked");
              }
            } else {
              if (checkedCount > 0) {
                widget.seatItemList[index].isChecked = false;
                widget.seatItemList[index].checkedCount--;
                oneItemChecked = false;
                checkedCount--;
                print(
                    "checked count: $checkedCount\noneItemChecked: $oneItemChecked");
              }
            }
          } else {
            if (!widget.seatItemList[index].isChecked) {
              widget.seatItemList[index].isChecked = true;
              widget.seatItemList[index].checkedCount++;
            } else {
              widget.seatItemList[index].isChecked = false;
              widget.seatItemList[index].checkedCount--;
            }
          }
          setState(() {});
          //Navigator.pop(context);
        },
        child: ListTile(
          enabled: widget.isDirectRepair
              ? _getEnableStatus(widget.seatItemList[index].isChecked)
              : true,
          contentPadding: EdgeInsets.zero,
          visualDensity: const VisualDensity(horizontal: 0, vertical: -4),
          //leading: Icon(widget.seatItemList[index].icon),
          subtitle: Text(
            widget.seatItemList[index].part1,
            style: _getEnableStatus(widget.seatItemList[index].isChecked)
                ? TextStyle(color: Colors.blue[800], fontSize: 16)
                : const TextStyle(color: Colors.grey, fontSize: 16),
          ),
          title: Text(
            widget.seatItemList[index].description,
            style: const TextStyle(fontSize: 18),
          ),
          trailing: /*widget.seatItemList[index].isChecked
              ? Icon(
                  Icons.check_box,
                  color: Theme.of(context).primaryColor,
                )
              : */
              _getDisabledIcon(widget.seatItemList[index].isChecked),
        ),
      );
    }
  }

  onlyPartNameListTile(int index) => widget.seatItemList[index].isChecked
      ? ListTile(
          contentPadding: EdgeInsets.zero,
          visualDensity: const VisualDensity(horizontal: 0, vertical: -4),
          //leading: Icon(widget.seatItemList[index].icon),
          subtitle: Text(
            widget.seatItemList[index].part1,
            style: TextStyle(color: Colors.blue[800], fontSize: 16),
          ),
          title: Text(
            widget.seatItemList[index].description,
            style: const TextStyle(fontSize: 18),
          ),
        )
      : Container();

  _getEnableStatus(bool isChecked) {
    (oneItemChecked == true && isChecked == true)
        ? true
        : oneItemChecked == false
            ? true
            : false;
    if (oneItemChecked == true && isChecked == true) {
      return true;
    } else if (oneItemChecked == false) {
      return true;
    } else {
      return false;
    }
  }

  _getDisabledIcon(bool isChecked) {
    if (widget.isDirectRepair) {
      if (oneItemChecked == true && isChecked == true) {
        return Icon(
          Icons.check_box,
          color: Theme.of(context).primaryColor,
        );
      } else if (oneItemChecked == false && isChecked == false) {
        return Icon(
          Icons.check_box_outline_blank,
          color: Theme.of(context).primaryColor,
        );
      } else {
        return const Icon(
          Icons.indeterminate_check_box,
          color: Colors.grey,
        );
      }
    } else {
      if (isChecked == true) {
        return Icon(
          Icons.check_box,
          color: Theme.of(context).primaryColor,
        );
      } else if (isChecked == false) {
        return Icon(
          Icons.check_box_outline_blank,
          color: Theme.of(context).primaryColor,
        );
      }
    }
  }
}
