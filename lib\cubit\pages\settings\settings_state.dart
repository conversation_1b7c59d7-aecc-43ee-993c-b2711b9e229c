part of 'settings_cubit.dart';

@immutable
abstract class SettingsState {}

class SettingsInitial extends SettingsState {}

class LogPreferencesUpdating extends SettingsState {}

class CameraSettingUpdated extends SettingsState {
  final String selection;
  CameraSettingUpdated({required this.selection});
}

class LogPreferenceFetching extends SettingsState {}

class LogPreferenceFetched extends SettingsState {
  final String selection;
  LogPreferenceFetched({required this.selection});
}
